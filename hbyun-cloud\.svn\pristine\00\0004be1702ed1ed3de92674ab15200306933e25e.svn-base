package com.huabo.audit.oracle.mapper;

import com.hbfk.util.PageInfo;
import com.huabo.audit.oracle.entity.TblYqnsSjzgWtzg;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description 针对表【TBL_YQNS_SJZG_WTZG(问题整改表)】的数据库操作Mapper
 * @createDate 2023-09-27 16:46:40
 * @Entity TblYqnsSjzgWtzg
 */
public class TblYqnsSjzgWtzgMapperSqlConfig {

    public String selectCountByPageInfo(PageInfo<TblYqnsSjzgWtzg> pageInfo, TblYqnsSjzgWtzg vo) {
        StringBuffer sb = new StringBuffer("SELECT COUNT(0) "
                + " FROM TBL_YQNS_SJZG_WTZG TBL1 "
                + " WHERE 1=1");

        sqlQuery(vo, sb);

        return sb.toString();
    }


    public String selectListByPageInfo(PageInfo<TblYqnsSjzgWtzg> pageInfo, TblYqnsSjzgWtzg vo) {
        StringBuffer sb = new StringBuffer("SELECT * FROM "
                + "(SELECT T1.*,ROWNUM RN  FROM "
                + "(SELECT TBL1.* "
                + "FROM TBL_YQNS_SJZG_WTZG TBL1 "
                + "WHERE 1=1");

        sqlQuery(vo, sb);

        sb.append(" ORDER BY TBL1.WTZGID DESC) T1 WHERE ROWNUM <= " + (pageInfo.getCurrentRecord() + pageInfo.getPageSize()) + " ) T2 WHERE T2.RN > " + pageInfo.getCurrentRecord());
        return sb.toString();
    }

    private void sqlQuery(TblYqnsSjzgWtzg vo, StringBuffer sb) {

        if (StringUtils.isNotEmpty(vo.getReportnum())) {
            sb.append(" AND TBL1.REPORTNUM LIKE '%" + vo.getReportnum() + "%'");
        }

        if (StringUtils.isNotEmpty(vo.getUnitname())) {
            sb.append(" AND TBL1.UNITNAME LIKE '%" + vo.getUnitname() + "%'");
        }

        if (vo.getStartDate() != null && vo.getStartDate().length() > 0) {
            sb.append(" AND TBL1.cjsj >= TO_DATE('" + vo.getStartDate() + " 00:00:00','yyyy-mm-dd HH24:MI:SS')");
        }

        if (vo.getEndDate() != null && vo.getEndDate().length() > 0) {
            sb.append(" AND TBL1.cjsj <= TO_DATE('" + vo.getEndDate() + " 23:59:59','yyyy-mm-dd HH24:MI:SS')");
        }
    }

}




