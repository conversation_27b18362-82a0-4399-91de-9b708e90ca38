package com.huabo.fxgl.entity;
import com.baomidou.mybatisplus.annotation.*;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

/**
 * <p>
 *
 * </p>
 * 评估计划
 * <AUTHOR>
 * @since 2022-08-01
 */
@Data
@ToString
@TableName(value = "TBL_RISK_ASSPLAN", resultMap = "RM_RISK_ASSPLAN")
@KeySequence(value = "SEQ_RISK_CTR", dbType = DbType.ORACLE)
public class RiskAssplan implements Serializable {
    private static final long serialVersionUID = 1L;
    public final static String PLANSTATUS_WKS  = "1";//未开始
    public final static String PLANSTATUS_PGZ  = "2"; //评估中
    public final static String PLANSTATUS_YWC  = "3"; //已完成
    /**
     * ID
     */
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.INPUT)
    private BigDecimal assplanid;
    /**
     * 计划编号
     */
	@ApiModelProperty("计划编号")
    private String plancode;
    /**
     * 计划名称
     */
	@ApiModelProperty("计划名称")
    @TableField("planName")
    private String planName;
    /**
     * 计划开始时间
     */
	@ApiModelProperty("计划开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField("startDate")
    private Date startDate;
    /**
     * 计划结束时间
     */
	@ApiModelProperty("计划结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField("endDate")
    private Date endDate;
    /**
     * 计划状态
     */
	@ApiModelProperty("计划状态  1-未开始 2-评估中 3-已完成")
    @TableField("planStatus")
    private String planStatus;
    /**
     * 公司ID
     */
	@ApiModelProperty("公司ID")
    @TableField(value = "UNIT", property = "organization.orgid",exist = false)
    private Organization organization;

	@ApiModelProperty("")
    private String unit;
    /**
     * 备注
     */
	@ApiModelProperty("备注")
    private String memo;
    /**
     * 计划描述
     */
	@ApiModelProperty("计划描述")
    private String plandes;
    /**
     * 计划类型
     */
	@ApiModelProperty("计划类型 1-年度计划  2-临时性计划")
    @TableField("planType")
    private String planType;
	
	
    private String recorder;
    /**
     * 创建时间
     */
	@ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime recorddate;
    private String asshead;
    /**
     * 评估标准ID
     */
	@ApiModelProperty("评估标准ID")
    @TableField(value = "assstdid", property = "assessmentstd.assstdid")
    private RiskAssessmentstd assessmentstd;

	@ApiModelProperty("")
    private String version;

	@ApiModelProperty("一个评估计划可能映射多个风险")
    @TableField(exist = false)
    private List<RiskAssplanRisk> riskAssplanRiskList;//一个评估计划可能映射多个风险

    @TableField(exist = false)
    private Set<Attachment> tblAttachments = new HashSet<Attachment>();

//    @TableField(exist = false)
//    private Set<RiskAssplanRisk> assPlanRisks = new TreeSet<RiskAssplanRisk>();



    @TableField(exist = false)
    private Integer count = 0;
    @TableField(exist = false)
    private Integer count1 = 0;
    @TableField(exist = false)
    private Integer count2= 0;
    @TableField(exist = false)
    private Integer count3= 0;
    @TableField(exist = false)
    private Integer count4= 0;
    @TableField(exist = false)
    private Integer count5= 0;

	@ApiModelProperty("")
    @TableField(exist = false)
    private String status;
	
	
	@ApiModelProperty("已评估字段")
    @TableField(exist = false)
	private String ypgStr;
	
	@ApiModelProperty("未评估字段")
    @TableField(exist = false)
	private String wpgStr;

}
