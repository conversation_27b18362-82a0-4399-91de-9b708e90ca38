package com.huabo.monitor.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huabo.monitor.entity.TblAssesstemple;
import com.huabo.monitor.mapper.TblAssesstempleMapper;
import com.huabo.monitor.service.ITblAssesstempleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huabo.monitor.util.ConstClass;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-26
 */
@Service
public class TblAssesstempleServiceImpl extends ServiceImpl<TblAssesstempleMapper, TblAssesstemple> implements ITblAssesstempleService {

    @Resource
    TblAssesstempleMapper tblAssesstempleMapper;

    @Override
    public IPage<TblAssesstemple> findAll(String orgid, Integer pageNumber, TblAssesstemple assesstemple) {
        IPage<TblAssesstemple> page = new Page<>(pageNumber, ConstClass.DEFAULT_SIZE);
        String hql = "select * from TblAssesstemple t where 1=1";
        if (StringUtils.isNotBlank(assesstemple.getTemplename())) {
            hql += " and t.templename like '%" + assesstemple.getTemplename() + "%'";
        }
        if (StringUtils.isNotBlank(assesstemple.getTempleNumber())) {
            hql += " and t.templeNumber like '%" + assesstemple.getTempleNumber() + "%'";
        }
        hql += " order by t.asstemid desc";
        return tblAssesstempleMapper.getSqlPage(page, hql);
    }

    @Override
    public List<TblAssesstemple> getTmplByNumber(String number, BigDecimal orgid) {
        return tblAssesstempleMapper.getTmplByNumber(number, orgid);
    }

    @Override
    public Serializable add(TblAssesstemple tblAssesstemple) {
        int insert = tblAssesstempleMapper.insert(tblAssesstemple);
        return insert;
    }

    @Override
    public void modify(TblAssesstemple tblAssesstemple) {
        tblAssesstempleMapper.updateById(tblAssesstemple);
    }

    @Override
    public TblAssesstemple findByid(BigDecimal id) {
        return tblAssesstempleMapper.findById(id);
    }

    @Override
    public void delete(TblAssesstemple tblAssesstemple) {
        tblAssesstempleMapper.deleteById(tblAssesstemple);
    }
}
