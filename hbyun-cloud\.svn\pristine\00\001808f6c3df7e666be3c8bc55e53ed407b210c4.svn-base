package com.huabo.monitor.mysql.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("TBL_ATTACHMENT")
@ApiModel(value = "TblAttachmentMySql对象", description = "")
public class TblAttachmentMySql implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键Id 自增")
    @TableId("ATTID")
    private BigDecimal attid;

    @ApiModelProperty(value = "附件名称")
    @TableField("ATTNAME")
    private String attname;

    @ApiModelProperty(value = "附件路径")
    @TableField("ATTPATH")
    private String attpath;

    @ApiModelProperty(value = "附件大小")
    @TableField("ATTSIZE")
    private BigDecimal attsize;

    @ApiModelProperty(value = "备注")
    @TableField("MEMO")
    private String memo;

    @ApiModelProperty(value = "上传时间")
    @TableField("UPLOADTIME")
    private LocalDateTime uploadtime;

    @ApiModelProperty(value = "上传人")
    @TableField("UPLOADER")
    private String uploader;

    @ApiModelProperty(value = "是否是python爬取文件 0是")
    @TableField("ISPYTHONFLAG")
    private String ispythonflag;


}
