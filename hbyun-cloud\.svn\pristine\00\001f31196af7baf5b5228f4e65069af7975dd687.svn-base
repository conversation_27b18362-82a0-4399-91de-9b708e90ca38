package com.huabo.know.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("TBL_ZSGX_REVIEW")
@ApiModel(value="知识共享-检查清单对象", description="")
public class TblzsgxReview implements Serializable {

	private static final long serialVersionUID = 1L;
	
	@TableId(value = "ID", type= IdType.INPUT)
    @ApiModelProperty(value="主键ID")
	private String id;
	
	@ApiModelProperty(value = "名称")
    @TableField("NAME")
	private String name;
	
	@ApiModelProperty(value = "编号")
    @TableField("CODE")
	private String code;
	
	@ApiModelProperty(value = "类型")
    @TableField("TYPE")
	private String type;
	
	@ApiModelProperty(value = "排序")
    @TableField("SORT")
	private Integer sort;
	
	@ApiModelProperty(value = "选中")
    @TableField("SELECTED")
	private Integer selected;
	
	@ApiModelProperty(value = "创建公司")
    @TableField("CREATE_COMPANY")
	private String createCompany;

	@ApiModelProperty(value = "创建部门")
	@TableField("CREATE_DEPT")
	private String createDept;

	@ApiModelProperty(value = "创建人")
	@TableField("CREATE_BY")
	private String createBy;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(value = "创建时间")
	@TableField("CREATE_TIME")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@ApiModelProperty(value = "修改时间")
    @TableField("UPDATE_TIME")
	private Date updateTime;
	
	@ApiModelProperty(value = "是否删除")
    @TableField("DELETED")
	private Integer deleted;
	
}
