package com.huabo.contract.service.impl;


import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblStaffUtil;
import com.hbfk.util.PageInfo;
import com.huabo.contract.config.DateBaseConfig;
import com.huabo.contract.oracle.entity.TblYyPrice;
import com.huabo.contract.oracle.entity.TblYyReportModel;
import com.huabo.contract.oracle.entity.TblYyXdfCompany;
import com.huabo.contract.oracle.mapper.TblBiPageMapper;
import com.huabo.contract.oracle.mapper.TblYyPriceMapper;
import com.huabo.contract.oracle.mapper.TblYyReportModelMapper;
import com.huabo.contract.oracle.mapper.TblYyXdfCompanyMapper;
import com.huabo.contract.service.TblyyxdfCompanyService;

@Service
public class TblyyxdfCompanyServiceImpl implements TblyyxdfCompanyService {

    @Resource
    private TblYyXdfCompanyMapper tblYyXdfCompanyMapper;

    @Resource
    private TblYyPriceMapper tblYyPriceMapper;
    @Resource
    private TblYyReportModelMapper tblYyReportModelMapper;
    @Resource
    private TblBiPageMapper tblBiPageMapper;



    // 定义一个名为findByCompay的方法，返回一个Map<String, Object>类型的结果
    @Override
    public Map<String, Object> findByCompay(Integer pageNumber, Integer pageSize, String teamid, String fxtype, String companyname, String token) {
        if(DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            try {
                TblStaffUtil staff = DealUserToken.parseUserToken(token);
                if (staff == null) {
                    // 如果用户令牌无效，设置结果码和消息，然后返回结果
                    resultMap.put("code", "0");
                    resultMap.put("msg", "用户已失效！");
                    return resultMap;
                }
                // 设置pageNumber和pageSize的默认值
                if (pageNumber == null) {
                    pageNumber = 1;
                }
                if (pageSize == null) {
                    pageSize = 15;
                }
                // 创建一个PageInfo对象，用于存储分页信息和查询结果
                PageInfo<TblYyXdfCompany> pageInfo = new PageInfo<TblYyXdfCompany>();
                if (teamid != null && teamid.trim().length() > 0) {
                    // 创建一个公司对象，设置查询条件
                    TblYyXdfCompany company = new TblYyXdfCompany();
                    company.setCompanyname(companyname);
                    company.setFxtype(fxtype);
                    company.setTeamid(new BigDecimal(teamid));
                    company.setStaffid(staff.getStaffid());
                    // 设置分页信息和查询条件
                    pageInfo.setCurrentPage(pageNumber);
                    pageInfo.setPageSize(pageSize);
                    pageInfo.setCondition(company);
                    // 执行查询，并将结果设置到pageInfo对象中
                    pageInfo.setTlist(tblYyXdfCompanyMapper.selectListByPageInfo(pageInfo));
                    pageInfo.setTotalRecord(tblYyXdfCompanyMapper.selectCountByPageInfo(pageInfo));
                    // 处理查询结果，为每个公司对象设置价格列表
                    List<TblYyXdfCompany> list = pageInfo.getTlist();
                    List<TblYyXdfCompany> newlist = new ArrayList<TblYyXdfCompany>();
                    if (list != null && list.size() > 0) {
                        for (TblYyXdfCompany tblyyCompany : list) {
                            List<TblYyPrice> list2 = tblYyPriceMapper.findByIs(tblyyCompany.getReportid());
                            tblyyCompany.setList(list2);
                            newlist.add(tblyyCompany);
                        }
                        pageInfo.setTlist(newlist);
                    }
                } else {
                    // 如果teamid不存在，执行另一种查询逻辑
                    TblYyXdfCompany company = new TblYyXdfCompany();
                    company.setCompanyname(companyname);
                    company.setFxtype(fxtype);
                    company.setStaffid(staff.getStaffid());
                    company.setOrgid(staff.getCurrentOrg().getOrgid());
                    pageInfo.setCurrentPage(pageNumber);
                    pageInfo.setPageSize(pageSize);
                    pageInfo.setCondition(company);
                    pageInfo.setTlist(tblYyXdfCompanyMapper.findBySqlPage(pageInfo));
                    pageInfo.setTotalRecord(tblYyXdfCompanyMapper.findCountBySqlPage(pageInfo));
                    List<TblYyXdfCompany> list = pageInfo.getTlist();
                    List<TblYyXdfCompany> newlist = new ArrayList<TblYyXdfCompany>();
                    if (list != null && list.size() > 0) {
                        for (TblYyXdfCompany tblyyCompany : list) {
                            List<TblYyPrice> list2 = tblYyPriceMapper.findByIs(tblyyCompany.getReportid());
                            tblyyCompany.setList(list2);
                            newlist.add(tblyyCompany);
                        }
                        pageInfo.setTlist(newlist);
                    }
                }
                resultMap.put("code", "1");
                resultMap.put("msg", "访问接口成功");
                resultMap.put("data", pageInfo);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return resultMap;
        } else {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            try {
                TblStaffUtil staff = DealUserToken.parseUserToken(token);
                if (staff == null) {
                    resultMap.put("code", "0");
                    resultMap.put("msg", "用户已失效！");
                    return resultMap;
                }
                if (pageNumber == null) {
                    pageNumber = 1;
                }
                if (pageSize == null) {
                    pageSize = 15;
                }
                PageInfo<TblYyXdfCompany> pageInfo = new PageInfo<TblYyXdfCompany>();
                if (teamid != null && teamid.trim().length() > 0) {
                    TblYyXdfCompany company = new TblYyXdfCompany();
                    company.setCompanyname(companyname);
                    company.setFxtype(fxtype);
                    company.setTeamid(new BigDecimal(teamid));
                    company.setStaffid(staff.getStaffid());
                    pageInfo.setCurrentPage(pageNumber);
                    pageInfo.setPageSize(pageSize);
                    pageInfo.setCondition(company);
                    pageInfo.setTlist(tblYyXdfCompanyMapper.selectListByPageInfo(pageInfo));
                    pageInfo.setTotalRecord(tblYyXdfCompanyMapper.selectCountByPageInfo(pageInfo));
                    List<TblYyXdfCompany> list = pageInfo.getTlist();
                    List<TblYyXdfCompany> newlist = new ArrayList<TblYyXdfCompany>();
                    if (list != null && list.size() > 0) {
                        for (TblYyXdfCompany tblyyCompany : list) {
                            List<TblYyPrice> list2 = tblYyPriceMapper.findByIs(tblyyCompany.getReportid());
                            tblyyCompany.setList(list2);
                            newlist.add(tblyyCompany);
                        }
                        pageInfo.setTlist(newlist);
                    }
                } else {
                    TblYyXdfCompany company = new TblYyXdfCompany();
                    company.setCompanyname(companyname);
                    company.setFxtype(fxtype);
                    company.setStaffid(staff.getStaffid());
                    company.setOrgid(staff.getCurrentOrg().getOrgid());
                    pageInfo.setCurrentPage(pageNumber);
                    pageInfo.setPageSize(pageSize);
                    pageInfo.setCondition(company);
                    pageInfo.setTlist(tblYyXdfCompanyMapper.findBySqlPage(pageInfo));
                    pageInfo.setTotalRecord(tblYyXdfCompanyMapper.findCountBySqlPage(pageInfo));
                    List<TblYyXdfCompany> list = pageInfo.getTlist();
                    List<TblYyXdfCompany> newlist = new ArrayList<TblYyXdfCompany>();
                    if (list != null && list.size() > 0) {
                        for (TblYyXdfCompany tblyyCompany : list) {
                            List<TblYyPrice> list2 = tblYyPriceMapper.findByIs(tblyyCompany.getReportid());
                            tblyyCompany.setList(list2);
                            newlist.add(tblyyCompany);
                        }
                        pageInfo.setTlist(newlist);
                    }
                }
                resultMap.put("code", "1");
                resultMap.put("msg", "访问接口成功");
                resultMap.put("data", pageInfo);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return resultMap;
        }
    }

    @Override
    public Map<String, Object> saveOrupdateYYCompany(String pageid, String priceid, String token, TblYyXdfCompany company) {
        if(DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            try {
                TblStaffUtil staff = DealUserToken.parseUserToken(token);
                if (staff == null) {
                    resultMap.put("code", "0");
                    resultMap.put("msg", "用户已失效！");
                    return resultMap;
                }
                // 创建报告模型
                TblYyReportModel model = new TblYyReportModel();
                model.setOrgid(staff.getCurrentOrg().getOrgid());
                model.setPriceid(priceid);
                model.setStaffid(staff.getStaffid());
                model.setReportname(company.getCompanyname());
                // 保存报告模型
                tblYyReportModelMapper.saveModel(model);
//            Set<TblBiPage> pagess =new HashSet<>();
//            if(pageid!=null && pageid.length()>0) {
//                List<TblBiPage> pageChilds=tblBiPageMapper.findBypageid(pageid);
//                pagess =new HashSet<>(pageChilds);
//            }

                // 设置公司信息
                company.setReportid(model.getReportid());
                company.setOrgid(staff.getCurrentOrg().getOrgid());
                company.setStaffid(staff.getStaffid());
                company.setCreatedate(new Date());
                // company.setPages(pagess);
                // 保存公司信息
                tblYyXdfCompanyMapper.insertCompany(company);
                resultMap.put("code", "1");
                resultMap.put("msg", "访问接口成功");
                resultMap.put("data", company);
            } catch (Exception e) {
                e.printStackTrace();
            }

            return resultMap;
        } else {
            // 如果数据库类型不是 Oracle，执行相同的操作
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            try {
                TblStaffUtil staff = DealUserToken.parseUserToken(token);
                if (staff == null) {
                    resultMap.put("code", "0");
                    resultMap.put("msg", "用户已失效！");
                    return resultMap;
                }
                TblYyReportModel model = new TblYyReportModel();
                model.setOrgid(staff.getCurrentOrg().getOrgid());
                model.setPriceid(priceid);
                model.setStaffid(staff.getStaffid());
                model.setReportname(company.getCompanyname());
                tblYyReportModelMapper.saveModel(model);
//            Set<TblBiPage> pagess =new HashSet<>();
//            if(pageid!=null && pageid.length()>0) {
//                List<TblBiPage> pageChilds=tblBiPageMapper.findBypageid(pageid);
//                pagess =new HashSet<>(pageChilds);
//            }

                company.setReportid(model.getReportid());
                company.setOrgid(staff.getCurrentOrg().getOrgid());
                company.setStaffid(staff.getStaffid());
                company.setCreatedate(new Date());
                // company.setPages(pagess);
                tblYyXdfCompanyMapper.insertCompany(company);
                resultMap.put("code", "1");
                resultMap.put("msg", "访问接口成功");
                resultMap.put("data", company);
            } catch (Exception e) {
                e.printStackTrace();
            }

            return resultMap;
        }
    }
}
