package com.huabo.contract.service;

import com.hbfk.util.PageInfo;
import com.huabo.contract.oracle.entity.TblOrgBankaccount;

import java.math.BigDecimal;

public interface TblOrgBankAccountService {
    void findListByPageInfo(PageInfo<TblOrgBankaccount> pageInfo, TblOrgBankaccount bank);

    void findListPageInfo(PageInfo<TblOrgBankaccount> pageInfo, TblOrgBankaccount bank);

    TblOrgBankaccount findById(Integer bankId);

    void findListByPageInfoPid(PageInfo<TblOrgBankaccount> pageInfo, BigDecimal pid,TblOrgBankaccount bank);

    TblOrgBankaccount findByBankId(BigDecimal bankid);

    void UpdateModifyBankInfo(TblOrgBankaccount oldEntity);

    void savebankInfo(TblOrgBankaccount bank);

    String removeOrgBankInfo(Integer bankId);
}
