package com.huabo.system.controller;

import com.google.gson.Gson;
import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblStaffUtil;
import com.hbfk.util.JsonBean;
import com.huabo.system.exception.ServiceException;
import com.huabo.system.service.business.FileUploadService;
import com.huabo.system.utils.TokenUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

@RestController
@RequestMapping("/fileManage")
@Api(tags = {"附件上传接口"})
@Slf4j
public class FileUploadController {

	@Resource
	private FileUploadService fileUploadService;

	@Value("${application.file-url:}")
	private String fileUrl;

	@PostMapping("/upload")
	@ApiOperation("文件上传接口")
	public JsonBean fileUpload(@ApiParam(name = "file", value = "附件上传", required = true) @RequestPart("file") MultipartFile[] file,
			@RequestHeader("token") String token) throws Exception {
		TblStaffUtil loginStaff = DealUserToken.parseUserToken(token);
		if (loginStaff == null) {
			throw new ServiceException(401, 20006);
		}
		JsonBean jsonBean = null;
		try {
			jsonBean = fileUploadService.fileUpload(file, loginStaff.getRealname());
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("通用文件上传 ...接口 异常", e);
		}
		return jsonBean;
	}

	@GetMapping("/getPrivewAttInfo")
	@ApiOperation("获取上传的url")
	public JsonBean getPrivewAttInfo(HttpServletResponse response,
			@ApiParam(name = "fileId", value = "文件主键ID", required = true) @RequestParam("fileId") String fileId,
			@RequestHeader("token") String token) {
		JsonBean jsonBean = null;
		try {
			TokenUtils.getUserInfo(token);
			jsonBean = fileUploadService.getPrivewAttInfo(response, Integer.parseInt(fileId));
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("获取上传的url ...接口 异常", e);
		}
		return jsonBean;
	}

	@GetMapping("/download")
	@ApiOperation("文件下载接口")
	public JsonBean fileDownLoad(HttpServletResponse response,
			@ApiParam(name = "fileId", value = "文件主键ID", required = true) @RequestParam("fileId") String fileId,
			@RequestHeader("token") String token) {
		try {
			TokenUtils.getUserInfo(token);
			fileUploadService.fileDownLoad(response, Integer.parseInt(fileId));
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("文件下载接口 ...接口 异常", e);
		}
		return null;
	}

	@ApiOperation("附件删除接口，不删除中间表关系")
	@DeleteMapping("/{id}")
	public JsonBean fileRemove(@PathVariable("id") Integer id, @RequestHeader("token") String token) {
		JsonBean jsonBean = null;
		try {
			TokenUtils.getUserInfo(token);
			jsonBean = fileUploadService.fileRemove(id);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("文件下载接口 ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("本地图片上传")
	@PostMapping("/upload/image")
	public void uploadPicture(@RequestParam(value = "file", required = false) MultipartFile file, HttpServletResponse response) {
		JsonBean jsonBean = new JsonBean();
		String url = "";//返回存储路径
		System.out.println(file);
		String fileName = file.getOriginalFilename();//获取文件名加后缀
		if (fileName != null && fileName != "") {
			//获取项目路径
			String path = System.getProperty("user.dir") + "/upload/imgs"; //文件存储位置
			log.info("本地图片上传 获取项目路径:{}", path);
			String fileF = fileName.substring(fileName.lastIndexOf("."), fileName.length());//文件后缀
			fileName = new Date().getTime() + "_" + new Random().nextInt(1000) + fileF;//新的文件名
			//先判断文件是否存在
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			String fileAdd = sdf.format(new Date());
			//获取文件夹路径
			File file1 = new File(path + "/" + fileAdd);
			//如果文件夹不存在则创建
			if (!file1.exists() && !file1.isDirectory()) {
				file1.getParentFile().mkdirs();
			}
			//将图片存入文件夹
			File targetFile = new File(file1, fileName);
			try {
				//将上传的文件写到服务器上指定的文件。
				FileUtils.copyInputStreamToFile(file.getInputStream(), targetFile);
				url = fileUrl + "/localUpload/imgs/" + fileAdd + "/" + fileName;
				jsonBean.setCode(200);
				jsonBean.setMsg("图片上传成功");
				jsonBean.setData(url);
			} catch (Exception e) {
				log.error("系统异常，图片上传失败", e);
				jsonBean.setCode(400);
				jsonBean.setMsg("系统异常，图片上传失败");
			}
		}
		writeJson(response, jsonBean);
	}

	@ApiOperation("本地图片上传-删除")
	@DeleteMapping("/upload/image")
	public void deletePicture(String url) {
		fileUploadService.deletePicture(url);
	}

	/**
	 * 输出JSON数据
	 *
	 * @param response
	 * @param obj
	 */
	public void writeJson(HttpServletResponse response, Object obj) {
		response.setContentType("text/json;charset=utf-8");
		response.setHeader("Pragma", "No-cache");
		response.setHeader("Cache-Control", "no-cache");
		response.setDateHeader("Expires", 0);
		PrintWriter pw = null;
		Gson gson = new Gson();
		try {
			pw = response.getWriter();
			pw.write(gson.toJson(obj));

			pw.flush();
		} catch (Exception e) {
			log.info("输出JSON数据异常", e);
		} finally {
			if (pw != null) {
				pw.close();
			}
		}
	}
}
