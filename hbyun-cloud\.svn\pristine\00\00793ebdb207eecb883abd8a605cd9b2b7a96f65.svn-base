package com.huabo.audit.oracle.vo;

import com.huabo.audit.util.BaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName ProjectStatusVo
 * @Description 项目任务分配查询入参
 * <AUTHOR>
 * @Date 2022/4/12 19:19
 * @Version 1.0
 */
@Data
@ApiModel("项目任务分配查询入参")
public class ProjectTaskVo extends BaseVo {
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "项目对象")
    private String projectObj;

    @ApiModelProperty(value="审批状态 1：未审批 2：审批中 3：审批驳回 4：审批通过",hidden = true)
    private Integer examineType;

    @ApiModelProperty(value = "组织id", hidden = true)
    private BigDecimal orgId;

    @ApiModelProperty(value = "用户登录的token")
    private String token;
}
