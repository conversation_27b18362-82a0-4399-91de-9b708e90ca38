package com.huabo.monitor.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Table(name = "TBL_NBSJ_BUG")
@Data
@ApiModel("缺陷实体类")
@Accessors(chain = true)
public class TblNbsjBugEntity extends FlexibleFieldEntity {
	

	@ApiModelProperty(value = "主键")
	@Id
	//@GeneratedValue(strategy = GenerationType.IDENTITY,generator = "select HIBERNATE_SEQUENCE.nextval from dual")
	@TableId("bugid")
	@TableField("bugid")
	@Column(name = "bugid")
	private BigDecimal bugid;
	
	@TableField(value = "bugnumber")
	@Column(name = "bugnumber")
	@ApiModelProperty(value="缺陷编号")
	private String bugnumber;
	
	@TableField(value = "bugdescripte")
	@Column(name = "bugdescripte")
	@ApiModelProperty(value="缺陷描述")
	private String bugdescripte;
	
	@TableField(value = "discovertime")
	@Column(name = "discovertime")
	@ApiModelProperty(value="发现日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd", iso = DateTimeFormat.ISO.DATE_TIME)
	private Date discovertime;
	
	@TableField(value = "discoverperson")
	@Column(name = "discoverperson")
	@ApiModelProperty(value="发现人")
	private String discoverperson;
	
	@TableField(value = "BUGPROPERTY")
	@Column(name = "BUGPROPERTY")
	@ApiModelProperty(value="缺陷性质")
	private String bugproperty;
	
	@TableField(value = "bugsource")
	@Column(name = "bugsource")
	@ApiModelProperty(value="是否财务相关")
	private String bugsource;
	
	@TableField(value = "bugdepartment")
	@Column(name = "bugdepartment")
	@ApiModelProperty(value="缺陷部门|创建公司")
	private String bugdepartment;
	
	@TableField(value = "needreform")
	@Column(name = "needreform")
	@ApiModelProperty(value="是否需要整改")
	private String needreform;
	
	@TableField(value = "bugreformstatus")
	@Column(name = "bugreformstatus")
	@ApiModelProperty(value="")
	private String bugreformstatus;
	
	@TableField(value = "projectname")
	@Column(name = "projectname")
	@ApiModelProperty(value="")
	private String projectname;
	
	@TableField(value = "memo")
	@Column(name = "memo")
	@ApiModelProperty(value="")
	private String memo;
	
	@TableField(value = "PROJECTID")
	@Column(name = "PROJECTID")
	@ApiModelProperty(value="关联项目Id")
	private BigDecimal projectId;
	
	@TableField(value = "fatherbugid")
	@Column(name = "fatherbugid")
	@ApiModelProperty(value="")
	private BigDecimal fatherbugid;
	
	@TableField(value = "bugbysystem")
	@Column(name = "bugbysystem")
	@ApiModelProperty(value="")
	private String bugbysystem;
	
	@Transient
	@TableField(exist = false)
	@Column(name = "tblInnerrules")
	@ApiModelProperty(value="", hidden = true)
	
	private Set tblInnerrules;
	
	@Transient
	@TableField(exist = false)
	@Column(name = "tblAttachments")
	@ApiModelProperty(value="附件", hidden = true)
	
	private Set tblAttachments;
	
	@Transient
	@TableField(exist = false)
	@Column(name = "tblOuterrules")
	@ApiModelProperty(value="", hidden = true)
	
	private Set tblOuterrules;
	
	@Transient
	@TableField(exist = false)
	@ApiModelProperty(value="整改追踪", hidden = true)
	
	private Set tblTracingresponsibilities;
	
	@Transient
	@TableField(exist = false)
	@ApiModelProperty(value="", hidden = true)
	
	private Set tblReforms;
	
	@Transient
	@TableField(exist = false)
	@ApiModelProperty(value="", hidden = true)
	
	private Set<TblNbsjBugEntity> children;
	
	@Transient
	@TableField(exist = false)
	@ApiModelProperty(value="", hidden = true)
	
	private Set<TblAssessTarget> tblproblemTargets;
	
	@TableField(value = "businessDescription")
	@Column(name = "businessDescription")
	@ApiModelProperty(value="业务描述")
	private String businessDescription;
	
	@Transient
	@TableField(exist = false)
	@ApiModelProperty(value="", hidden = true)
	private TblNbsjProject relatedProject;
	
	@Transient
	@TableField(exist = false)
	@ApiModelProperty(value="", hidden = true)
	private String bugcrilevel;
	
	 @TableField("BUSINESSTYPE")
	 @Column(name = "BUSINESSTYPE")
	 @ApiModelProperty(value="业务单元")
	 private String businessType;
	@Transient
	@TableField(exist = false)
	@ApiModelProperty(value="")
	private BigDecimal bugcriid;
	@Transient
	@TableField(exist = false)
	@ApiModelProperty(value="", hidden = true)
	private String orgname;
	
	@TableField(value = "RESONFORNOREFORM")
	@Column(name = "RESONFORNOREFORM")
	@ApiModelProperty(value="")
	private String resonfornoreform;
	
	
	//------中核新增
	 @ApiModelProperty("缺陷名称")
	    @TableField("DEFECTSNAME")
	    private String defectsname;
	
	 @ApiModelProperty("涉及金额(万元)")
	    @TableField("AMOUNT")
	    private BigDecimal amount ;
	    
	    @ApiModelProperty("原因分析")
	    @TableField("CAUSEANALYSIS")
	    private String causeanalysis;
	    
	    @ApiModelProperty("缺陷类别")
	    @TableField("DEFECTCATEGORY")
	    private String defectcategory ;
	    
	    @ApiModelProperty("缺陷种类")
	    @TableField("DEFECTTYPE")
	    private String defecttype ;
		
	    @ApiModelProperty("是否涉诉")
	    @TableField("LITIGATION")
	    private String litigation ;
	    
	    @ApiModelProperty("是否境外")
	    @TableField("OVERSEAS")
	    private String overseas ;
	    
	    @ApiModelProperty("创建人")
	    @TableField("CREATESTAFFID")
	    private BigDecimal createstaffid;
	    
	    @TableField("CREATETIME")
	 	@ApiModelProperty(value = "创建时间")
	    @JsonFormat(pattern = "yyyy-MM-dd")
	   	@DateTimeFormat(pattern = "yyyy-MM-dd")
	     private Date createtime;

	    @ApiModelProperty("关联公司")
	    @TableField("UNIT")
	    private BigDecimal unit;
	    
	    @ApiModelProperty("审批状态")
	    @TableField("STATUS")
	    private Integer status;
	 
	    @ApiModelProperty(value = "密级主键")
	    @TableField("SECRECTLEVELID")
	    @Column(name = "SECRECTLEVELID")
	    private BigDecimal secrectLevelId;
	    
	    @ApiModelProperty(value = "知悉范围 多个逗号分隔")
	    @TableField("STAFFSCOPEIDS")
	    @Column(name = "STAFFSCOPEIDS")
	    private String staffScopeIds;
	    
	    @ApiModelProperty(value = "知悉访问人员姓名 多个逗号分隔")
	    @TableField("STAFFSCOPENAMES")
	    @Column(name = "STAFFSCOPENAMES")
	    private String staffScopeNames;
	    @ApiModelProperty(value = "所属部门")
	    @TableField("LINKDEPTID")
	    @Column(name = "LINKDEPTID")
	  private BigDecimal linkdeptid;
	    
	    
}
