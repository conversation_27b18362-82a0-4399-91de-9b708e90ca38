package com.huabo.audit.controller;

import com.hbfk.util.*;
import com.huabo.audit.oracle.entity.RequireSuggestionEntity;
import com.huabo.audit.service.RequireSuggestionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.bouncycastle.cert.ocsp.Req;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName RequireSuggestionController
 * @Description 计划管理-需求建议
 * @DATE 2023/9/6
 */
@RestController
@Slf4j
@Api(value = "计划管理-需求建议", tags = {"计划管理-需求建议所有接口"})
@RequestMapping(value = "/plan/require/suggestion")
public class RequireSuggestionController {
    @Autowired
    private RequireSuggestionService requireSuggestionService;

    @GetMapping("/getList")
    @ApiOperation("需求建议列表")
    public JsonBean getList(HttpServletRequest request,
                            @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token")String token,
                            @ApiParam(name="pageNumber",value="分页当前页数",required=false)@RequestParam(value = "pageNumber", required = false, defaultValue = "1") Integer pageNumber,
                            @ApiParam(name="pageSize",value="每页记录数",required=false)@RequestParam(value = "pageSize", required = false, defaultValue = "15") Integer pageSize,
                            @ApiParam(name="id",value="序号",required = false)@RequestParam(value = "id", required = false, defaultValue = "") String id,
                            @ApiParam(name="concerns",value="重点关注领域、项目、事项和风险",required = false)@RequestParam(value = "concerns", required = false, defaultValue = "") String concerns
    ){
        JsonBean jsonBean = null;
        try{
            jsonBean = requireSuggestionService.findAll(token,pageNumber,pageSize,id,concerns);
        }catch (Exception e){
            e.printStackTrace();
            jsonBean= ResponseFormat.retParam(0,e.getMessage(),null);
        }
        return jsonBean;
    }

    @GetMapping("/detail")
    @ApiOperation("根据ID获取需求建议详细信息")
    public JsonBean getById(HttpServletRequest request,@ApiParam(name="id",value="序号",required = false)@RequestParam(value = "id", required = false, defaultValue = "") String id){
        JsonBean jsonBean = null;
        try{
            jsonBean = requireSuggestionService.findById(id);
        }catch (Exception e){
            e.printStackTrace();
            jsonBean= ResponseFormat.retParam(0,e.getMessage(),null);
        }
        return jsonBean;
    }

    @PostMapping(value="/saveOrUpdate" , produces = "application/json; charset=utf-8")
    @ApiOperation("添加或修改信息")
    public JsonBean saveOrUpdate(HttpServletRequest request,
                                 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token")String token,
                                 @ApiParam(name="rs", value="实体", required = true) @RequestBody RequireSuggestionEntity requireSuggestionEntity
    ){
        JsonBean jsonBean = null;
        try{
            if(null != requireSuggestionEntity.getId()){
                requireSuggestionService.updateEntity(requireSuggestionEntity);
            }else{
                requireSuggestionService.saveEntity(token,requireSuggestionEntity);
            }
            jsonBean= ResponseFormat.retParam(1,200,null);
        }catch (Exception e){
            e.printStackTrace();
            jsonBean= ResponseFormat.retParam(0,e.getMessage(),null);
        }
        return jsonBean;
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除")
    public JsonBean deleteByIds(HttpServletRequest request,
                                @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token")String token,
                                @ApiParam(name="ids", value="Id,多个用,号隔开",required = true) @RequestParam String ids
    ){
        JsonBean jsonBean = null;
        try{
            requireSuggestionService.deleteByIds(ids);
            jsonBean= ResponseFormat.retParam(1,200,null);
        }catch (Exception e){
            e.printStackTrace();
            jsonBean= ResponseFormat.retParam(0,e.getMessage(),null);
        }
        return jsonBean;
    }

    @GetMapping("/export")
    @ApiOperation("列表导出")
    public void exportList( HttpServletResponse response,
                           @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token")String token,
                           @ApiParam(name="pageNumber",value="分页当前页数",required=false)@RequestParam(value = "pageNumber", required = false, defaultValue = "1") Integer pageNumber,
                           @ApiParam(name="pageSize",value="每页记录数",required=false)@RequestParam(value = "pageSize", required = false, defaultValue = "15") Integer pageSize,
                           @ApiParam(name="id",value="序号",required = false)@RequestParam(value = "id", required = false, defaultValue = "") String id,
                           @ApiParam(name="concerns",value="重点关注领域、项目、事项和风险",required = false)@RequestParam(value = "concerns", required = false, defaultValue = "")String concerns){
        log.info("需求建议列表导出Excel");
        response.setContentType("application/binary;charset=UTF-8");
        try{
            String date = String.valueOf(System.currentTimeMillis());
            String fileName = "需求建议列表" + "_" + date + ".xlsx";
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            ServletOutputStream outputStream = response.getOutputStream();

            JsonBean jsonBean = requireSuggestionService.findAll(token, pageNumber, pageSize, id, concerns);
            List<RequireSuggestionEntity> list = ((PageInfo) jsonBean.getData()).getTlist();
            List<Object[]> exportList = new ArrayList<>(list.size());
            String[] titles = {"序号", "重点关注领域、项目、事项或风险", "关注内容", "项目类型", "备注", "底稿编号"};
            for (int i = 0; i < list.size(); i++) {
                RequireSuggestionEntity r = list.get(i);
                Object[] o = {i+1,r.getConcerns(),r.getConcernsContent(),r.getProjectType(),r.getRemark(),r.getDraftId()};
                exportList.add(o);
            }

            ImportOrExportExcelUtil.exportExcel(titles, exportList, outputStream, null);
        }catch (Exception e){
            e.printStackTrace();
            log.error("需求建议列表导出失败",e );
        }
    }

    @PostMapping("/import")
    @ApiOperation("列表导入")
    public JsonBean importList(HttpServletRequest request, @ApiParam(name = "file", value = "导入的文件", required = true) MultipartFile file,
                               @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token
    ) throws IOException {
        InputStream in = request.getInputStream();
        XSSFWorkbook workBook = new XSSFWorkbook(in);
        try{

            for (int i =0; i < workBook.getNumberOfSheets(); i++){
                XSSFSheet sheet = workBook.getSheetAt(i);
                requireSuggestionService.resolveSheet(sheet,token);
            }
            return ResponseFormat.retParam(1,200);
        }catch (Exception e){
            e.printStackTrace();
            log.error("需求建议列表导入失败",e );
            return  ResponseFormat.retParam(0,1000,e.getMessage());
        }finally {
            //读取完毕则关闭流
            in.close();
            workBook.close();
        }

    }

    @PostMapping(value="/xf" , produces = "application/json; charset=utf-8")
    @ApiOperation("下发接口")
    public JsonBean distribute(HttpServletRequest request,
                                 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token")String token,
                                 @ApiParam(name="rs", value="请求参数：ids,personIds", required = true) @RequestBody Map<String,String> params
    ){
        JsonBean jsonBean = null;
        try{
            String ids = params.get("ids");
            String personIds = params.get("personIds");
            if(StringUtil.isEmpty(ids)){
                return ResponseFormat.retParam(30001,"未选择下发的需求建议");
            }
            if(StringUtils.isEmpty(personIds)){
                return ResponseFormat.retParam(30001,"未选择下发的人员");
            }
            requireSuggestionService.distribute(ids,personIds);
            jsonBean= ResponseFormat.retParam(1,200,null);
        }catch (Exception e){
            e.printStackTrace();
            jsonBean= ResponseFormat.retParam(0,e.getMessage(),null);
        }
        return jsonBean;
    }

}
