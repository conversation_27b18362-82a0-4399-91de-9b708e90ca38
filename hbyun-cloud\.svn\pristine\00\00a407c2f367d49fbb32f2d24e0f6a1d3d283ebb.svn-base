package com.huabo.monitor.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblStaffUtil;

import com.hbfk.util.JsonBean;

import com.huabo.monitor.entity.*;


import com.huabo.monitor.service.*;
import com.huabo.monitor.util.ConstClass;
import com.huabo.monitor.util.DateUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;



@RestController
@Slf4j
@Api(value = "评价管理-评价立项", tags = {"评价管理-评价立项所有接口"})
@RequestMapping(value = "/nbkz")
public class PjlxController {

    @Autowired
    TblAssessService tblAssessService;

    @Autowired
    ITblStaffService iTblStaffService;
    @Autowired
    ITblAssessMarkService  iTblAssessMarkService;

    @Autowired
    ITblTaskService iTblTaskService;
    @Autowired
    ITblAssesstempleService iTblAssesstempleService;

    @Autowired
    ITblAsseleCategoryService iTblAsseleCategoryService;

    @GetMapping(value = "/pjgl/proj_initiate")
    @ApiOperation("评价立项-主页查询")
    public JsonBean initiatePjgl(@ApiParam(name = "pageNumber", value = "pageNumber") @RequestParam(value = "pageNumber", required = false) Integer pageNumber,
                                 @ApiParam(name = "assNumnber", value = "评价编号") @RequestParam(value = "assNumnber", required = false) String assNumnber,
                                 @ApiParam(name = "assName", value = "项目名称") @RequestParam(value = "assName", required = false) String assName,
                                 @ApiParam(name = "startDate", value = "开始时间:格式年-月-日") @RequestParam(value = "startDate", required = false) String startDate,
                                 @ApiParam(name = "startDates", value = "--开始时间") @RequestParam(value = "startDates", required = false) String startDates,
                                 @ApiParam(name = "endDate", value = "结束日期") @RequestParam(value = "endDate", required = false) String endDate,
                                 @ApiParam(name = "endDates", value = "--结束日期") @RequestParam(value = "endDates", required = false) String endDates,
                                 @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token
    ) throws Exception {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }

        TblStaffUtil staff = DealUserToken.parseUserToken(token);
        boolean isAudit = false;
        TblOrganization organization=this.tblAssessService.queryOrganizationById(staff.getLinkDetp().getOrgid());
        System.out.println(organization);
        /*if (organization.getAudittype() != null
                && organization.getAudittype().equals(new BigDecimal(TblOrganization.AUDITTYPE))) {
            isAudit = true;
        }*/
        System.out.println("isAudit---"+isAudit);
        IPage<TblAssessVo> iPage=tblAssessService.initiatePjgl( isAudit,staff.getCurrentOrg().getOrgid(),staff.getStaffid(),staff.getRealname(),pageNumber, assNumnber, assName, startDate, startDates, endDate, endDates);

        Map<String, Object> mv = new HashMap<>();
        mv.put("assNumnber", assNumnber);
        mv.put("assName", assName);
        mv.put("startDate", startDate);
        mv.put("startDates", startDates);
        mv.put("endDate", endDate);
        mv.put("endDates", endDates);
        mv.put("pageBean", iPage);
        return new JsonBean(200, "success", mv);
    }


    /**
     * 通用获取设置的编号
     *
     * @param
     */
    @GetMapping(value = "/code/findAutoNumber")
    @ApiOperation("评价立项-新建页面-编号生成(这是通用方法,通过改变传值生成各类编号)")
    public JsonBean findAutoNumber(HttpServletResponse response,
                                   @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token,
                                   @ApiParam(name = "tblName", value = "TBL_ASSESS", defaultValue = "TBL_ASSESS") @RequestParam(value = "tblName") String tblName,
                                   @ApiParam(name = "column", value = "ASSESSID", defaultValue = "ASSESSID") @RequestParam(value = "column") String column,
                                   @ApiParam(name = "orgCol", value = "TBLCOMANY", defaultValue = "TBLCOMANY") @RequestParam(value = "orgCol") String orgCol,
                                   @ApiParam(name = "noId", value = "noId", defaultValue = "309") @RequestParam(value = "noId") Integer noId) throws Exception {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        TblStaffUtil staff = DealUserToken.parseUserToken(token);
        String flowNextId = null;
        try {
            flowNextId = tblAssessService.findFlowNextId(tblName, column, orgCol, staff.getCurrentOrg().getOrgid(), noId,
                    null, null, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        response.reset();
        return new JsonBean(200, "success", flowNextId);
    }


    /*
       选择评价负责人
       点击树--查询用户信息
    */
    @GetMapping(value = "/pjlx/list")
    @ApiOperation("评价立项-新建/修改页面-选择评价负责人列表")
    public JsonBean pjlxuserListss(@ApiParam(name = "pageNumber", value = "pageNumber", defaultValue = "1") @RequestParam(value = "pageNumber", required = false) Integer pageNumber,
                                   @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token,
                                   @ApiParam(name = "pid", value = "左侧树节点id", defaultValue = "198328") @RequestParam(value = "pid", required = false) String pid,
                                   @ApiParam(name = "realname", value = "用户真实名", defaultValue = "") @RequestParam(value = "realname", required = false) String realname
    ) throws Exception {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        TblStaffUtil staff = DealUserToken.parseUserToken(token);

        BigDecimal orgid = staff.getCurrentOrg().getOrgid();// 选则的机构
        TblOrganization tblOrganization;
        Integer orgtype;
        if (StringUtils.isNotBlank(pid)) {
            tblOrganization = this.tblAssessService.queryOrganizationById(new BigDecimal(pid));
            orgid = tblOrganization.getOrgid();
            orgtype = tblOrganization.getOrgtype().intValue();
        }else{
            tblOrganization = this.tblAssessService.queryOrganizationById(orgid);
            orgtype=tblOrganization.getOrgtype().intValue();
        }

        IPage<Map<String, Object>> iPage = new Page<>(pageNumber, ConstClass.DEFAULT_SIZE);

        this.tblAssessService.queryAllPageBeanPid(iPage, orgid, orgtype, realname);

        Map<String, Object> mv = new LinkedHashMap<>();
        mv.put("pid", pid);
        mv.put("pageBean", iPage);

        return new JsonBean(200, "success", mv);
    }

    @GetMapping(value = "/pjgl/proj_tmplLocation")
    @ApiOperation("评价立项-新建/修改页面-选择模板列表")
    public JsonBean proj_tmplLocationPjgl(
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token,
            @ApiParam(name = "pageNumber", value = "pageNumber", defaultValue = "1") @RequestParam(value = "pageNumber", required = false) Integer pageNumber,
            @ApiParam(name = "templeNumber", value = "模板编号", defaultValue = "") @RequestParam(value = "templeNumber", required = false) String templeNumber,
            @ApiParam(name = "orgids", value = "组织机构编号s", defaultValue = "127634,198333") @RequestParam(value = "orgids", required = true) String orgids,
            @ApiParam(name = "templename", value = "模板名称", defaultValue = "") @RequestParam(value = "templename", required = false) String templename) throws Exception {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        TblStaffUtil staff = DealUserToken.parseUserToken(token);
        IPage<Map<String, Object>> iPage = new Page<>(pageNumber, ConstClass.DEFAULT_SIZE);

        if (StringUtils.isNotBlank(orgids)) {
            this.tblAssessService.findAll(iPage, staff.getCurrentOrg().getOrgid().toString(), templeNumber, templename, orgids);

        }

        Map<String, Object> mv = new HashMap<>();
        mv.put("pageBean", iPage);
        mv.put("templeNumber", templeNumber);
        mv.put("templename", templename);
        mv.put("orgids", orgids);
        return new JsonBean(200, "success", mv);

    }


    /**
     * 评价立项 --保存项目
     *
     * @param start
     * @param end
     * @param templatekey
     * @return assessid
     * assessname
     * start
     * end
     * staffname
     * staffid
     * projname
     * orgid
     * templatename
     */
    @PostMapping(value = "/pjgl/add", produces = "application/json; charset=utf-8")
    @ApiOperation("评价立项-新建页面-保存")
    public JsonBean addPjgl(
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token,
            @ApiParam(name = "assessid", value = "项目编号", defaultValue = "EVA-30") @RequestParam(value = "assessid")String assessid,
            @ApiParam(name = "assessname", value = "项目名称", defaultValue = "") @RequestParam(value = "assessname")String assessname,
            @ApiParam(name = "start", value = "开始日期", defaultValue = "2022-07-10") @RequestParam(value = "start") String start,
            @ApiParam(name = "end", value = "结束日期", defaultValue = "") @RequestParam(value = "end")String end,
            @ApiParam(name = "staffid", value = "负责人id", defaultValue = "792927") @RequestParam(value = "staffid")String staffid,
            @ApiParam(name = "templatekey", value = "模板key", defaultValue = "228595") @RequestParam(value = "templatekey") BigDecimal templatekey,
            @ApiParam(name = "orgid", value = "评价对象orgids", defaultValue = "198328,127634") @RequestParam(value = "orgid")String orgid)throws Exception  {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        TblStaffUtil staff = DealUserToken.parseUserToken(token);

        if (StringUtils.isNotBlank(assessid) && StringUtils.isNotBlank(orgid)) {
            TblAssess tblAssess=new  TblAssess();
            // List<TblAssess> assesses =
            // this.tblAssessService.getAssessByNumber(tblAssess.getAssessid());
            Long count = this.tblAssessService.selectTblassessNumber(assessid, staff.getCurrentOrg().getOrgid());
            if (count > 0) {
                return  new JsonBean(-1,"编号不能重复",null);
            } else {
                if (DateUtils.compare_date(end, start) == 1) {
                    tblAssess.setAssessid(assessid);
                    tblAssess.setAssessname(assessname);
                    tblAssess.setStartdate(DateUtils.StringToLocalDateTime(start,"yyyy-MM-dd"));
                    tblAssess.setEnddate(DateUtils.StringToLocalDateTime(end,"yyyy-MM-dd"));
                    tblAssess.setAsstemid(templatekey);

                    tblAssess.setAssstatus(1 + "");
                    tblAssess.setAsssponsor(staff.getRealname());

                    tblAssess.setTblcomany(staff.getCurrentOrg().getOrgid().toString());
                     //---------------------------------
                    if (staffid != null && staffid.trim().length() > 0) {
                        tblAssess.setLeaderid(new BigDecimal(staffid)); //负责人id

                    }

                    return tblAssessService.saveAssEss(staff,tblAssess,orgid);
                } else {
                    return  new JsonBean(-1,"结束时间不能大于开始时间",null);
                }

            }
        }
        return null;
    }


    @GetMapping("/pjgl/proj_diap")
    @ApiOperation("评价立项-列表页面-点击评价模板")
    public JsonBean proj_diapPjgl(
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token,
            @ApiParam(name = "tmplId", value = "模板id", defaultValue = "226754") @RequestParam(value = "tmplId") BigDecimal  tmplId) {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        return this.tblAssessService.queryPjmb(tmplId);
    }



    /**
     * 获取项目下面的被评价对象
     *
     * @param pageNumber
     * @param selectedPlans
     * @return
     */
    @GetMapping(value = "/pjgl/t08_proj_org")
    @ApiOperation("评价立项-列表页面-授权列表")
    public JsonBean t08_proj_task(
            @ApiParam(name = "pageNumber", value = "pageNumber",defaultValue="1") @RequestParam(value = "pageNumber", required = false) Integer pageNumber,
            @ApiParam(name = "selectedPlans", value = "assid",defaultValue ="647047" ) @RequestParam(value = "selectedPlans") BigDecimal selectedPlans,
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {

        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }

        TblStaffUtil staff = DealUserToken.parseUserToken(token);


        Map<String, Object> mav = new HashMap<>();

        if (null != selectedPlans) {

            IPage<Map<String,Object>> page = new Page(pageNumber, ConstClass.DEFAULT_SIZE);

            this.tblAssessService.getOrgByassId(page,selectedPlans);
            TblAssess assess = tblAssessService.getById(selectedPlans);

            mav.put("pageBean", page);
            mav.put("project",assess);
        }
        return new JsonBean(200,"success", mav);
    }


    /**
     * 项目授权
     *
     * @return
     */
    @ApiOperation("评价立项-授权列表-授权")
    @GetMapping(value = "/pjgl/t08_proj_modify")
    public JsonBean t08_proj_modify(
            @ApiParam(name = "pageNumber", value = "pageNumber",defaultValue="1") @RequestParam(value = "pageNumber", required = false) Integer pageNumber,
            @ApiParam(name = "assId", value = "assId",defaultValue ="647047" ) @RequestParam(value = "assId") BigDecimal assId,
            @ApiParam(name = "orgId", value = "orgId",defaultValue ="198328" ) @RequestParam(value = "orgId")BigDecimal orgId,
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {

        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        Map<String, Object> mav = new HashMap<>();
        if (null != assId && null != orgId) {

            IPage<TblAssessMarkVo> page = new Page(pageNumber, ConstClass.DEFAULT_SIZE);
            this.tblAssessService.findAssessMarkVoByPageBean(page,assId,orgId);


            mav.put("pageBean", page);
            mav.put("assId", assId);
            mav.put("pageNumber", pageNumber);
            mav.put("orgId", orgId);
            mav.put("project", this.tblAssessService.getById(assId));

        }
        return new JsonBean(200,"success", mav);
    }

    /**
     * 设置参评人权重
     *
     * @return
     */
    @ApiOperation("评价立项-授权列表-授权-设置参评人权重")
    @GetMapping(value = "/pjgl/t08_proj_assignor")
    public JsonBean t08_proj_assignor(
            @ApiParam(name = "assMarkId", value = "assMarkId",defaultValue ="647143" ) @RequestParam(value = "assMarkId") BigDecimal assMarkId,
            @ApiParam(name = "pageNumber", value = "pageNumber",defaultValue="1") @RequestParam(value = "pageNumber", required = false) Integer pageNumber,
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {

        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        Map<String, Object> mav = new HashMap<>();

        IPage<TblAssessStaffVo> page = new Page(pageNumber, ConstClass.DEFAULT_SIZE);
        this.tblAssessService.getTblAssessStaffByMarkId(page,assMarkId);
        mav.put("pageBean", page);
        mav.put("pageNumber", pageNumber);
        mav.put("assMarkId", assMarkId);
        mav.put("assess", tblAssessService.getTblAssessByassmarkid(assMarkId));

        return new JsonBean(200,"success", mav);
    }


    /**
     * 保存参评人权重
     *
     * @param ids
     * @param values
     * @return
     */
    @PostMapping(value = "/pjgl/SaveAssignor")
    @ApiOperation("评价立项-授权列表-授权-保存参评人权重")
    public JsonBean  SaveAssignor(
            @ApiParam(name = "ids", value = "ids",defaultValue ="647181,647182,647183" ) @RequestParam(value = "ids") String ids,
            @ApiParam(name = "values", value = "values",defaultValue ="25,25.0,50" ) @RequestParam(value = "values")String values,
            @ApiParam(name = "assMarkId", value = "assMarkId",defaultValue ="647143" ) @RequestParam(value = "assMarkId") BigDecimal assMarkId,
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {

        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        Map<String, Object> map = new HashMap<String, Object>();
        if (StringUtils.isNotBlank(ids) && StringUtils.isNotBlank(values)) {
            String[] idArray = ids.split(",");
            String[] varleArray = values.split(",");
            Double number = 0d;
            for (int i = 0; i < varleArray.length; i++) {
                number += Double.parseDouble(varleArray[i]);
            }
            if (number > 100) {
                return new JsonBean(-1,"权重比例设置不正确",null);
            }

            TblAssessMark tblAssessMark = this.tblAssessService.updateTblAssessStaff(assMarkId, idArray, varleArray);

            map.put("assId", tblAssessMark.getAssid());
            map.put("orgId", tblAssessMark.getAssorgid());
            map.put("tblAssessMark", tblAssessMark);

        }
        return new JsonBean(200,"success", map);
    }


    /**
     * 获取组织架构下面的人员
     *
     * @param nodeId   部门id
     * @param
     * @param  z      markids
     * @return
     */
    @ApiOperation("评价立项-授权列表-授权-右边主评人/参评人管理列表")
    @GetMapping(value = "/pjgl/setting_user_list")
    public JsonBean settingUserList(
            @ApiParam(name = "pageNumber", value = "pageNumber",defaultValue="1") @RequestParam(value = "pageNumber", required = false) Integer pageNumber,
            @ApiParam(name = "nodeId", value = "部门id",defaultValue="198328") @RequestParam(value = "nodeId", required = true)  BigDecimal nodeId,
            @ApiParam(name = "z", value = "assmarkids",defaultValue="647160,647143,647144") @RequestParam(value = "z", required = false)        String z,
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {

        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        IPage<TblStaff> page = new Page(pageNumber, ConstClass.DEFAULT_SIZE);
        iTblStaffService.findStaffByOrgid(page,nodeId);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("pageBean", page);
        map.put("z", z);
        map.put("nodeId", nodeId);
        map.put("pageNumber", pageNumber);

        return new JsonBean(200,"success", map);
    }


    /**
     * 保存主评人
     *
     * @param z
     * @param userid
     * @return
     */
    @PostMapping(value = "/pjgl/savezupingUsers")
    @ApiOperation("评价立项-主评人管理列表-点击选定保存主评人")
    public JsonBean  savezupingUsers(
            @ApiParam(name = "z", value = "assmarkids",defaultValue="647160,647143,647144") @RequestParam(value = "z")        String z,
            @ApiParam(name = "userid", value = "用户id",defaultValue="198339") @RequestParam(value = "userid")  String userid,
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {

        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        if (StringUtils.isNotBlank(z) && StringUtils.isNotBlank(userid)) {
            String[] str = z.split(",");
            return this.tblAssessService.updateAssessMarkStaffid(userid,str);
        }
        return  new JsonBean(-1,"保存失败", null);
    }


    /**
     * 保存主评人
     *
     * @param z
     * @param users
     * @return
     */
    @PostMapping(value = "/pjgl/savecanpingUsers")
    @ApiOperation("评价立项-参评人管理列表-点击选定保存参评人")
    public JsonBean  savecanpingUsers(
            @ApiParam(name = "z", value = "assmarkids",defaultValue="647144,647145") @RequestParam(value = "z")        String z,
            @ApiParam(name = "users", value = "用户ids",defaultValue="198338,198339") @RequestParam(value = "users")  String users,
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }

        if (StringUtils.isNotBlank(z) && StringUtils.isNotBlank(users)) {

            return this.tblAssessService.updateCanPingRen(users,z);
        }
        return  new JsonBean(-1,"保存失败", null);
    }


    /**
     * 项目立项 --启动
     *
     * @param assId
     * @return
     */
    @PostMapping(value = "/pjgl/start")
    @ApiOperation("评价立项-启动")
    public  String start(

              @ApiParam(name = "assId", value = "assId",defaultValue="647047") @RequestParam(value = "assId")  BigDecimal assId,
               @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {
        if (!ConstClass.checkToken(token)) {
            return JsonBean.error("无token");
        }

        TblAssess assess = this.tblAssessService.getById(assId);
        if (assess.getAssstatus().equals(TblAssess.CREATE)) {

            QueryWrapper<TblAssessMark> qw=new QueryWrapper();
            qw.eq("assid",assId);
            qw.eq("suitable",1);
            qw.eq("state",1);
            List<TblAssessMark> assessMarks = iTblAssessMarkService.list(qw);

            if (assessMarks.size() == 0) {

                List<BigDecimal> staff = this.iTblTaskService.getStaff(assId);
                for (BigDecimal bigDecimal : staff) {
                    TblTask task = new TblTask();
                    task.setStaffid(bigDecimal);

                    task.setStatus(TblTask.START);
                    task.setTaskcode(assess.getAssessid());
                    task.setTaskname(assess.getAssessname());

                    task.setUrl("/nbkz/pjgl/proj_task_gradelist");
                    task.setTasktime(LocalDateTime.now());
                    this.iTblTaskService.save(task);
                }
                assess.setAssstatus(TblAssess.START);
                assess.setAssstartday(LocalDateTime.now());
                this.tblAssessService.update(assess);
                return JsonBean.success();
            } else {
                return JsonBean.error("参评人设置不完整");
            }
        }
        return JsonBean.error("不能重复启动");
    }


    /**
     * 项目立项 --修改
     *
     * @param selectedPlans
     *
     */
    @GetMapping(value = "/pjgl/proj_modify")
    @ApiOperation("评价立项-修改")
    public  JsonBean proj_modifyPjgl(

            @ApiParam(name = "selectedPlans", value = "selectedPlans",defaultValue="647047") @RequestParam(value = "selectedPlans")  BigDecimal selectedPlans,
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        Map<String, Object> map = new HashMap<String, Object>();

        if (null != selectedPlans) {
            TblAssess assess = tblAssessService.getById(selectedPlans);

            List<TblOrganization> organizations=this.tblAssessService.getOrgByassId(assess.getAssid());

            String orgids = "";
            String orgName = "";
            for (TblOrganization tblOrganization : organizations) {
                orgids += tblOrganization.getOrgid() + ",";
                orgName += tblOrganization.getOrgname() + ",";
            }
            map.put("assess", assess);

            if (orgids.length() > 0) {
                map.put("orgids", orgids.substring(0, orgids.length() - 1));
                map.put("orgName", orgName.substring(0, orgName.length() - 1));
            }
            //查询负责人
            //final TblStaff fuzeren = this.iTblStaffService.getById(assess.getLeaderid());
            //查询模板
            final TblAssesstemple moban = this.iTblAssesstempleService.getById(assess.getAsstemid());
            map.put("fuzeren", "");
            map.put("moban", moban);
        }


        return new JsonBean(200,"success", map);
    }


    /**
     * 执行修改操作
     *
     * @param
     * @param
     * @return
     */
    @PostMapping(value = "/pjgl/proj_update")
    @ApiOperation("评价立项-修改-保存修改")
    public JsonBean proj_update(
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token,
            @ApiParam(name = "assid", value = "assid", defaultValue = "647047") @RequestParam(value = "assid")String assid,
            @ApiParam(name = "assessname", value = "项目名称", defaultValue = "") @RequestParam(value = "assessname")String assessname,
            @ApiParam(name = "start", value = "开始日期", defaultValue = "2020-02-20") @RequestParam(value = "start") String start,
            @ApiParam(name = "end", value = "结束日期", defaultValue = "2020-02-24") @RequestParam(value = "end")String end,
            @ApiParam(name = "staffid", value = "负责人id", defaultValue = "527565") @RequestParam(value = "staffid")String staffid,
            @ApiParam(name = "templatekey", value = "模板key", defaultValue = "646171") @RequestParam(value = "templatekey") BigDecimal templatekey,
            @ApiParam(name = "orgid", value = "评价对象orgids", defaultValue = "198328,198330") @RequestParam(value = "orgid")String orgid)throws Exception  {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        TblStaffUtil staff = DealUserToken.parseUserToken(token);
        if (null != assid && StringUtils.isNotBlank(orgid)) {

            return tblAssessService.updateAssEss(staff,assid,assessname,start,end,staffid,templatekey,orgid);

        }
        return null;
    }



    /**
     * 项目立项 --删除
     *
     * @param
     * @return
     */
    @PostMapping(value = "/pjgl/proj_delete")
    @ApiOperation("评价立项-删除")
    public @ResponseBody String proj_deletePjgl(
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token,
            @ApiParam(name = "assid", value = "assid", defaultValue = "") @RequestParam(value = "assid")BigDecimal assid) {
        if (!ConstClass.checkToken(token)) {
            return JsonBean.error("无token");
        }
        return this.tblAssessService.deleteAssess(assid);

    }


    @ApiOperation("评价立项-主评人/参评人-左侧部门树")
    @GetMapping(value = "/pjgl/tree")
    public JsonBean  zupingtree(
            @ApiParam(name = "z", value = "assmarkid如果多值用逗号隔开", defaultValue = "644778,644779") @RequestParam(value = "z") String z,
            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token
    ) throws Exception  {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        Map<String, Object> map = new HashMap<String, Object>();

        String[] ids = z.split(",");
        if(ids.length>0){
            TblAssessMark assessMark=this.iTblAssessMarkService.getById(ids[0]);
            if(assessMark==null){
                return new JsonBean(-1,"assmarkid传值不正确,数据库无此数据", null);
            }else{
              List<Map<String,Object>> treeList= this.tblAssessService.getOrgTree(assessMark.getAssorgid());
              map.put("tree", treeList);

            }
        }

        map.put("z", z);


        return new JsonBean(200,"success", map);
    }


}
