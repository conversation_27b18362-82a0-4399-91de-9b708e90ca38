package com.huabo.system.service;

import com.hbfk.util.JsonBean;
import com.huabo.system.mysql.entity.TblJobMySql;
import com.huabo.system.oracle.entity.TblJob;
import com.huabo.system.oracle.entity.TblRole;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface TblJobService {
    List<TblJob> findAll(BigDecimal var1);

    List<TblJobMySql> findAllMySql(BigDecimal var1);

    void saveJob(TblJob var);

    void saveMySqlJob(TblJobMySql var);

    TblJob findByid(String var1);

    TblJobMySql findByMySqlid(String var1);

    void delete(TblJob var1);

    void deleteMySql(TblJobMySql var1);

    Map<String, Object> list(Integer pageNumber,Integer pageSize,String token,String staffId);

    TblJob findByJobId(String toString);

    TblJobMySql findByMySqlJobId(String toString);

    void updateJob(TblJob newJob);

    void updateMySqlJob(TblJobMySql newJob);

    Map<String, Object> listJob(Integer pageNumber, Integer pageSize, String token, String orgIds, String jobName, String orgName);

    void deleteJob(BigDecimal jobid);
    
    Map<String, Object> syncPost(Integer operaType,String data) throws Exception;
    
   void syncJob(String data) throws Exception;

}
