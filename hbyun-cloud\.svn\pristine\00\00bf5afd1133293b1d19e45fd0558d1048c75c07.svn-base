package com.huabo.audit.oracle.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@TableName("TBL_REFORM")
@Data
@ApiModel("实体类")
@Accessors(chain = true)
public class TblWorksheetEntity {

	public final static Integer  NO_ZG=0;
	public final static Integer  YES_ZG=1;

	@TableId(value = "worksheetid", type= IdType.AUTO)
    @ApiModelProperty(value="")
    private BigDecimal worksheetid;
	
    @TableField(value = "tblProblem")
    @ApiModelProperty(value="")
    private TblProblemEntity tblProblem;
    
	@TableField(value = "worksheetnumber")
    @ApiModelProperty(value="")
    private String worksheetnumber;
	
	@TableField(value = "auditedorg")
    @ApiModelProperty(value="")
    private String auditedorg;
	
	@TableField(value = "audittarget")
    @ApiModelProperty(value="")
    private String audittarget;
	
	@TableField(value = "auditdescription")
    @ApiModelProperty(value="")
    private String auditdescription;
	
	@TableField(value = "auditprocess")
    @ApiModelProperty(value="")
    private String auditprocess;
	
	@TableField(value = "auditjudge")
    @ApiModelProperty(value="")
    private String auditjudge;
	
	@TableField(value = "checkopinin")
    @ApiModelProperty(value="")
    private String checkopinin;
	
	@TableField(value = "recorder")
    @ApiModelProperty(value="")
    private String recorder;
	
	@TableField(value = "recordingdate")
    @ApiModelProperty(value="")
    private Date recordingdate;
	
	@TableField(value = "memo")
    @ApiModelProperty(value="")
    private String memo;
	
	@TableField(value = "worksheetname")
    @ApiModelProperty(value="")
    private String worksheetname;
	
	@TableField(value = "worksheetbysystem")
    @ApiModelProperty(value="")
    private String worksheetbysystem;
	
	@TableField(value = "userid")
    @ApiModelProperty(value="")
    private Integer userid;
	
	@TableField(value = "orgid")
    @ApiModelProperty(value="")
    private Integer orgid;
	
	@TableField(value = "rectification")
    @ApiModelProperty(value="")
    private Integer rectification;
	
	@TableField(value = "tblAttachments")
    @ApiModelProperty(value="")
    private Set tblAttachments;
	
	@TableField(value = "tblReforms")
    @ApiModelProperty(value="")
    private Set<TblReformEntity> tblReforms;
	
	@TableField(value = "tblproblemTargets")
    @ApiModelProperty(value="")
    private Set<TblAssessTarget> tblproblemTargets;
}
