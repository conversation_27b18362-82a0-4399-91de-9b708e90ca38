package com.huabo.system.oracle.service.impl;


import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.hbfk.util.PageInfo;
import com.huabo.system.oracle.entity.TblOaFlowMessage;
import com.huabo.system.oracle.mapper.TblOaFlowMessageMapper;
import com.huabo.system.oracle.service.TblOaFlowMessageService;

@Service
public class TblOaFlowMessageServiceImpl implements TblOaFlowMessageService {

	@Resource
	private TblOaFlowMessageMapper tblOaFlowMessageMapper;

	@Override
	public void saveOrUpdateEntity(TblOaFlowMessage tblOaFlowMessage) throws Exception {
		
		TblOaFlowMessage message = this.tblOaFlowMessageMapper.selectByPrimaryKey(tblOaFlowMessage.getId());
		if(message == null) {
			this.tblOaFlowMessageMapper.insertSelective(tblOaFlowMessage);
		}else {
			this.tblOaFlowMessageMapper.updateByPrimaryKeySelective(tblOaFlowMessage);
		}
	}

	@Override
	public String selectFlowTypeById(String id) throws Exception {
		return this.tblOaFlowMessageMapper.selectFlowTypeById(id);
	}

	@Override
	public TblOaFlowMessage findEntityById(String id) throws Exception {
		return this.tblOaFlowMessageMapper.selectByPrimaryKey(id);
	}

	@Override
	public void modifyFlowMessage(TblOaFlowMessage message) throws Exception {
		this.tblOaFlowMessageMapper.updateByPrimaryKeySelective(message);
	}

	@Override
	public void setPageInfoList(PageInfo<TblOaFlowMessage> pageInfo) throws Exception {
		pageInfo.setTlist(this.tblOaFlowMessageMapper.selectPageInfoList(pageInfo));
		pageInfo.setTotalRecord(this.tblOaFlowMessageMapper.selectPageInfoCount(pageInfo));
	}


}
