package com.huabo.fxgl.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huabo.fxgl.util.IgnoreSwaggerParameter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@TableName("TBL_NBSJ_BUG")
@Data
@ApiModel("实体类")
@Accessors(chain = true)
public class TblNbsjBugEntity {
	
	@TableId(value = "bugid", type= IdType.AUTO)
	@ApiModelProperty(value="主键ID")
	private BigDecimal bugid;
	
	@TableField(value = "bugnumber")
	@ApiModelProperty(value="缺陷编号")
	private String bugnumber;
	
	@TableField(value = "bugdescripte")
	@ApiModelProperty(value="缺陷描述")
	private String bugdescripte;
	
	@TableField(value = "discovertime")
	@ApiModelProperty(value="发现日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd", iso = DateTimeFormat.ISO.DATE_TIME)
	private Date discovertime;
	
	@TableField(value = "discoverperson")
	@ApiModelProperty(value="发现人")
	private String discoverperson;
	
	@TableField(value = "BUGPROPERTY")
	@ApiModelProperty(value="缺陷性质")
	private String bugproperty;
	
	@TableField(value = "bugsource")
	@ApiModelProperty(value="是否财务相关")
	private String bugsource;
	
	@TableField(value = "bugdepartment")
	@ApiModelProperty(value="缺陷部门")
	private String bugdepartment;
	
	@TableField(value = "needreform")
	@ApiModelProperty(value="是否需要整改")
	private String needreform;
	
	@TableField(value = "bugreformstatus")
	@ApiModelProperty(value="")
	private String bugreformstatus;
	
	@TableField(value = "projectname")
	@ApiModelProperty(value="")
	private String projectname;
	
	@TableField(value = "memo")
	@ApiModelProperty(value="")
	private String memo;
	
	@TableField(value = "fatherbugid")
	@ApiModelProperty(value="")
	private BigDecimal fatherbugid;
	
	@TableField(value = "bugbysystem")
	@ApiModelProperty(value="")
	private String bugbysystem;
	
	@TableField(value = "tblInnerrules")
	@ApiModelProperty(value="", hidden = true)
	@IgnoreSwaggerParameter
	private Set tblInnerrules;
	
	@TableField(value = "tblAttachments")
	@ApiModelProperty(value="附件", hidden = true)
	@IgnoreSwaggerParameter
	private Set tblAttachments;
	
	@TableField(value = "tblOuterrules")
	@ApiModelProperty(value="", hidden = true)
	@IgnoreSwaggerParameter
	private Set tblOuterrules;
	
	@TableField(value = "tblTracingresponsibilities")
	@ApiModelProperty(value="整改追踪", hidden = true)
	@IgnoreSwaggerParameter
	private Set tblTracingresponsibilities;
	
	@TableField(value = "tblReforms")
	@ApiModelProperty(value="", hidden = true)
	@IgnoreSwaggerParameter
	private Set tblReforms;
	
	@TableField(value = "children")
	@ApiModelProperty(value="", hidden = true)
	@IgnoreSwaggerParameter
	private Set<TblNbsjBugEntity> children;
	
//	@TableField(value = "tblproblemTargets")
//	@ApiModelProperty(value="", hidden = true)
//	@IgnoreSwaggerParameter
//	private Set<TblAssessTarget> tblproblemTargets;
	
	@TableField(value = "businessDescription")
	@ApiModelProperty(value="业务描述")
	private String businessDescription;
	
//	@TableField(value = "relatedProject")
//	@ApiModelProperty(value="", hidden = true)
//	@IgnoreSwaggerParameter
//	private TblNbsjProject relatedProject;
//	
	@TableField(value = "")
	@ApiModelProperty(value="", hidden = true)
	private String bugcrilevel;
	
	@TableField(value = "")
	@ApiModelProperty(value="")
	private Integer bugcriid;
	
	@TableField(value = "")
	@ApiModelProperty(value="缺陷部门", hidden = true)
	private String orgname;
	
	@TableField(value = "RESONFORNOREFORM")
	@ApiModelProperty(value="")
	private String resonfornoreform;
	
	
}
