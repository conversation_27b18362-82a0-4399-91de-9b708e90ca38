<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huabo.monitor.mapper.TblAssesslevelMapper">


	<select id="getRegionNew"
		resultType="com.huabo.monitor.entity.TblAssesslevel">
		select * from TBL_ASSESSLEVEL t where 1=1
		<if test="fen != null">
			and t.levelupper &gt;= #{fen}
		</if>
		and TBLCOMANY= #{orgid} order by t.levelupper desc
	</select>

	<select id="selectPageInfo"
		resultType="com.huabo.monitor.entity.TblAssesslevel">
		select * from TBL_ASSESSLEVEL where 1=1
		<if test="sql!=null and sql!=''">
			${sql}
		</if>
		order by asslevid desc

	</select>

</mapper>
