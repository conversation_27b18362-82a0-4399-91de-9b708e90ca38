server:
 port: 9000

spring:
 application:
  name: hbyunGateWayServer
 cloud:
  gateway:
   globalcors:
    corsConfigurations:
     '[/**]':
      allowCredentials: true
      allowedHeaders: '*'
      allowedMethods: '*'
      allowedOriginPattern: '*'
   discovery:
    locator:
     enabled: true #是否和服务注册与发现组件结合，设置为ture后可以直接使用应用服务名称调用服务
     lowerCaseServiceId: true #是将请求路径上的服务名配置为小写（因为服务注册的时候，向注册中心注册时将服务名转成大写的了），比如以/service-hi/*的请求路径被路由转发到服务名为service-hi的服务上。
   routes:
    - id: hbyunSystemSetting #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8763 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/setting/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      #示例 访问 gateway服务 http://127.0.0.1:9000/setting/test 后，转发到：http:127.0.0.1:8763/test
      filters:
       - RewritePath=/setting/(?<segment>.*),/$\{segment}
    - id: hbyunContractModule #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8064 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/contract/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
       - RewritePath=/contract/(?<segment>.*),/$\{segment}
    - id: hbyunAuditModule #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8065 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/audit/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
      - RewritePath=/contract/(?<segment>.*),/$\{segment}
    - id: hbyunCentralEnterprisesAuditModule #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8681 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/centralaudit/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
      - RewritePath=/centralaudit/(?<segment>.*),/$\{segment}
    - id: hbyunOilAuditModule #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8067 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/oiaudit/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
       - RewritePath=/contract/(?<segment>.*),/$\{segment}
    - id: hbyunknow #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8068 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/konw/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
       - RewritePath=/audit/(?<segment>.*),/$\{segment}
    - id: hbyunLegalModule #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8086 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/fwgl/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
       - RewritePath=/fwgl/(?<segment>.*),/$\{segment}
    - id: hbyunFinancialData #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:9988 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/zbgl/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
       - RewritePath=/zbgl/(?<segment>.*),/$\{segment}
    - id: hbyunCyberMonitor #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8081 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/monitor/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
       - RewritePath=/monitor/(?<segment>.*),/$\{segment}
    - id: hbyunRiskControl #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8082 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/riskcontrol/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
       - RewritePath=/riskcontrol/(?<segment>.*),/$\{segment}
    - id: hbyunMonitor #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8085 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/nkhg/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
       - RewritePath=/nkhg/(?<segment>.*),/$\{segment}
    - id: hbyunEtl #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8017 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/etl/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
       - RewritePath=/etl/(?<segment>.*),/$\{segment}
    - id: hbyunComplianceModule #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8083 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/hggl/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
       - RewritePath=/hggl/(?<segment>.*),/$\{segment}
    - id: hbyunEs #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8018 #目标服务地址 请求转发后的地址
      predicates:
       - Path=/es/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
       - RewritePath=/es/(?<segment>.*),/$\{segment}
    - id: hbyunLog #路由标识 （id:标识，具有唯一性）
      uri: http://127.0.0.1:8066 #目标服务地址 请求转发后的地址
      predicates:
        - Path=/rzgl/** #转发地址格式为  注册中心地址/setting
      #目标服务地址，请求转发后的地址url进行替换，替换掉转发Path  setting
      filters:
        - RewritePath=/rzgl/(?<segment>.*),/$\{segment}
    - id: hbyunfile
      uri: http://************:8077
      predicates:
        - Path=/wjgl/**
      filters:
        - RewritePath=/wjgl/(?<segment>.*),/$\{segment}

#eureka 配置
eureka:
 client:
  service-url:
   #eureka 服务注册中心
   defaultZone: **********************************************/eureka
 instance:
  # 指定自己的ip信息，不指定的话会自己寻找
  ip-address: 127.0.0.1
  # 执行当前服务的应用ID  不可以重复  标识的是每一个具体的的服务
  instance-id: hbfkGateWayService-9000
  # 当调用getHostname获取实例的hostname时，返回ip而不是host名称
  prefer-ip-address: true
  #实例主机名
  hostname: hbfk-gateWay-server

#日志配置
logging:
 level:
  org:
   springframework:
    cloud:
     gateway: debug

application:
 #文件上传
 file-upload:
  base-dir: D:\nginx-1.20.2\html\hbfk
  base-url: http://*************/hbfk
