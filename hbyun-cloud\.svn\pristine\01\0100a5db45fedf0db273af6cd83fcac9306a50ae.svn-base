package com.huabo.cybermonitor.util;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DatabaseTableParam implements Serializable {

	@NotNull(message = "bookid 不能为空")
	@ApiModelProperty(value = "bookid")
	private String bookid;

}

