package com.huabo.legal.oracle.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 法务机构及负责人扩展表 (此表名实体类名称与oracle数据库不一致 注意)
 */
@ApiModel(value = "TblFwglLegalOrganizationExtOracle")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tbl_fwgl_organization_ext")
public class TblFwglLegalOrganizationExtOracle implements Serializable {

	@Id
	@Column(name = "ORGANIZATIONEXTID")
	@GeneratedValue(strategy = GenerationType.IDENTITY, generator = "select HIBERNATE_SEQUENCE.nextval from dual")
	@ApiModelProperty(value = "法务机构及负责人扩展ID")
	private Integer organizationExtId;

	@Column(name = "FIRMREGULATIONSAUDITNUMBER")
	@ApiModelProperty(value = "企业规章制度审核件数")
	private String firmRegulationsAuditNumber;

	@Column(name = "REGULATIONSAUDITRATIO")
	@ApiModelProperty(value = "规章制度审核率(%)")
	private String regulationsAuditRatio;

	@Column(name = "FIRMECONOMICSCONTRACTNUMBER")
	@ApiModelProperty(value = "企业经济合同审核件数")
	private String firmEconomicsContractNumber;

	@Column(name = "ECONOMICSCONTRACTRATIO")
	@ApiModelProperty(value = "经济合同审核率(%)")
	private String economicsContractRatio;

	@Column(name = "FIRMMAJORDECISIONNUMBER")
	@ApiModelProperty(value = "企业重要决策审核件数")
	private String firmMajorDecisionNumber;

	@Column(name = "MAJORDECISIONRATIO")
	@ApiModelProperty(value = "重要决策审核率(%)")
	private String majorDecisionRatio;

	@Column(name = "REMARK")
	@ApiModelProperty(value = "备注")
	private String remark;

	@Column(name = "STATE")
	@ApiModelProperty(value = "状态", hidden = true)
	private Integer state;

	@Column(name = "CREATOR")
	@ApiModelProperty(value = "创建人", hidden = true)
	private String creator;

	@Column(name = "WORKUNIT")
	@ApiModelProperty(value = "工作单位", hidden = true)
	private String workUnit;

	@Column(name = "BELONGGROUP")
	@ApiModelProperty(value = "所属集团", hidden = true)
	private String belongGroup;

	@Column(name = "CREATEDTIME")
	@ApiModelProperty(value = "创建时间", hidden = true)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createdTime;

	@Column(name = "UPDATEDTIME")
	@ApiModelProperty(value = "更新时间", hidden = true)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updatedTime;

	private static final long serialVersionUID = 1L;

	public static TblFwglLegalOrganizationExtOracle ofId(Integer id) {
		TblFwglLegalOrganizationExtOracle legalOrganizationExtMySql = new TblFwglLegalOrganizationExtOracle();
		legalOrganizationExtMySql.setOrganizationExtId(id);
		return legalOrganizationExtMySql;
	}
}
