package com.huabo.audit.oracle.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import org.springframework.format.annotation.DateTimeFormat;

import com.huabo.audit.config.IgnoreSwaggerParameter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.*;

@TableName("TBL_NBSJ_DOUBTFULPOINT")
@Data
@ApiModel("疑点实体类")
@Accessors(chain = true)
//@KeySequence(value="HIBERNATE_SEQUENCE",clazz=Integer.class) //value为数据库中生成的序列名，class指主键属性类型
public class TblNbsjDoubtfulpointEntity {


    @ApiModelProperty(value = "主键")
    @Id
    //@GeneratedValue(strategy = GenerationType.IDENTITY,generator = "select HIBERNATE_SEQUENCE.nextval from dual")
    @TableId(value = "dpointid",type = IdType.INPUT)
    private BigDecimal dpointid;

    @TableField(value = "dpnumber")
    @ApiModelProperty(value = "疑点编号")
    private String dpnumber;
    
    @TableField(value = "editor")
    @ApiModelProperty(value = "编制人",hidden = true)
    @IgnoreSwaggerParameter
    private String editor;
    
    @TableField(value = "CREATESTAFFID")
    @ApiModelProperty(value = "编制人id",hidden = true)
	@IgnoreSwaggerParameter
    private BigDecimal createstaffid;
    
    @TableField(value = "edittime")
    @ApiModelProperty(value = "编制时间",hidden = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd", iso = DateTimeFormat.ISO.DATE_TIME)
    private Date edittime;
    
	@TableField(value = "dpdescribe")
    @ApiModelProperty(value = "疑点描述")
    private String dpdescribe;

	@TableField(value = "memo")
    @ApiModelProperty(value = "备注")
    private String memo;
	
	@TableField(value = "dpname")
    @ApiModelProperty(value = "疑点名称")
    private String dpname;
	
	@TableField(value = "testresult")
    @ApiModelProperty(value = "测试结果")
    private String testresult;
	
	@TableField(value = "dpstatus")
    @ApiModelProperty(value = "")
    private String dpstatus;
	
	@TableField(value = "dpbysystem")
    @ApiModelProperty(value = "")
    private String dpbysystem;
	
	@TableField(value = "orgid")
    @ApiModelProperty(value = "隶属组织id",hidden = true)
	@IgnoreSwaggerParameter
    private BigDecimal orgid;
	
	@TableField(value = "projectid")
    @ApiModelProperty(value = "关联项目id")
    private BigDecimal projectid;
	
	@ApiModelProperty(value = "密级主键")
    @TableField("SECRECTLEVELID")
    @Column(name = "SECRECTLEVELID")
    private BigDecimal secrectLevelId;
    
    @ApiModelProperty(value = "知悉范围 多个逗号分隔")
    @TableField("STAFFSCOPEIDS")
    @Column(name = "STAFFSCOPEIDS")
    private String staffScopeIds;
    
    @ApiModelProperty(value = "知悉访问人员姓名 多个逗号分隔")
    @TableField("STAFFSCOPENAMES")
    @Column(name = "STAFFSCOPENAMES")
    private String staffScopeNames;
	
	
}
