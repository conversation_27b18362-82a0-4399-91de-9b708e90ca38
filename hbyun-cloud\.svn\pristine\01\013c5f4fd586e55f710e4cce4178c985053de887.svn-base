package com.huabo.contract.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblStaffUtil;
import com.hbfk.util.PageInfo;
import com.huabo.contract.config.DateBaseConfig;
import com.huabo.contract.oracle.entity.TblCounterpartBankinfo;
import com.huabo.contract.oracle.mapper.TblCounterpartBankinfoMapper;
import com.huabo.contract.oracle.mapper.TblCyhwProjectbudgetMapper;
import com.huabo.contract.service.TblCounterpartBankInfoService;

@Service
public class TblCounterpartBankInfoServiceImpl implements TblCounterpartBankInfoService {

    @Resource
    private TblCounterpartBankinfoMapper tblCounterpartBankinfoMapper;

    @Resource
    private TblCyhwProjectbudgetMapper tblCyhwProjectbudgetMapper;

    @Override
    public Map<String, Object> findListByPageInfo(PageInfo<TblCounterpartBankinfo> pageInfo, TblCounterpartBankinfo bank) {
        if(DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            pageInfo.setTlist(tblCounterpartBankinfoMapper.findListByPageInfo(pageInfo, bank));
            pageInfo.setTotalRecord(tblCounterpartBankinfoMapper.selectCountByPageInfoCount(bank));
            resultMap.put("code", "1");
            resultMap.put("msg", "访问接口成功");
            resultMap.put("data", pageInfo);
            return resultMap;
        } else {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            pageInfo.setTlist(tblCounterpartBankinfoMapper.findListByPageInfo(pageInfo, bank));
            pageInfo.setTotalRecord(tblCounterpartBankinfoMapper.selectCountByPageInfoCount(bank));
            resultMap.put("code", "1");
            resultMap.put("msg", "访问接口成功");
            resultMap.put("data", pageInfo);
            return resultMap;
        }
    }

    @Override
    public Map<String, Object> saveCounterPartBankInfo(Integer budgetId,String token, TblCounterpartBankinfo bank) {
        if(DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            try {
                TblStaffUtil staff = DealUserToken.parseUserToken(token);
                if (staff == null) {
                    resultMap.put("code", "0");
                    resultMap.put("msg", "用户已失效！");
                    return resultMap;
                }
                if (bank.getBankid() != null) {
                    this.tblCounterpartBankinfoMapper.updateByBankId(bank);
                } else {
                    bank.setBudgetid(new BigDecimal(budgetId));
                    bank.setCreatetime(new Date());
                    bank.setCreatestaff(staff.getStaffid());
                    this.tblCounterpartBankinfoMapper.saveBank(bank);
                }
                resultMap.put("code", "1");
                resultMap.put("msg", "保存成功");
                resultMap.put("data", bank);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return resultMap;
        } else {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            try {
                TblStaffUtil staff = DealUserToken.parseUserToken(token);
                if (staff == null) {
                    resultMap.put("code", "0");
                    resultMap.put("msg", "用户已失效！");
                    return resultMap;
                }
                if (bank.getBankid() != null) {
                    this.tblCounterpartBankinfoMapper.updateByBankId(bank);
                } else {
                    bank.setBudgetid(new BigDecimal(budgetId));
                    bank.setCreatetime(new Date());
                    bank.setCreatestaff(staff.getStaffid());
                    this.tblCounterpartBankinfoMapper.saveBank(bank);
                }
                resultMap.put("code", "1");
                resultMap.put("msg", "保存成功");
                resultMap.put("data", bank);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return resultMap;
        }
    }

    @Override
    public Map<String, Object> findAllListByBankInfo(Integer budgetId, Integer pageNumber, Integer pageSize, TblCounterpartBankinfo bank) {
        if(DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            try {
                bank.setBudgetid(new BigDecimal(budgetId));
                if (pageNumber == null) {
                    pageNumber = 1;
                }
                if (pageSize == null) {
                    pageSize = 15;
                }
                PageInfo<TblCounterpartBankinfo> pageInfo = new PageInfo<TblCounterpartBankinfo>();
                pageInfo.setPageSize(pageSize);
                pageInfo.setCurrentPage(pageNumber);
                pageInfo.setCondition(bank);
                pageInfo.setTlist(tblCounterpartBankinfoMapper.findAllListByBankInfo(pageInfo));
                pageInfo.setTotalRecord(tblCounterpartBankinfoMapper.findCountListByBankInfo(pageInfo));
                resultMap.put("code", "1");
                resultMap.put("msg", "成功");
                resultMap.put("data", pageInfo);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return resultMap;
        } else {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            try {
                bank.setBudgetid(new BigDecimal(budgetId));
                if (pageNumber == null) {
                    pageNumber = 1;
                }
                if (pageSize == null) {
                    pageSize = 15;
                }
                PageInfo<TblCounterpartBankinfo> pageInfo = new PageInfo<TblCounterpartBankinfo>();
                pageInfo.setPageSize(pageSize);
                pageInfo.setCurrentPage(pageNumber);
                pageInfo.setCondition(bank);
                pageInfo.setTlist(tblCounterpartBankinfoMapper.findAllListByBankInfo(pageInfo));
                pageInfo.setTotalRecord(tblCounterpartBankinfoMapper.findCountListByBankInfo(pageInfo));
                resultMap.put("code", "1");
                resultMap.put("msg", "成功");
                resultMap.put("data", pageInfo);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return resultMap;
        }
    }

    @Override
    public Map<String, Object> removeBank(String bankId) {
        if(DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            Integer count = this.tblCounterpartBankinfoMapper.selectCountByUser(bankId);
            if (count > 0) {
                resultMap.put("code", "0");
                resultMap.put("msg", "该账户已使用，无法删除");
                resultMap.put("data", "");
                return resultMap;
            }
            this.tblCounterpartBankinfoMapper.removeBank(bankId);
            resultMap.put("code", "1");
            resultMap.put("msg", "删除成功");
            resultMap.put("data", "");
            return resultMap;
        } else {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            Integer count = this.tblCounterpartBankinfoMapper.selectCountByUser(bankId);
            if (count > 0) {
                resultMap.put("code", "0");
                resultMap.put("msg", "该账户已使用，无法删除");
                resultMap.put("data", "");
                return resultMap;
            }
            this.tblCounterpartBankinfoMapper.removeBank(bankId);
            resultMap.put("code", "1");
            resultMap.put("msg", "删除成功");
            resultMap.put("data", "");
            return resultMap;
        }
    }

    @Override
    public Map<String, Object> modifyBankStatus(String bankId, Integer bankstatus) {
        if(DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            this.tblCounterpartBankinfoMapper.modifyBankStatus(bankId, bankstatus);
            resultMap.put("code", "1");
            resultMap.put("msg", "更新成功");
            resultMap.put("data", "");
            return resultMap;
        } else {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            this.tblCounterpartBankinfoMapper.modifyBankStatus(bankId, bankstatus);
            resultMap.put("code", "1");
            resultMap.put("msg", "更新成功");
            resultMap.put("data", "");
            return resultMap;
        }
    }
}
