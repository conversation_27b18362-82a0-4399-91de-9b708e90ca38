<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huabo.central.enterprises.audit.oracle.mapper.TblStaffOracleMapper">
  <resultMap id="BaseResultMap" type="com.huabo.central.enterprises.audit.oracle.entity.TblStaffOracle">
    <!--@mbg.generated generated on Tue Oct 11 21:51:12 CST 2022.-->
    <!--@Table hbgrctest.tbl_staff-->
    <id column="STAFFID" jdbcType="INTEGER" property="staffId" />
    <result column="REALNAME" jdbcType="VARCHAR" property="realName" />
    <result column="FIXEDPHONE" jdbcType="VARCHAR" property="fixedPhone" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="MIBLEPHONE" jdbcType="VARCHAR" property="miblePhone" />
    <result column="MEMO" jdbcType="VARCHAR" property="memo" />
    <result column="ORGID" jdbcType="INTEGER" property="orgId" />
    <result column="USERNAME" jdbcType="VARCHAR" property="userName" />
    <result column="PASSWORD" jdbcType="VARCHAR" property="password" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
    <result column="ROLEID" jdbcType="INTEGER" property="roleId" />
    <result column="CREATETIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="JOBID" jdbcType="INTEGER" property="jobId" />
    <result column="OUTSIDEID" jdbcType="INTEGER" property="outsideId" />
    <result column="OUTSIDEOPENDID" jdbcType="VARCHAR" property="outsideopendId" />
    <result column="EMAILCODE" jdbcType="VARCHAR" property="emailCode" />
    <result column="UPDATETIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DATASOURCE" jdbcType="VARCHAR" property="datasource" />
    <result column="HISTORYCODE" jdbcType="VARCHAR" property="historyCode" />
    <result column="HISTORYDEPARTMENTID" jdbcType="VARCHAR" property="historyDepartmentId" />
    <result column="MANAGEORGS" jdbcType="VARCHAR" property="manageOrgs" />
    <result column="MANAGEORGNAMES" jdbcType="VARCHAR" property="manageOrgNames" />
    <result column="ROLEIDSTRS" jdbcType="VARCHAR" property="roleIdsTrs" />
    <result column="PKYMSTAFFID" jdbcType="VARCHAR" property="pkymStaffId" />
  </resultMap>

    <select id="findBelongGroupIdUserInfo" resultType="com.huabo.central.enterprises.audit.vo.result.StaffResult">
        select ORGNAME as belongGroupName from TBL_ORGANIZATION where ORGID=#{orgid}
    </select>

    <select id="findWorkUnitIdUserInfo" resultType="com.huabo.central.enterprises.audit.vo.result.StaffResult">
        select ORGNAME as workUnitName from TBL_ORGANIZATION where ORGID=#{orgid}
    </select>

    <select id="findUserInfo" resultType="com.huabo.central.enterprises.audit.vo.result.StaffResult">
        select REALNAME as realName from TBL_STAFF where STAFFID=#{staffId}
    </select>

    <select id="findCreatorUserInfo" resultType="com.huabo.central.enterprises.audit.vo.result.StaffResult">
        select REALNAME as realName from TBL_STAFF where STAFFID=#{staffId}
    </select>

    <select id="findCreatorUserInfos" resultType="com.huabo.central.enterprises.audit.vo.result.UserInfo">
        select STAFFID as staffId,REALNAME as realName from TBL_STAFF where STAFFID in (${staffId})
    </select>

    <select id="findWorkUnitIdUserInfos" resultType="com.huabo.central.enterprises.audit.vo.result.UserInfo">
        select ORGID as workUnitId,ORGNAME as workUnitName from TBL_ORGANIZATION where ORGID in (${orgid})
    </select>

    <select id="findBelongGroupIdUserInfos" resultType="com.huabo.central.enterprises.audit.vo.result.UserInfo">
        select ORGID as belongGroupId,ORGNAME as belongGroupName from TBL_ORGANIZATION where ORGID in (${orgid})
    </select>

    <select id="findBelongGroupIdUserInfoOrgmeno" resultType="com.huabo.central.enterprises.audit.vo.result.UserInfo">
        select ORGID as belongGroupId,orgmeno as belongGroupName from TBL_ORGANIZATION where ORGID in (${orgid})
    </select>

    <select id="findUserInfoExam" resultType="com.huabo.central.enterprises.audit.vo.result.StaffResult">
        select * from TBL_STAFF where ROLEIDSTRS like '%${rid}%' and STAFFID=#{staffId}
    </select>

    <select id="findLikeUser" resultType="java.lang.Integer">
        SELECT RID from TBL_ROLE where rname = #{type} and COMPANYID =#{orgid}
    </select>
    
    <select id="getUserInfoForId" resultType="com.huabo.central.enterprises.audit.oracle.entity.TblStaffOracle">
        SELECT * from TBL_STAFF where  STAFFID=#{staffId}
    </select>

    <select id="getUserInfoForIdMap" resultType="com.huabo.central.enterprises.audit.oracle.entity.TblStaffOracle">
        SELECT * from TBL_STAFF where  STAFFID in (#{staffIds})
    </select>
    

</mapper>
