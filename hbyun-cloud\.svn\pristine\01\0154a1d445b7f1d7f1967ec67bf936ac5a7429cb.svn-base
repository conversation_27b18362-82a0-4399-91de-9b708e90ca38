package com.huabo.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblStaffUtil;
import com.hbfk.util.JsonBean;
import com.hbfk.util.ResponseFormat;
import com.huabo.system.config.DateBaseConfig;
import com.huabo.system.mysql.entity.TblManageScreenRightMySql;
import com.huabo.system.mysql.mapper.TblManageScreenRightMySqlDAO;
import com.huabo.system.oracle.entity.TblManageScreenRight;
import com.huabo.system.oracle.entity.TblStaff;
import com.huabo.system.oracle.mapper.TblManageScreenRightDAO;
import com.huabo.system.service.TblManageScreenRightService;
import com.huabo.system.utils.Tree;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
public class TblManageScreenRightServiceImpl implements TblManageScreenRightService {

    @Resource
    private TblManageScreenRightDAO tblManageScreenRightDAO;

    @Resource
    private TblManageScreenRightMySqlDAO tblManageScreenRightMySqlDAO;


    @Override
    public void grantScreenRight(String userid, String priid) {
        if (DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            tblManageScreenRightDAO.deleteUserId(userid);
            String[] rightIds = priid.split(",");
            for (String rightId : rightIds) {
                tblManageScreenRightDAO.insertUserId(userid, rightId);
            }
        } else {
            tblManageScreenRightMySqlDAO.deleteUserId(userid);
            String[] rightIds = priid.split(",");
            for (String rightId : rightIds) {
                tblManageScreenRightMySqlDAO.insertUserId(userid, rightId);
            }
        }
    }

    @Override
    public String getTree(TblStaff tblStaff) throws Exception {
        if (DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            List<TblManageScreenRight> rightList = this.tblManageScreenRightDAO.findListByGetRight(tblStaff.getStaffid());
            List<Tree> list = new ArrayList(0);
            Tree tree = null;

            for (Iterator var6 = rightList.iterator(); var6.hasNext(); list.add(tree)) {
                TblManageScreenRight right = (TblManageScreenRight) var6.next();
                tree = new Tree();
                tree.setName(right.getRightName());
                tree.setId(new BigDecimal(right.getRightId()));
                tree.setTarget("mainFramex");
                tree.setChecked(right.getStaffid() == null ? false : true);
                tree.setpId(new BigDecimal(right.getFatherId()));
                tree.setUrl("");
                tree.setOpen(true);
                List<Tree> childre = this.addChildren(right.getRightId(), tblStaff.getStaffid());
                if (childre.size() > 0) {
                    tree.setChildren(childre);
                }
            }

            return JSONObject.toJSONString(list);
        } else {
            List<TblManageScreenRightMySql> rightList = this.tblManageScreenRightMySqlDAO.findListByGetRight(tblStaff.getStaffid());
            List<Tree> list = new ArrayList(0);
            Tree tree = null;

            for (Iterator var6 = rightList.iterator(); var6.hasNext(); list.add(tree)) {
                TblManageScreenRightMySql right = (TblManageScreenRightMySql) var6.next();
                tree = new Tree();
                tree.setName(right.getRightName());
                tree.setId(new BigDecimal(right.getRightId()));
                tree.setTarget("mainFramex");
                tree.setChecked(right.getStaffid() == null ? false : true);
                tree.setpId(new BigDecimal(right.getFatherId()));
                tree.setUrl("");
                tree.setOpen(true);
                List<Tree> childre = this.addChildren(right.getRightId(), tblStaff.getStaffid());
                if (childre.size() > 0) {
                    tree.setChildren(childre);
                }
            }

            return JSONObject.toJSONString(list);
        }
    }

    private List<Tree> addChildren(Integer rightId, BigDecimal staffid) throws Exception {
        if (DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            //String sql = "SELECT TMSR.RIGHTID,TMSR.RIGHTNAME,TMSR.FATHERID,(SELECT STAFFID FROM TBL_MANAGE_USER_SCREEN WHERE RIGTHID = TMSR.RIGHTID AND STAFFID = " + staffid + ") STAFFID  FROM TBL_MANAGE_SCREEN_RIGHT TMSR WHERE TMSR.RIGHTSTATUS = 1 AND TMSR.FATHERID = " + rightId;
            List<TblManageScreenRight> rightList = this.tblManageScreenRightDAO.findListByGet(staffid, rightId);
            List<Tree> list = new ArrayList(0);
            Tree tree = null;

            for (Iterator var7 = rightList.iterator(); var7.hasNext(); list.add(tree)) {
                TblManageScreenRight right = (TblManageScreenRight) var7.next();
                tree = new Tree();
                tree.setName(right.getRightName());
                tree.setId(new BigDecimal(right.getRightId()));
                tree.setTarget("mainFramex");
                tree.setChecked(right.getStaffid() == null ? false : true);
                tree.setpId(new BigDecimal(right.getFatherId()));
                tree.setUrl("");
                tree.setOpen(true);
                List<Tree> childre = this.addChildren(right.getRightId(), staffid);
                if (childre.size() > 0) {
                    tree.setChildren(childre);
                }
            }

            return list;
        } else {
            //String sql = "SELECT TMSR.RIGHTID,TMSR.RIGHTNAME,TMSR.FATHERID,(SELECT STAFFID FROM TBL_MANAGE_USER_SCREEN WHERE RIGTHID = TMSR.RIGHTID AND STAFFID = " + staffid + ") STAFFID  FROM TBL_MANAGE_SCREEN_RIGHT TMSR WHERE TMSR.RIGHTSTATUS = 1 AND TMSR.FATHERID = " + rightId;
            List<TblManageScreenRightMySql> rightList = this.tblManageScreenRightMySqlDAO.findListByGet(staffid, rightId);
            List<Tree> list = new ArrayList(0);
            Tree tree = null;

            for (Iterator var7 = rightList.iterator(); var7.hasNext(); list.add(tree)) {
                TblManageScreenRightMySql right = (TblManageScreenRightMySql) var7.next();
                tree = new Tree();
                tree.setName(right.getRightName());
                tree.setId(new BigDecimal(right.getRightId()));
                tree.setTarget("mainFramex");
                tree.setChecked(right.getStaffid() == null ? false : true);
                tree.setpId(new BigDecimal(right.getFatherId()));
                tree.setUrl("");
                tree.setOpen(true);
                List<Tree> childre = this.addChildren(right.getRightId(), staffid);
                if (childre.size() > 0) {
                    tree.setChildren(childre);
                }
            }

            return list;
        }
    }

    @Override
    public void grantScreenRightToRole(String roleId, String rightIds) throws Exception {
        if (DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            tblManageScreenRightDAO.deleteRoleId(roleId);
            String[] rights = rightIds.split(",");
            for (String rightId : rights) {
                tblManageScreenRightDAO.insertRoleId(roleId, rightId);
            }
        } else {
            tblManageScreenRightMySqlDAO.deleteRoleId(roleId);
            String[] rights = rightIds.split(",");
            for (String rightId : rights) {
                tblManageScreenRightMySqlDAO.insertRoleId(roleId, rightId);
            }
        }
    }

    @Override
    public JsonBean getScreenRoleRightList(String token, Integer rightId) throws Exception {
        if (DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            TblStaffUtil loginStaff = DealUserToken.parseUserToken(token);
            if (loginStaff == null) {
                return ResponseFormat.retParam(0, 20006, null);
            }
            if (rightId == null) {
                rightId = -1;
            }
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            if (loginStaff.getRoleIdStrs() == null || "".equals(loginStaff.getRoleIdStrs())) {
                resultMap.put("rightList", null);
                return ResponseFormat.retParam(1, 200, resultMap);
            }
            List<TblManageScreenRight> rightList = this.tblManageScreenRightDAO.selectChildrenRightListByRole(rightId, loginStaff.getRoleIdStrs());
            this.setChidrenRightListByRole(rightList, loginStaff.getRoleIdStrs());

            resultMap.put("rightList", rightList);
            return ResponseFormat.retParam(1, 200, resultMap);
        } else {
            TblStaffUtil loginStaff = DealUserToken.parseUserToken(token);
            if (loginStaff == null) {
                return ResponseFormat.retParam(0, 20006, null);
            }
            if (rightId == null) {
                rightId = -1;
            }
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            if (loginStaff.getRoleIdStrs() == null || "".equals(loginStaff.getRoleIdStrs())) {
                resultMap.put("rightList", null);
                return ResponseFormat.retParam(1, 200, resultMap);
            }
            List<TblManageScreenRightMySql> rightList = this.tblManageScreenRightMySqlDAO.selectChildrenRightListByRole(rightId, loginStaff.getRoleIdStrs());
            this.setMySqlChidrenRightListByRole(rightList, loginStaff.getRoleIdStrs());

            resultMap.put("rightList", rightList);
            return ResponseFormat.retParam(1, 200, resultMap);
        }
    }

    private void setChidrenRightListByRole(List<TblManageScreenRight> rightList, String roleIdStrs) {
        List<TblManageScreenRight> childrenList = null;
        for (TblManageScreenRight right : rightList) {
            childrenList = this.tblManageScreenRightDAO.selectChildrenRightListByRole(right.getRightId(), roleIdStrs);
            this.setChidrenRightListByRole(childrenList, roleIdStrs);
            right.setChildren(childrenList);
        }
    }

    private void setMySqlChidrenRightListByRole(List<TblManageScreenRightMySql> rightList, String roleIdStrs) {
        List<TblManageScreenRightMySql> childrenList = null;
        for (TblManageScreenRightMySql right : rightList) {
            childrenList = this.tblManageScreenRightMySqlDAO.selectChildrenRightListByRole(right.getRightId(), roleIdStrs);
            this.setMySqlChidrenRightListByRole(childrenList, roleIdStrs);
            right.setChildren(childrenList);
        }
    }

    @Override
    public JsonBean getAllRightList(String token, Integer rightId, Integer roleId) throws Exception {
        if (DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            TblStaffUtil loginStaff = DealUserToken.parseUserToken(token);
            if (loginStaff == null) {
                return ResponseFormat.retParam(0, 20006, null);
            }
            if (rightId == null) {
                rightId = -1;
            }
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            List<TblManageScreenRight> rightList = this.tblManageScreenRightDAO.selectAllRightListByRole(rightId);
            this.setAllChidrenRightListByRole(rightList, roleId);

            resultMap.put("rightList", rightList);
            return ResponseFormat.retParam(1, 200, resultMap);
        } else {
            TblStaffUtil loginStaff = DealUserToken.parseUserToken(token);
            if (loginStaff == null) {
                return ResponseFormat.retParam(0, 20006, null);
            }
            if (rightId == null) {
                rightId = -1;
            }
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            List<TblManageScreenRightMySql> rightList = this.tblManageScreenRightMySqlDAO.selectAllRightListByRole(rightId);
            this.setMySqlAllChidrenRightListByRole(rightList, roleId);

            resultMap.put("rightList", rightList);
            return ResponseFormat.retParam(1, 200, resultMap);
        }
    }

    private void setAllChidrenRightListByRole(List<TblManageScreenRight> rightList, Integer roleId) {
        List<TblManageScreenRight> childrenList = null;
        Integer count = 0;
        for (TblManageScreenRight right : rightList) {
            childrenList = this.tblManageScreenRightDAO.selectAllRightListByRole(right.getRightId());
            count = this.tblManageScreenRightDAO.judgeScreenRightByRoleId(right.getRightId(), roleId);
            if (count == 0) {
                right.setChecked(false);
            } else {
                right.setChecked(true);
            }
            this.setAllChidrenRightListByRole(childrenList, roleId);
            right.setChildren(childrenList);
        }
    }

    private void setMySqlAllChidrenRightListByRole(List<TblManageScreenRightMySql> rightList, Integer roleId) {
        List<TblManageScreenRightMySql> childrenList = null;
        Integer count = 0;
        for (TblManageScreenRightMySql right : rightList) {
            childrenList = this.tblManageScreenRightMySqlDAO.selectAllRightListByRole(right.getRightId());
            count = this.tblManageScreenRightMySqlDAO.judgeScreenRightByRoleId(right.getRightId(), roleId);
            if (count == 0) {
                right.setChecked(false);
            } else {
                right.setChecked(true);
            }
            this.setMySqlAllChidrenRightListByRole(childrenList, roleId);
            right.setChildren(childrenList);
        }
    }
}
