package com.huabo.system.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RedisFinalUtis {

    private static final Logger logger = LoggerFactory.getLogger(RedisFinalUtis.class);
    public static final String USERMANGERRIGHT = "userMangerRight";
    public static final String MANAGEORGLIST = "orgFatherRight_list_";
    public static final String ORGLIST = "orglistMenu_risk_";
    public static final String ORGTREEDEPRATMENT = "orgTree_depart_";
    public static final String ORGDEPTLIST = "orgdept_list_";
    public static final String ORGUSERSTR = "orgUser_str_";
    public static final String ORGMENUSTR = "orgMenu_str_";
    public static final String JTTREEGSQH = "orgJtt_reeGsqh_";
    public static final String ORGROOTSTR = "orgRoot_str_";
    public static final String ORGALLUSER = "orgAll_User_";
    public static final String ORGTREEDEPTLIST = "orgDept_list_";
    public static final String ORGALLSTRLIST = "orgAllStr_list_";
    public static final String ORGCHILIST = "orgChildren_tree_";
    public static final String ORGTYPELIST = "orgType_list_";
    public static final String ORGPROIDTREE = "orgPrior_tree_";
    public static final String JTNODEAllGSQh = "orgJtn_deallgsoh_";
    public static final String ORGFATHERIDSTR = "orgFather_str";
    public static final String ORGFATHERTREE = "orgFather_tree_";
    public static final String ORGTREEJTNODE = "orgJTnode_tree_";
    public static final String ORGTREE = "orgtree_";
    public static final String COMPANYBYTREE = "orgCom_tree_";
    public static final String ORGBYFATHERSTR = "orgByFather_str_";
    public static final String ORGNODEHYTREE = "orgNodeHy_tree_";

    public RedisFinalUtis() {
    }
}
