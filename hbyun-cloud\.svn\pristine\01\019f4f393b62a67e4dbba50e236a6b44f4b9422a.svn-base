package com.huabo.contract.oracle.mapper;

import com.hbfk.util.DateUtil;
import com.hbfk.util.PageInfo;
import com.huabo.contract.oracle.entity.*;

import java.math.BigDecimal;

public class TblContractTypeofMapperSqlConfig {


	public String findAllList(BigDecimal orgid, Integer parentId) {//ORGID = " + orgid;
		String sql = "SELECT * FROM TBL_CONTRACT_TYPEOF ";
		if(parentId != null && parentId == -1) {
				sql += " WHERE  PARENTID IS NOT NULL";
		}
		return sql.toString();
	}

	public String selectLedgerListPageInfo(PageInfo<TblContractTypeof> pageInfo) {
		TblContractTypeof condition = pageInfo.getCondition();
		StringBuffer sqlSb = new StringBuffer("SELECT * FROM (SELECT BUDGET.*,ROWNUM RN FROM ( SELECT * FROM TBL_CONTRACT_TYPEOF WHERE 1=1 ");//
		if(condition.getTypename() != null && !"".equals(condition.getTypename())) {
			sqlSb.append(" AND TYPENAME LIKE '%"+condition.getTypename()+"%'");
		}
		if(condition.getTypeid() != null ) {
			sqlSb.append(" AND PARENTID = "+condition.getTypeid()+"");
		}else {
			sqlSb.append(" AND PARENTID IS NULL");
		}
		sqlSb.append(" ORDER BY TYPEID ASC ) BUDGET WHERE rownum <= "+(pageInfo.getCurrentPage()*pageInfo.getPageSize())+" ) WHERE RN > "+pageInfo.getCurrentRecord());
		return sqlSb.toString();
	}

	public String selectLedgerCountPageIfo(PageInfo<TblContractTypeof> pageInfo) {
		TblContractTypeof condition = pageInfo.getCondition();
		StringBuffer sqlSb = new StringBuffer("SELECT COUNT(*) FROM TBL_CONTRACT_TYPEOF WHERE 1=1 ");
		if(condition.getTypename() != null && !"".equals(condition.getTypename())) {
			sqlSb.append(" AND typeName LIKE '%"+condition.getTypename()+"%'");
		}
		if(condition.getTypeid() != null ) {
			sqlSb.append(" AND PARENTID = "+condition.getTypeid()+"");
		}else {
			sqlSb.append(" AND PARENTID IS NULL");
		}
		return sqlSb.toString();
	}

	public String saveContractTypeof(TblContractTypeof typeof) {
		StringBuffer column = new StringBuffer("INSERT INTO TBL_CONTRACT_TYPEOF (TYPEID");
		StringBuffer value = new StringBuffer(" VALUES (HIBERNATE_SEQUENCE.nextval");

		if(typeof.getTypename() != null) {
			column.append(",TYPENAME");
			value.append(",'"+typeof.getTypename()+"'");
		}
		if(typeof.getCreatestaff() != null) {
			column.append(",CREATESTAFF");
			value.append(",'"+typeof.getCreatestaff()+"'");
		}
		if(typeof.getCreatetime() != null) {
			column.append(",CREATETIME");
			value.append(",TO_DATE('"+DateUtil.parseDate(typeof.getCreatetime(),"yyyy-MM-dd HH:mm:ss") +"', 'YYYY-MM-DD HH24:MI:SS')");
		}
		if(typeof.getOrgid() != null) {
			column.append(",ORGID");
			value.append(",'"+typeof.getOrgid()+"'");
		}
		if(typeof.getUpdatestaff() != null) {
			column.append(",UPDATESTAFF");
			value.append(",'"+typeof.getUpdatestaff()+"'");
		}
		if(typeof.getSettingid() != null) {
			column.append(",SETTINGID");
			value.append(",'"+typeof.getSettingid()+"'");
		}
		if(typeof.getPageurl() != null) {
			column.append(",PAGEURL");
			value.append(",'"+typeof.getPageurl()+"'");
		}
		if(typeof.getUpdatetime() != null) {
			column.append(",UPDATETIME");
			value.append(",TO_DATE('"+DateUtil.parseDate(typeof.getUpdatetime(),"yyyy-MM-dd HH:mm:ss") +"', 'YYYY-MM-DD HH24:MI:SS')");
		}
		if(typeof.getParentid() != null) {
			column.append(",PARENTID");
			value.append(",'"+typeof.getParentid()+"'");
		}
		if(typeof.getType() != null) {
			column.append(",TYPE");
			value.append(",'"+typeof.getType()+"'");
		}
		column.append(")");
		value.append(")");
		String sql = column.toString()+value.toString();
		return sql;
	}

	public String selectRepeatTypeName(BigDecimal orgid, String typeNameStr) {
		String sql = "SELECT * FROM TBL_CONTRACT_TYPEOF WHERE ORGID = " + orgid + " AND TYPENAME IN (" + typeNameStr + ")";
		return sql.toString();
	}



	


}
