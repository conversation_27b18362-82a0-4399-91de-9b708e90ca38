package com.huabo.audit.oracle.vo;

import com.huabo.audit.util.BaseVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("项目执行一览列表查询入参")
public class TblnbsjProjectZXYLVo extends BaseVo{
	
	@ApiModelProperty(value="计划编号")
	private String planNumber;
	
	@ApiModelProperty(value="计划名称")
	private String planName;
	
	@ApiModelProperty(value = "项目名称")
    private String prjoectName;
	
	@ApiModelProperty(value="项目编号")
	private String projectCode;
	
	@ApiModelProperty(value="计划年度")
	private String planYear;
	
	@ApiModelProperty(value = "orgid",hidden=true)
    private Integer orgId;
	
}