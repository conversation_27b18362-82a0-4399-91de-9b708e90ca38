package com.huabo.contract.controller;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.aspose.words.Shape;
import com.aspose.words.*;
import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblStaffUtil;
import com.hbfk.util.PageInfo;
import com.hbfk.util.*;
import com.huabo.contract.oracle.entity.*;
import com.huabo.contract.oracle.mapper.*;
import com.huabo.contract.oracle.vo.StaffResult;
import com.huabo.contract.service.*;
import com.huabo.contract.util.FreeMarkerUtil;
import com.huabo.contract.util.HttpClient;
import com.huabo.contract.util.Tree;
import io.netty.util.internal.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfig;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.*;
import java.util.concurrent.ExecutionException;

//import com.huabo.contract.util.FileController;

@RestController
@Slf4j
@Api(value="合同Controller",tags={"合同所有接口"})
public class ContractController {

	private static final Logger logger = LoggerFactory.getLogger(ContractController.class);
	private static final String DOCDIC = PropertyFileReader.getItem("file.path");
	private static final String separator = System.getProperty("file.separator");
	@Resource
	private TblCyhwProjectbudgetService tblCyhwProjectbudgetService;

	@Resource
	private TblAttachmentService tblAttachmentService;

	@Resource
	private TblContractCollectionService tblContractCollectionService;

	@Resource
	private TblFlowService tblFlowService;

	@Resource
	private TblCyhwUnitService tblCyhwUnitService;

	@Resource
	private TblContractPlannodeService tblContractPlannodeService;

	@Resource
	private TblCounterpartBankInfoService tblCounterpartBankInfoService;

	@Resource
	private TblOrgBankAccountService tblOrgBankAccountService;

	@Resource
	private TblContractInvoicesmanagemenService tblContractInvoicesmanagemenService;

	@Resource
	private TblCirculationService tblCirculationService;

	@Resource
	private TblProcessAnalysisService tblProcessAnalysisService;

	@Resource
	private TblProcessAnalusisUserService tblProcessAnalusisUserService;

	@Resource
	private TblMyTaskService tblMyTaskService;

	@Resource
	private TblContractPaymenService tblContractPaymenService;

	@Resource
	private TblOrganizaService tblOrganizaService;

	@Resource
	private TblStaffService tblStaffService;
	
    @Resource
    private TblStaffMapper tblStaffMapper;

	@Resource
	public HttpServletRequest request;

	@Resource
	private TblLegalDisputregistrationService tblLegalDisputregistrationService;

	@Resource
	private TblLegalNegotiatedsettlemenService tblLegalNegotiatedsettlemenService;

	@Resource
	private TblLegalNegotiateRecordService tblLegalNegotiateRecordService;

	@Resource
	private TblLegalNegotiateeAttService tblLegalNegotiateeAttService;

	@Resource
	private TblLegalLitigationsettlementService tblLegalLitigationsettlementService;

	@Resource
	private TblLegalProceedingsrecordService tblLegalProceedingsrecordService;

	@Resource
	private TblLegalLsettlementAttService tblLegalLsettlementAttService;

	@Resource
	private TblLegalArbitratsettlementService tblLegalArbitratsettlementService;

	@Resource
	private TblLegalArbitrationrecordService tblLegalArbitrationrecordService;

	@Resource
	private TblLegalArbitrationAttService tblLegalArbitrationAttService;

	@Resource
	private TblLegalCloseinformationService tblLegalCloseinformationService;

	@Resource
	private TblLegalQualificationService tblLegalQualificationService;

	@Resource
	private TblLegalFrozenaccountService tblLegalFrozenaccountService;

	@Resource
	private TblyypriceService tblyypriceService;

	@Resource
	private TblyyxdfCompanyService tblyyxdfCompanyService;

	@Resource
	private TblYyXdfTeamService tblYyXdfTeamService;
	
	@Resource
	public FreeMarkerConfig freeMarkerConfig;
	
	@Resource
	private TblCyhwProjectbudgetMapper tblCyhwProjectbudgetMapper;
    @Resource
    private TblCirculationMapper tblCirculationMapper;

    @Resource
    private TblContractAppendixsigningMapper tblContractAppendixsigningMapper;
    
    @Resource
    private TblContractLendService tblContractLendService;
    
    @Resource
    private TblMyTaskMapper tblMyTaskMapper;
    
    @Resource
    private TblLegalAssetporotectService tblLegalAssetporotectService;
    
    @Resource
    private TblLegalExecumgrService tblLegalExecumgrService;
    
    @Resource
    private TblLegalCloseSumService tblLegalCloseSumService;
    
    @Resource
    private TbllegalAttorneyService tbllegalAttorneyService;
    
    
    
    
	/** 相对方维护列表页面
	 *
	 * @param request
	 * @param token
	 * @param flowId
	 * @param staffId
	 * @param currentPage
	 * @param pageSize
	 * @param budget
	 * @return
	 */
	@Resource
	private TblCyhwBudetgoodsService tblCyhwBudetgoodsService;

	@Resource
	private TblContractSpnodeService tblContractSpnodeService;

	@Resource
	private TblCyhwEconomiccontractService tblCyhwEconomiccontractService;

	@Resource
	private TblContractTypeofService tblContractTypeofService;

	@Resource
	private TblCyhwBasicuninspectionService tblCyhwBasicuninspectionService;


	@Value("${fwgl.belongGroupName:}")
	private String belongGroupName;
	@Value("${fwgl.legalPersonnel:}")
	private String legalPersonnel;

	/**
	 * 相对方-合同用印-办理流程
	 * @param request
	 * @return
	 */
	@ApiOperation(value="相对方-合同用印-办理流程")
	@PostMapping(value = "/cyhw/blprocessyszc",produces = "application/json; charset=utf-8")
	public @ResponseBody String blprocessyszc(HttpServletRequest request,
											  @ApiParam(name="budgetId",value="budgetId",required=false)@RequestParam(value = "budgetId",required = false)Integer budgetId,
											  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token){
		String taskId=request.getParameter("taskId");
		String processDefinitionId=request.getParameter("processDefinitionId");
		String processInstanceId=request.getParameter("processInstanceId");
		String transitionName=request.getParameter("transitionName");
		String examination=request.getParameter("examination");
		String cyId=request.getParameter("cyId");
		TblStaffUtil user = null;
		BigDecimal orgId = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			user = DealUserToken.parseUserToken(token);
			if (user == null) {
				resultMap.put("code", "0");
				resultMap.put("msg", "用户已失效！");
				JSONObject jsonObj = new JSONObject(resultMap);
				return jsonObj.toString();
			}
			orgId = user.getCurrentOrg().getOrgid();
		}catch (Exception e) {

		}
		try {
			TblCyhwProjectbudget aw = tblCyhwProjectbudgetService.findBudgetid(budgetId);
			if(taskId!=null && !taskId.equals("")){
				Map<String, Object> map = HttpClient.handleProcessJson(user.getStaffid().toString(),transitionName,taskId);
				String object = (String) map.get("result");
				if(object!=null && object.equals("true")){
					TblMyTask task =new TblMyTask();
					TblMyTask oldtask = tblMyTaskService.findOndbyFrom(budgetId.toString());
					//TblAnalysis ond = tblProcessAnalusisUserService.findOnd(oldtask.getAnalid());
					TblProcessAnalysis findOnd = null;
					if(oldtask == null){
						findOnd = tblProcessAnalysisService.findOndBytakdid("");
					}else{
						findOnd=tblProcessAnalysisService.findOnd(oldtask.getAnalid());
					}
					Integer number=1;
					String usertaskid = findOnd.getUsertaskid();
					Integer num=Integer.parseInt(usertaskid.substring(usertaskid.length()-1,usertaskid.length()))+number;
					String blande=usertaskid.substring(0,usertaskid.length()-1)+num;
					TblProcessAnalysis analysis =null;
					if(transitionName!=null && transitionName.equals("退回")){
						analysis = tblProcessAnalysisService.findOndBytakdidstart("",findOnd.getProcessname());
					}else{
						analysis = tblProcessAnalysisService.findOndBytakdidAnId(blande,findOnd.getProcessname());
					}
					TblProcessAnalusisUser analysisUser = null;
					if(analysis!=null && analysis.getAnalid()!=null){
						analysisUser = tblProcessAnalusisUserService.findOnd(analysis.getAnalid().toString(), budgetId.toString());
					}
					TblCirculation circulation = tblCirculationService.getOneBytaskid(budgetId.toString());
					String nextapprover = HttpClient.nextapprover(circulation.getBusinesskey());
					task.setApprovaldate(new Date());
					if(user.getTrole()!=null && user.getTrole().getRname()!=null){
						task.setApprovalrole(user.getTrole().getRname());
					}
					task.setApprover(user.getRealname());
					task.setExamination(examination);
					task.setFromid(budgetId.toString());
					task.setProcessDefinitionId(processDefinitionId);
					task.setUsrid(user.getStaffid().toString());
					task.setCirid(cyId);
					task.setResult(transitionName);
					task.setProcessName(oldtask.getProcessName());
					task.setTaskId(taskId);
					task.setProcessInstanceId(processInstanceId);
					if(StringUtils.isBlank(nextapprover)){//transitionName.equals("完成")||
						task.setHandle("无");
						circulation.setCystate("已完成");
						aw.setInspectionstatus(TblCswlManagement.STATE_WC);
					}else{
						//退回or通过
						TblStaff findById = tblStaffService.findById(circulation.getCystaffid());//表单提交人
						if(nextapprover.contains("bmfzr")) {
                            TblStaff bmfzr = tblStaffService.findByStaffManOrgs(user.getLinkDetp().getOrgid().toString());
							task.setHandle(bmfzr.getRealname());
						}else if(nextapprover.contains("tcuserid")) {
							//退回到创建人
							task.setHandle(findById.getRealname());
						}else {
							//角色
							task.setHandle(nextapprover);
						}
					}
					if(aw.getInspectionstatus()==2){
						aw.setInspectionstatus(TblCswlManagement.STATE_SP);
						circulation.setCystate("审批中");
					}
					if(transitionName!=null && transitionName.equals("退回")){
						circulation.setCystate("需调整");
						aw.setInspectionstatus(TblCswlManagement.STATE_TZ);
					}

					if(transitionName!=null && transitionName.equals("终止")){
						circulation.setCystate("终止");
						task.setHandle("无");
						aw.setInspectionstatus(TblCswlManagement.STATE_ZZ);
					}
					tblMyTaskService.updateSetting(task);
					tblCirculationService.upateTblCirculation(circulation);
					tblCyhwProjectbudgetService.updateTcb(aw);

				}
				  resultMap.put("code", "1");
	              resultMap.put("msg", "办理成功！");
				//return JsonBean.success();
	              JSONObject jsonObj = new JSONObject(resultMap);
				  return jsonObj.toString();
			}else{
				  resultMap.put("code", "0");
	              resultMap.put("msg", "办理失败！");
	              JSONObject jsonObj = new JSONObject(resultMap);
				  return jsonObj.toString();
			}
		} catch (Exception e) {
			log.error("异常信息：", e);
			 resultMap.put("code", "0");
             resultMap.put("msg", "办理失败！");
             JSONObject jsonObj = new JSONObject(resultMap);
			 return jsonObj.toString();
		}
	}

	/**
	 * 付款管理-办理流程
	 * @param request
	 * @return
	 */
	@ApiOperation(value="付款管理-办理流程")
	@PostMapping(value = "/fkgl/blprocess",produces = "application/json; charset=utf-8")
	public @ResponseBody String tzgl_blprocess(HttpServletRequest request, 
			   @RequestParam(value="paymentid",required=true)String paymentid,
			   @RequestParam(value="cyId",required=true)String cyId, 
			   @RequestParam(value="taskId",required=true)String taskId, 
			   @RequestParam(value="transitionName",required=true)String transitionName, 
			   @RequestParam(value="processDefinitionId",required=true)String processDefinitionId, 
			   @RequestParam(value="processInstanceId",required=true)String processInstanceId, 
			   @RequestParam(value="examination",required=true)String examination, 
			   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token){

		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblContractPayment aw = this.tblContractPaymenService.findPaymentInfoByParmentId(Integer.parseInt(paymentid));
			TblStaffUtil user = null;
			BigDecimal orgId = null;
		
			try {
				user = DealUserToken.parseUserToken(token);
				if (user == null) {
					resultMap.put("code", "0");
					resultMap.put("msg", "用户已失效！");
					JSONObject jsonObj = new JSONObject(resultMap);
					return jsonObj.toString();
				}
				orgId = user.getCurrentOrg().getOrgid();
			}catch (Exception e) {

			}
			Long paystate = aw.getPaymentstatus();
			if(taskId!=null && !taskId.equals("")){


				Map<String, Object> map = HttpClient.handleProcessJson(user.getStaffid().toString(),transitionName,taskId);
				String object = (String) map.get("result");
				if(object!=null && object.equals("true")){
					TblMyTask task=new TblMyTask();
					TblMyTask oldtask = tblMyTaskService.findOndbyFrom(paymentid);
					TblProcessAnalysis findOnd = null;
					if(oldtask==null){
						findOnd = tblProcessAnalysisService.findOndBytakdid("");
					}else{
						findOnd = tblProcessAnalysisService.findOnd(oldtask.getAnalid());
					}
					Integer number=1;
					String usertaskid = findOnd.getUsertaskid();
					Integer num=Integer.parseInt(usertaskid.substring(usertaskid.length()-1,usertaskid.length()))+number;
					String blande=usertaskid.substring(0,usertaskid.length()-1)+num;
					TblProcessAnalysis analysis =null;
					if(transitionName!=null && transitionName.equals("驳回")){
						analysis = tblProcessAnalysisService.findOndBytakdidstart("",findOnd.getProcessname());
					}else{
						analysis = tblProcessAnalysisService.findOndBytakdidAnId(blande,findOnd.getProcessname());
					}
					TblProcessAnalusisUser analysisUser = null;

					if(analysis!=null && analysis.getAnalid()!=null){
						analysisUser = tblProcessAnalusisUserService.findOnd(analysis.getAnalid().toString(),paymentid);
					}
					TblCirculation circulation = tblCirculationService.get(cyId);
					
					task.setApprovaldate(new Date());
					task.setApprovalrole(user.getTrole().getRname());
					task.setApprover(user.getRealname());
					task.setExamination(examination);
					task.setFromid(paymentid);
					task.setProcessDefinitionId(processDefinitionId);
					task.setUsrid(user.getStaffid().toString());
					task.setCirid(cyId);
					task.setResult(transitionName);
					task.setProcessName(oldtask.getProcessName());
					task.setTaskId(taskId);
					task.setProcessInstanceId(processInstanceId);
					if(analysis==null || analysisUser==null){
						task.setHandle("无");
						circulation.setCystate("已完成");
						aw.setPaymentstatus(TblContractPayment.STATE_WC);
					}else{
						if(transitionName!=null && transitionName.equals("完成")){
							task.setHandle("无");
							circulation.setCystate("已完成");
							//此处调用上上签电子签章
							aw.setContractstatus(TblCswlManagement.STATE_WC);
						}else {
							task.setHandle(analysisUser.getStaffid());
							task.setAnalid(analysis.getAnalid().toString());
						}
					}
					if(aw.getPaymentstatus()==2){
						aw.setPaymentstatus(TblContractPayment.STATE_SP);
						circulation.setCystate("审批中");
						SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
					}
					if(transitionName!=null && (transitionName.equals("驳回") || transitionName.equals("退回"))){
						circulation.setCystate("需调整");
						aw.setPaymentstatus(TblContractPayment.STATE_TZ);
					}

					if(transitionName!=null && transitionName.equals("终止")){
						circulation.setCystate("终止");
						task.setHandle("无");
						aw.setPaymentstatus(TblContractPayment.STATE_ZZ);
					}
					tblMyTaskService.updateSetting(task);
					tblCirculationService.upateTblCirculation(circulation);
					this.tblContractPaymenService.savePaymentInfo(aw);
				}
				 resultMap.put("code", "1");
	              resultMap.put("msg", "办理成功！");
				//return JsonBean.success();
	              JSONObject jsonObj = new JSONObject(resultMap);
				 return jsonObj.toString();
			}else{
				  resultMap.put("code", "0");
	              resultMap.put("msg", "办理失败！");
	              JSONObject jsonObj = new JSONObject(resultMap);
				 return jsonObj.toString();
			}
		} catch (Exception e) {
			log.error("异常信息：", e);
			 resultMap.put("code", "0");
            resultMap.put("msg", "办理失败！");
            JSONObject jsonObj = new JSONObject(resultMap);
			return jsonObj.toString();
		}
	}


	/**
	 * 收款管理-办理流程
	 * @param request
	 * @return
	 */
	@ApiOperation(value="收款管理-办理流程")
	@PostMapping(value = "/skgl/blprocess",produces = "application/json; charset=utf-8")
	public @ResponseBody String zcgl_blprocess(HttpServletRequest request, 
											   @RequestParam(value="collectionid",required=true)String collectionid,
											   @RequestParam(value="cyId",required=true)String cyId, 
											   @RequestParam(value="taskId",required=true)String taskId, 
											   @RequestParam(value="transitionName",required=true)String transitionName, 
											   @RequestParam(value="processDefinitionId",required=true)String processDefinitionId, 
											   @RequestParam(value="processInstanceId",required=true)String processInstanceId, 
											   @RequestParam(value="examination",required=true)String examination, 
											   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token){
		TblContractCollection aw = tblContractCollectionService.findById(new BigDecimal(collectionid));
		TblStaffUtil user = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			user = DealUserToken.parseUserToken(token);
			if (user == null) {
				resultMap.put("code", "0");
				resultMap.put("msg", "用户已失效！");
				JSONObject jsonObj = new JSONObject(resultMap);
				return jsonObj.toString();
			}
		}catch (Exception e) {
		}
		try {

			if(taskId!=null && !taskId.equals("")){
				//办理流程
				Map<String, Object> map = HttpClient.handleProcessJson(user.getStaffid().toString(),transitionName,taskId);
				String object = (String) map.get("result");
				if(object!=null && object.equals("true")){
					TblMyTask task=new TblMyTask();
					TblMyTask oldtask = tblMyTaskService.findOndbyFrom(collectionid);
					TblProcessAnalysis findOnd = null;
					if(oldtask==null){
						findOnd = tblProcessAnalysisService.findOndBytakdid("");
					}else{
						findOnd = tblProcessAnalysisService.findOnd(oldtask.getAnalid());
					}
					Integer number=1;
					String usertaskid = findOnd.getUsertaskid();
					Integer num=Integer.parseInt(usertaskid.substring(usertaskid.length()-1,usertaskid.length()))+number;
					String blande=usertaskid.substring(0,usertaskid.length()-1)+num;
					TblProcessAnalysis analysis =null;
					if(transitionName!=null && transitionName.equals("驳回")){
						analysis = tblProcessAnalysisService.findOndBytakdidstart("",findOnd.getProcessname());
					}else{
						analysis = tblProcessAnalysisService.findOndBytakdidAnId(blande,findOnd.getProcessname());
					}
					TblProcessAnalusisUser analysisUser = null;
					if(analysis!=null && analysis.getAnalid()!=null){
						analysisUser = tblProcessAnalusisUserService.findOnd(analysis.getAnalid().toString(),collectionid);
					}
					TblCirculation circulation = tblCirculationService.get(cyId);

					//需要修改 TODO
					task.setApprovaldate(new Date());
					task.setApprovalrole(user.getTrole().getRname());
					task.setApprover(user.getRealname());
					task.setExamination(examination);
					task.setFromid(collectionid);
					task.setProcessDefinitionId(processDefinitionId);
					task.setUsrid(user.getStaffid().toString());
					task.setCirid(cyId);
					task.setResult(transitionName);
					task.setProcessName(oldtask.getProcessName());
					task.setTaskId(taskId);
					task.setProcessInstanceId(processInstanceId);
					if(analysis==null || analysisUser==null){
						task.setHandle("无");
						circulation.setCystate("已完成");
						aw.setCollectionstatus(TblContractCollection.STATE_WC);
					}else{
						if(transitionName!=null && transitionName.equals("完成")){
							task.setHandle("无");
							circulation.setCystate("已完成");
							//此处调用上上签电子签章
							aw.setContractstatus(TblCswlManagement.STATE_WC);
						}else {
							task.setHandle(analysisUser.getStaffid());
							task.setAnalid(analysis.getAnalid().toString());
						}
					}
					if(aw.getCollectionstatus()==2){
						aw.setCollectionstatus(TblContractCollection.STATE_SP);
						circulation.setCystate("审批中");
					}

					if(transitionName!=null && (transitionName.equals("驳回") || transitionName.equals("退回"))){
						circulation.setCystate("需调整");
						aw.setCollectionstatus(TblContractCollection.STATE_TZ);
					}
					if(transitionName!=null && transitionName.equals("终止")){
						circulation.setCystate("终止");
						task.setHandle("无");
						aw.setCollectionstatus(TblContractCollection.STATE_ZZ);
					}
					tblMyTaskService.updateSetting(task);
					tblCirculationService.upateTblCirculation(circulation);
					this.tblContractCollectionService.mengerCollectionEntity(aw);
				}
				 resultMap.put("code", "1");
	             resultMap.put("msg", "办理成功！");
				//return JsonBean.success();
	             JSONObject jsonObj = new JSONObject(resultMap);
				 return jsonObj.toString();
			}else{
				  resultMap.put("code", "0");
	              resultMap.put("msg", "办理失败！");
	              JSONObject jsonObj = new JSONObject(resultMap);
				 return jsonObj.toString();
			}
		} catch (Exception e) {
			log.error("异常信息：", e);
			resultMap.put("code", "0");
            resultMap.put("msg", "办理失败！");
            JSONObject jsonObj = new JSONObject(resultMap);
			return jsonObj.toString();
		}
	}


	/**
	 * 合同范本-合同起草-合同变更-办理流程
	 * @param request
	 * @return
	 */
	@ApiOperation(value="合同范本-合同起草-合同变更-办理流程")
	@RequestMapping(value = "/cyhw/blprocessjc",produces = "application/json; charset=utf-8")
	public @ResponseBody String blprocessjc(HttpServletRequest request,
											@ApiParam(name="contractId",value="contractId",required=true)BigDecimal contractId,
											@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token){

		String taskId=request.getParameter("taskId");
		String processDefinitionId=request.getParameter("processDefinitionId");
		String processInstanceId=request.getParameter("processInstanceId");
		String transitionName=request.getParameter("transitionName");
		String examination=request.getParameter("examination");
		String contractNo=request.getParameter("contractNo");
		String cyId=request.getParameter("cyId");
		String imgBaseStr=request.getParameter("imgBaseStr");
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil user = null;
		String orgid = null;
		try {
			user = DealUserToken.parseUserToken(token);
			if (user == null) {
				resultMap.put("code", "0");
				resultMap.put("msg", "用户已失效！");
				JSONObject jsonObj = new JSONObject(resultMap);
				return jsonObj.toString();
			}
			orgid = user.getCurrentOrg().getOrgid().toString();
		}catch (Exception e) {

		}
		try {
			TblCyhwUnit aw = tblCyhwUnitService.getEntity(contractId);
			if(taskId!=null && !taskId.equals("")){

				Map<String, Object> map = HttpClient.handleProcessJson(user.getStaffid().toString(),transitionName,taskId);
				String object = (String) map.get("result");
				if(object!=null && object.equals("true")){
					TblMyTask task=new TblMyTask();
					TblMyTask oldtask = tblMyTaskService.findOndbyFrom(contractId.toString());
					TblProcessAnalysis findOnd = null;
					if(oldtask==null){
						findOnd = tblProcessAnalysisService.findOndBytakdid("");
					}else{
						findOnd= tblProcessAnalysisService.findOnd(oldtask.getAnalid());
					}
					Integer number=1;
					String usertaskid = findOnd.getUsertaskid();
					Integer num=Integer.parseInt(usertaskid.substring(usertaskid.length()-1,usertaskid.length()))+number;
					String blande=usertaskid.substring(0,usertaskid.length()-1)+num;
					
					TblProcessAnalysis analysis =null;
					if(transitionName!=null && transitionName.equals("退回") && user.getTrole()!=null && !user.getTrole().getRname().equals("律师")){
						analysis = tblProcessAnalysisService.findOndBytakdidstart("",findOnd.getProcessname());
					}else if(transitionName!=null && transitionName.equals("退回") && user.getTrole()!=null && user.getTrole().getRname().equals("律师")){
						analysis = tblProcessAnalysisService.findOndBytakdidAnId("usertask2",findOnd.getProcessname());
					}else{
						analysis = tblProcessAnalysisService.findOndBytakdidAnId(blande,findOnd.getProcessname());
					}
					TblProcessAnalusisUser analysisUser = null;
					if(analysis!=null && analysis.getAnalid()!=null){
						analysisUser = tblProcessAnalusisUserService.findOnd(analysis.getAnalid().toString(), contractId.toString());
					}
					TblCirculation circulation = tblCirculationService.getOneBytaskid(contractId.toString());
					//查询执行人 
					String nextapprover = HttpClient.nextapprover(circulation.getBusinesskey());
					task.setApprovaldate(new Date());
					if(user.getTrole()!=null && user.getTrole().getRname()!=null){
						task.setApprovalrole(user.getTrole().getRname());
					}
					task.setApprover(user.getRealname());
					task.setExamination(examination);
					task.setFromid(contractId.toString());
					task.setProcessDefinitionId(processDefinitionId);
					task.setUsrid(user.getStaffid().toString());
					task.setCirid(cyId);
					task.setResult(transitionName);
					task.setProcessName(oldtask.getProcessName());
					task.setTaskId(taskId);
					task.setProcessInstanceId(processInstanceId);
					task.setImgbasestr(imgBaseStr);
					task.setAnalid("");
					if(analysis!=null) {
						task.setAnalid(analysis.getAnalid().toString());
					}
					if(StringUtils.isBlank(nextapprover)){//transitionName.equals("完成")||
							task.setHandle("无");
							circulation.setCystate("已完成");
							//完成合同
							aw.setContractstatus(TblCswlManagement.STATE_WC);
					}else{
						//退回or通过
						TblStaff findById = tblStaffService.findById(circulation.getCystaffid());//表单提交人
						if(nextapprover.contains("bmfzr")) {
                            TblStaff bmfzr = tblStaffService.findByStaffManOrgs(user.getLinkDetp().getOrgid().toString());
							task.setHandle(bmfzr.getRealname());
						}else if (nextapprover.contains("fgld")) {
                        	//分管领导
                            TblStaff fgld = tblStaffMapper.findByStaffFgOrgs(findById.getOrgid().toString());
                            task.setHandle(fgld.getRealname());
                        }else if(nextapprover.contains("tcuserid")) {
							//退回到创建人
							task.setHandle(findById.getRealname());
						}else {
							//角色
							task.setHandle(nextapprover);
						}
					}
					if(aw.getContractstatus()==2){
						circulation.setCystate("审批中");
						aw.setContractstatus(TblCswlManagement.STATE_SP);
					}
					if(transitionName!=null && transitionName.equals("退回") && user.getTrole()!=null && !user.getTrole().getRname().equals("律师")){
						circulation.setCystate("需调整");
						aw.setContractstatus(TblCswlManagement.STATE_TZ);
					}
					if(transitionName!=null && transitionName.equals("终止")){
						circulation.setCystate("终止");
						task.setHandle("无");
						aw.setContractstatus(TblCswlManagement.STATE_ZZ);
					}

					if(JudgeRoleRight.judgeRoleRight("合同管理员",user.getRoleNames())){
						aw.setContractno(contractNo);
						circulation.setCycode(contractNo);
					}
					String recordType = request.getParameter("flowname");
					aw.setRecordtype(recordType);
					tblCirculationService.upateTblCirculation(circulation);
					tblCyhwUnitService.updateCyhwUnitNoDescrpib(aw);

					if(blande.equals("usertask5") && transitionName.equals("通过") && user.getTrole().getRname().equals("律师") && aw.getLinkdept()==null){
						bljc(request, contractId, orgid,"usertask5",task);
					}else{
						tblMyTaskService.updateSetting(task);
					}
				}

				resultMap.put("code", "1");
	              resultMap.put("msg", "办理成功！");
				//return JsonBean.success();
	              JSONObject jsonObj = new JSONObject(resultMap);
				 return jsonObj.toString();
			}else{
				//==加签节点提交
				if(transitionName.equals("提交")) {
					TblMyTask task=new TblMyTask();
					TblMyTask oldtask = tblMyTaskService.findOndbyFrom(contractId.toString());
					
					task.setApprovaldate(new Date());
					if(user.getTrole()!=null && user.getTrole().getRname()!=null){
						task.setApprovalrole(user.getTrole().getRname());
					}
					task.setApprover(user.getRealname());
					task.setExamination(examination);
					task.setFromid(contractId.toString());
					task.setProcessDefinitionId(processDefinitionId);
					task.setUsrid(user.getStaffid().toString());
					task.setCirid(cyId);
					task.setResult(transitionName);
					task.setProcessName(oldtask.getProcessName());
					task.setTaskId(taskId);
					task.setProcessInstanceId(processInstanceId);
					task.setImgbasestr(imgBaseStr);
					task.setAnalid("");
//					if(analysis!=null) {
//						task.setAnalid(analysis.getAnalid().toString());
//					}
					
					//查询发起转发的人
					List<TblMyTask> listMyTask = tblMyTaskMapper.selectByZFForm(cyId, user.getStaffid().toString());
					
					System.out.println("发起转发的人============================="+listMyTask.toString());
					
					if(null!= listMyTask  && listMyTask.size()>0) {
						TblMyTask zfFormTask = listMyTask.get(0);
						task.setHandle(zfFormTask.getApprover());
					}
					
					tblMyTaskService.updateSetting(task);
					
					//更新合同状态
					aw.setContractstatus(TblCswlManagement.STATE_SP);
					tblCyhwUnitService.updateCyhwUnitNoDescrpib(aw);
					
					resultMap.put("code", "1");
		              resultMap.put("msg", "办理成功！");
		              JSONObject jsonObj = new JSONObject(resultMap);
					 return jsonObj.toString();
					 
				}else {
					resultMap.put("code", "0");
		              resultMap.put("msg", "办理失败！");
		              JSONObject jsonObj = new JSONObject(resultMap);
					 return jsonObj.toString();
				}
			}
		} catch (Exception e) {
			log.error("异常信息：", e);
			 resultMap.put("code", "0");
			 resultMap.put("msg", "办理失败！");
			 JSONObject jsonObj = new JSONObject(resultMap);
			 return jsonObj.toString();
		}
	}
	
	
	@ApiOperation(value="合同借阅--办理流程")
	@RequestMapping(value = "/cyhw/blprocesshtjy",produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	public String blprocesshtjy(HttpServletRequest request,
			@ApiParam(name="lendid",value="lendid",required=true)@RequestParam(name="lendid",required=true)String lendid,
			@ApiParam(name="taskId",value="taskId",required=true)@RequestParam(name="taskId",required=true)String taskId,
			@ApiParam(name="processDefinitionId",value="processDefinitionId",required=true)@RequestParam(name="processDefinitionId",required=true)String processDefinitionId,
			@ApiParam(name="processInstanceId",value="processInstanceId",required=true)@RequestParam(name="processInstanceId",required=true)String processInstanceId,
			@ApiParam(name="transitionName",value="按钮完成，退回",required=true)@RequestParam(name="transitionName",required=true)String transitionName,
			@ApiParam(name="examination",value="审批意见",required=true)@RequestParam(name="examination",required=true)String examination,
			@ApiParam(name="returnDate",value="归还时间",required=true)@RequestParam(name="returnDate",required=true)String returnDate,
			@ApiParam(name="memo",value="借阅详情",required=true)@RequestParam(name="memo",required=true)String memo,
			@ApiParam(name="cyId",value="cyId",required=true)@RequestParam(name="cyId",required=true)String cyId,
			@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception{
		
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		String orgid = null;
		TblStaffUtil user = DealUserToken.parseUserToken(token);
			if (user == null) {
				resultMap.put("code", "0");
				resultMap.put("msg", "用户已失效！");
				JSONObject jsonObj = new JSONObject(resultMap);
				return jsonObj.toString();
			}
			orgid = user.getCurrentOrg().getOrgid().toString();
   		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
		try {
			TblContractLend aw = tblContractLendService.findById(lendid);
			aw.setReturndate(simpleDateFormat.parse(returnDate));//归还日期
			aw.setMemo(memo);
			if(taskId!=null && !taskId.equals("")){
				Map<String, Object> map = null;
				if(!"完成".equals(transitionName) && !"退回".equals(transitionName) ) {
					map = HttpClient.handleProcessJson(user.getStaffid().toString(),"通过",taskId);
				}else {
					 map = HttpClient.handleProcessJson(user.getStaffid().toString(),transitionName,taskId);
				}
				
				String object = (String) map.get("result");
				if(object!=null && object.equals("true")){
					TblMyTask task=new TblMyTask();
					TblMyTask oldtask = tblMyTaskService.findOndbyFrom(lendid.toString());
					TblProcessAnalysis findOnd = null;
					if(oldtask==null){
						findOnd = tblProcessAnalysisService.findOndBytakdid("");
					}else{
						findOnd= tblProcessAnalysisService.findOnd(oldtask.getAnalid());
					}
					Integer number=1;
					String usertaskid = findOnd.getUsertaskid();
					Integer num=Integer.parseInt(usertaskid.substring(usertaskid.length()-1,usertaskid.length()))+number;
					String blande=usertaskid.substring(0,usertaskid.length()-1)+num;
					TblProcessAnalysis analysis =null;
					if(transitionName!=null && "退回".equals(transitionName)){
						analysis = tblProcessAnalysisService.findOndBytakdidstart("",findOnd.getProcessname());
					}else{
						analysis = tblProcessAnalysisService.findOndBytakdidAnId(blande,findOnd.getProcessname());
					}
					TblProcessAnalusisUser analysisUser = null;
					if(analysis!=null && analysis.getAnalid()!=null){
						analysisUser = tblProcessAnalusisUserService.findOnd(analysis.getAnalid().toString(), lendid);
					}
					TblCirculation circulation = tblCirculationService.getOneBytaskid(lendid);
					//查询执行人
					String nextapprover = HttpClient.nextapprover(circulation.getBusinesskey());
					System.out.println("nextapprover===================:"+nextapprover);
					
					task.setApprovaldate(new Date());
					if(user.getTrole()!=null && user.getTrole().getRname()!=null){
						task.setApprovalrole(user.getTrole().getRname());
					}
					task.setApprover(user.getRealname());
					task.setExamination(examination);
					task.setFromid(lendid.toString());
					task.setProcessDefinitionId(processDefinitionId);
					task.setUsrid(user.getStaffid().toString());
					task.setCirid(cyId);
					task.setResult(transitionName);
					task.setProcessName(oldtask.getProcessName());
					task.setTaskId(taskId);
					task.setProcessInstanceId(processInstanceId);
					if(transitionName.equals("完成")){
						task.setHandle("无");
						circulation.setCystate("已完成");
						//完成合同
						aw.setLendstatus(TblContractLend.STATE_WC);
					}else{
						TblStaff findById = tblStaffService.findById(circulation.getCystaffid());//表单提交人
						if(nextapprover.contains("bmfzr")) {
							TblStaff bmfzr = tblStaffService.findByJobName("部门负责人", findById.getOrgid().toString());
							task.setHandle(bmfzr.getRealname());
						}else if(nextapprover.contains("fgld")) {
							//分管领导参数
							TblStaff fgld = tblStaffService.findByStaffManOrgs(findById.getOrgid().toString());
							task.setHandle(fgld.getRealname());
						}else if(nextapprover.contains("tcuserid")) {
							//退回到创建人
							task.setHandle(findById.getRealname());
						}else {
							task.setHandle(nextapprover);//角色
						}
						task.setAnalid(analysis.getAnalid().toString());
					}
					
					if(aw.getLendstatus()==2){
						aw.setLendstatus(TblContractLend.STATE_SP);
						circulation.setCystate("审批中");
					}
					if(transitionName!=null && transitionName.equals("退回")){
						circulation.setCystate("需调整");
						aw.setLendstatus(TblContractLend.STATE_TZ);
					}
					
					if(transitionName!=null && transitionName.equals("终止")){
						circulation.setCystate("终止");
						task.setHandle("无");
						aw.setLendstatus(TblContractLend.STATE_ZZ);
					}
					tblMyTaskService.insertMyTaskSetting(task);
					tblCirculationService.upateTblCirculation(circulation);
					//tblContractLeadService.modifyLendStatus(lendid.toString(), aw.getLendstatus());
					this.tblContractLendService.saveTblContractLeadEntity(aw);
				}
				
				return JsonBean.success();
			}else{
				return JsonBean.error("办理失败");
			}
		} catch (Exception e) {
			log.error("异常信息：", e);
			return JsonBean.error("办理失败");
		}
	}
	
	private void bljc(HttpServletRequest request,BigDecimal contractId, String orgid, String usertaskid,TblMyTask task){
		String taskId=request.getParameter("taskId");
		//String processDefinitionId=request.getParameter("processDefinitionId");
		//String processInstanceId=request.getParameter("processInstanceId");
		String cyId=request.getParameter("cyId");
		String transitionName=request.getParameter("transitionName");
		TblStaff user = tblStaffService.findByRolename("基建科科长", orgid);
		try {
			TblCyhwUnit aw = tblCyhwUnitService.getEntity(contractId);
			TblCirculation cy = tblCirculationService.getOneBytaskid(contractId.toString());
			List<TblMyTask> list =null;
			if(user.getTrole()!=null && user.getTrole().getRname()!=null){
				list= HttpClient.findByTask(user.getTrole().getRname(), user.getStaffid().toString(), 1, 10000);
			}
			if(list!=null && list.size()>0){

				for (TblMyTask myTask : list) {
					if(cy!=null && myTask.getProcessInstanceId().equals(cy.getBusinesskey())){
						taskId=myTask.getTaskId();
						break;
					}
				}
			}
			if(taskId!=null && !taskId.equals("")){


				Map<String, Object> map = HttpClient.handleProcessJson(user.getStaffid().toString(),transitionName,taskId);
				String object = (String) map.get("result");
				if(object!=null && object.equals("true")){
					//MyTask task=new TblMyTask();
					TblMyTask oldtask = tblMyTaskService.findOndbyFrom(contractId.toString());
					//TblAnalysis ond = tblProcessAnalusisUserService.findOnd(oldtask.getAnalid());
					TblProcessAnalysis findOnd = null;
					if(oldtask==null){
						findOnd = tblProcessAnalysisService.findOndBytakdid("");
					}else{
						findOnd = tblProcessAnalysisService.findOnd(oldtask.getAnalid());
					}
					Integer number=1;
					//String usertaskid = findOnd.getUsertaskid();
					Integer num=Integer.parseInt(usertaskid.substring(usertaskid.length()-1,usertaskid.length()))+number;
					String blande=usertaskid.substring(0,usertaskid.length()-1)+num;
					TblProcessAnalysis analysis =null;
					analysis = tblProcessAnalysisService.findOndBytakdidAnId(blande,findOnd.getProcessname());
					TblProcessAnalusisUser analysisUser = null;
					if(analysis!=null && analysis.getAnalid()!=null){
						analysisUser = tblProcessAnalusisUserService.findOnd(analysis.getAnalid().toString(), contractId.toString());
					}
					TblCirculation circulation = tblCirculationService.get(cyId);
					//task.setApprovaldate(new Date());
					//task.setApprovalRole(user.getTblRole().getRname());
					//task.setApprover(user.getRealname());
					//task.setFromid(contractId.toString());
					//task.setProcessDefinitionId(processDefinitionId);
					//task.setUsrid(user.getStaffid().toString());
					task.setCirid(cyId);
					//task.setResult(transitionName);
					//task.setProcessName(analysis.getProcessName());
					//task.setTaskId(taskId);
					//task.setProcessInstanceId(processInstanceId);
					if(analysis==null || analysisUser==null){
						task.setHandle("无");
						circulation.setCystate("已完成");
						aw.setContractstatus(TblCswlManagement.STATE_WC);
					}else{
						if(transitionName!=null && transitionName.equals("完成")){
							task.setHandle("无");
							circulation.setCystate("已完成");
							//此处调用上上签电子签章
							aw.setContractstatus(TblCswlManagement.STATE_WC);
						}else {
							task.setHandle(analysisUser.getStaffid());
							task.setAnalid(analysis.getAnalid().toString());
						}
					}


					tblMyTaskService.updateSetting(task);
					tblCirculationService.upateTblCirculation(circulation);
					tblCyhwUnitService.updateCyhwUnitNoDescrpib(aw);
				}
			}
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
	}
	
	
	/**
	 * 审批-附件列表
	 * @return
	 */
	@RequestMapping(value = "/cyhw/getAprAttList",method = {RequestMethod.POST},produces = "application/html; charset=utf-8")
	@ApiOperation(value="审批-附件列表")
	public String getAprAttList(@ApiParam(value="id",required=true)Integer id){
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblAttachmentService.findAttachmentListByMyTaskId(id);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}
	/**
	 * 审批-附件删除
	 */
	@RequestMapping(value = "/cyhw/delAprAtt", method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "审批-附件删除")
	public @ResponseBody String deleteFileRelation(HttpServletRequest request,
												   @ApiParam(value="attid",required=true)Integer attid){
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblMyTaskService.delAprAttById(attid);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}


	@RequestMapping(value = "/oppositePartyMaintenance",method = {RequestMethod.POST} )
	@ApiOperation(value="相对方维护列表页面")
	public String oppositePartyMaintenance(HttpServletRequest request,
			@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
			@ApiParam(name="flowId",value="流程Id主键",required=true)String flowId,
			@ApiParam(name="staffId",value="用户Id主键",required=false)String staffId,
			@ApiParam(name="currentPage",value="当前页",required=false)Integer pageNumber,
			@ApiParam(name="pageSize",value="每页数量",required=false)Integer pageSize
			,TblCyhwProjectbudget budget){
		String result = null;
		try {
			if(pageNumber == null) {
				pageNumber = 1;
			}
			Map<String,Object>  resultMap = this.tblCyhwProjectbudgetService.findBudgetListByStaffOrg(token,flowId,staffId,budget,pageNumber,pageSize);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	/**
	 * 加载相对方新增页面前置条件
	 * @param request
	 * @param flowId
	 * @param token
	 * @param contractId
	 * @param choiceSearch
	 * @param staffId
	 * @return
	 */
	@RequestMapping(value="/oppositePartyToAddPage",method = {RequestMethod.POST},produces = "application/html; charset=utf-8")
	@ApiOperation(value="加载相对方新增页面前置条件")
	public String oppositePartyToAddPage(HttpServletRequest request,@ApiParam(name="staffId",value="流程Id主键",required=true)String flowId,@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
			@ApiParam(name="contractId",value="合同用印查询合同信息",required=false)Integer contractId,@ApiParam(name="choiceSearch",value="用来判断查询显示还是隐藏默认值hide",required=false)String choiceSearch,@ApiParam(name="staffId",value="当前登录用户Id",required=false)String staffId) {
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblCyhwProjectbudgetService.loadAddOppositePartyInfo(flowId,contractId,choiceSearch,token,staffId);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}




	@RequestMapping(value = "/saveOppositeParty",method = {RequestMethod.POST})
	@ApiOperation(value="保存相对方用户信息")
	public String oppositePartySave(HttpServletRequest request,
			@ApiParam(name="flowId",value="流程主键Id",required=true)String flowId,TblCyhwProjectbudget tcpb,
			@ApiParam(name="linkDeptId",value="关联部门Id",required=false)String linkDeptId,
			@ApiParam(name="createDate",value="创建日期",required=false)String createDate,
			@ApiParam(name="pstartDate1",value="证件有效期开始日期",required=false)String pstartDate1,
			@ApiParam(name="pendDate1",value="证件有效期结束日期",required=false)String pendDate1,
			@ApiParam(name="recortParent",value="关联合同Id",required=false)String recortParent,
			@ApiParam(name="contractDeptId",value="报送科室单位",required=false)String contractDeptId,
			@ApiParam(name="bgDeptId",value="保管部门Id",required=false)String bgDeptId,
			@ApiParam(name="bgstaffid",value="保管人Id",required=false)String bgstaffid,
			@ApiParam(name="pageEffectDate",value="保管有效期开始日期",required=false)String pageEffectDate,
			@ApiParam(name="pageSafeDate",value="保管有效期结束日期",required=false)String pageSafeDate,
			@ApiParam(name="attids",value="附件Id数组",required=false)String attids,
			@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
			@ApiParam(name="staffId",value="登录用户Id",required=false)String staffId){
		String result = null;
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

			if(createDate != null && !"".equals(createDate)){
				tcpb.setCreatetime(sdf.parse(createDate));
			}else{
				tcpb.setCreatetime(new Date());
			}
			//证件有效期
			if(pstartDate1 != null && !"".equals(pstartDate1)){
				tcpb.setPstartdate(sdf.parse(pstartDate1));
			}
			if(pendDate1 != null && !"".equals(pendDate1)){
				tcpb.setPenddate(sdf.parse(pendDate1));
			}

			if(recortParent != null && !"".equals("recortParent")){
				tcpb.setRecordparent(Integer.parseInt(recortParent));
			}


			if(linkDeptId != null && !"".equals(linkDeptId)){
				tcpb.setLinkdepr(Integer.parseInt(linkDeptId));
			}

			if(contractDeptId != null && !"".equals(contractDeptId)){
				tcpb.setReporttodept(Integer.parseInt(contractDeptId));
			}

			if(bgDeptId != null && !"".equals(bgDeptId)){
				tcpb.setSafeorg(Integer.parseInt(bgDeptId));
			}

			if(bgstaffid != null && !"".equals(bgstaffid)){
				tcpb.setSafestaff(Integer.parseInt(bgstaffid));
			}

			if(pageEffectDate != null && !"".equals(pageEffectDate)){
				tcpb.setEffectdate(sdf.parse(pageEffectDate));
			}

			if(pageSafeDate != null && !"".equals(pageSafeDate)){
				tcpb.setSafedate(sdf.parse(pageSafeDate));
			}
			tcpb.setInspectionstatus(0);//设置相对方信息基础状态  未审批
			Map<String,Object> resultMap =  this.tblCyhwProjectbudgetService.SaveOppositeParty(tcpb,attids,staffId,token,flowId);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	@ApiOperation(value="相对方管理删除资质文件")
	@RequestMapping(value = "/removeOppsiteFile",produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	public String removeOppsiteFile(HttpServletRequest request,@ApiParam(name="attid",value="附件ID",required=true)String attid){
		try {
			this.tblCyhwProjectbudgetService.removeOppsiteFile(attid);
			return JsonBean.success();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return JsonBean.error("删除失败");
	}

	/**
	 * 获取相对方信息状态
	 * @param request
	 * @param budgetId
	 * @return
	 */
	@RequestMapping(value="/getProjectrBudgetStatue",produces = "application/html; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="获取相对方信息状态")
	public String getProjectrBudgetStatue(HttpServletRequest request,@ApiParam(name="budgetId",value="相对方信息主键",required=true)Integer budgetId){
		try {
			Integer statue = this.tblCyhwProjectbudgetService.findStatueById(budgetId);
			if(statue!=null && statue==1){
				return JsonBean.error("流程审批中");
			}else if(statue!=null && statue==3){
				return JsonBean.error("流程已通过");
			}else if((statue!=null && statue==4) ||(statue!=null && statue==5) || (statue!=null && statue==6)){
				return JsonBean.error("流程已完成");
			}else{
				return JsonBean.success();
			}

		} catch (Exception e) {
			log.error("异常信息：", e);
			return JsonBean.error("失败");
		}

	}

	/**
	 *相对方信息修改加载相对方信息
	 * @param request
	 * @param flowId
	 * @param budgetId
	 * @param token
	 * @param staffId
	 * @return
	 */
	@RequestMapping(value = "/findOppsiteInfo",method = {RequestMethod.POST},produces = "application/html; charset=utf-8")
	@ApiOperation(value="相对方信息修改加载相对方信息")
	public String findOppsiteInfo(HttpServletRequest request,
								  @ApiParam(name="flowId",value="流程信息主键",required=true)String flowId
			,@ApiParam(name="budgetId",value="相对方信息主键",required=true)Integer budgetId,
								  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
								  @ApiParam(name="staffId",value="登录用户Id",required=false)String staffId){
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblCyhwProjectbudgetService.findOppsiteAllInfoById(flowId,budgetId,token,staffId);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}

		return result;
	}

	@RequestMapping(value = "/oppositePartyMenger",method = {RequestMethod.POST})
	@ApiOperation(value="保存修改后的相对方信息")
	public String oppositePartyMenger(HttpServletRequest request,
									  @ApiParam(name="flowid",value="流程信息主键",required=true)String flowId,TblCyhwProjectbudget tcpb,
									  @ApiParam(name="linkDeptId",value="关联部门Id",required=false)Integer linkDeptId,
									  @ApiParam(name="itemtype",value="itemtype",required=false)String itemtype,
									  @ApiParam(name="pstartDate1",value="证件有效期开始日期",required=false)String pstartDate1,
									  @ApiParam(name="pendDate1",value="证件有效期结束日期",required=false)String pendDate1,
									  @ApiParam(name="recortParent",value="合同主键ID",required=false)String recortParent,
									  @ApiParam(name="contractDeptId",value="关联科室Id",required=false)Integer contractDeptId,
									  @ApiParam(name="bgDeptId",value="保管部门ID",required=false)String bgDeptId,
									  @ApiParam(name="bgstaffid",value="保管用户ID",required=false)String bgstaffid,
									  @ApiParam(name="pageEffectDate",value="档案有效期开始日期",required=false)String pageEffectDate,
									  @ApiParam(name="pageSafeDate",value="档案有效期结束日期",required=false)String pageSafeDate,
									  @ApiParam(name="attids",value="附件ID数组",required=false)String attids,
									  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
									  @ApiParam(name="staffId",value="登录用户Id",required=false)String staffId){
		String result = null;
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

			tcpb.setReporttodept(contractDeptId);
			tcpb.setLinkdepr(linkDeptId);
			if(recortParent != null && !"".equals(recortParent)){
				tcpb.setRecordparent(Integer.parseInt(recortParent));
			}
			if(bgDeptId != null && !"".equals(bgDeptId)){
				tcpb.setSafeorg(Integer.parseInt(bgDeptId));
			}
			if(bgstaffid != null && !"".equals(bgstaffid)){
				tcpb.setSafestaff(Integer.parseInt(bgstaffid));
			}
			if(pageEffectDate != null && !"".equals(pageEffectDate)){
				tcpb.setEffectdate(sdf.parse(pageEffectDate));
			}
			if(pageSafeDate != null && !"".equals(pageSafeDate)){
				tcpb.setSafedate(sdf.parse(pageSafeDate));
			}
			if(pstartDate1 != null && !"".equals(pstartDate1)){
				tcpb.setPstartdate(sdf.parse(pstartDate1));
			}
			if(pendDate1 != null && !"".equals(pendDate1)){
				tcpb.setPenddate(sdf.parse(pendDate1));
			}
			Map<String,Object> resultMap = this.tblCyhwProjectbudgetService.mengerOppsitePartyById(tcpb,token,staffId,attids);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	/**
	 * 删除相对方信息
	 * @param request
	 * @param flowId  流程信息主键
	 * @param token
	 * @param staffId
	 * @param budgetId 删除的相对方信息主键
	 * @return
	 */
	@RequestMapping(value = "/removeOppsiteParty",method = {RequestMethod.POST})
	@ApiOperation(value="删除相对方信息")
	public String removeOppsitepart(HttpServletRequest request,@ApiParam(name="flowid",value="流程信息主键",required=true)String flowId,
									@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
									@ApiParam(name="staffId",value="登录用户ID",required=false)String staffId,
			@ApiParam(name="budgetId",value="删除的相对方信息主键",required=true)String budgetId) {
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblCyhwProjectbudgetService.removeOppsitePartyInfo(flowId,token,staffId,budgetId);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}
	
	@RequestMapping(value = "/removeOppsitePartyNoLc",method = {RequestMethod.POST})
	@ApiOperation(value="删除相对方信息--无流程版本")
	public String removeOppsitePartyNoLc(HttpServletRequest request,
									@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
									@ApiParam(name="staffId",value="登录用户ID",required=false)@RequestParam(value="staffId",required=false)String staffId,
			@ApiParam(name="budgetId",value="删除的相对方信息主键",required=true)@RequestParam(value="budgetId",required=false)String budgetId) {
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblCyhwProjectbudgetService.removeOppsitePartyInfoNoLc(token,staffId,budgetId);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}
	
	/**
	 *相对方提交审批、合同用印提交审批
	 * @param request
	 * @param budgetId
	 * @param examination
	 * @param token
	 * @param staffId
	 * @param flowId
	 * @return
	 */
	@ApiOperation(value="相对方提交审批、合同用印提交审批")
	@RequestMapping(value="/submitOppsitePartApproval",produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	public String submitOppsitePartApproval(HttpServletRequest request,
			@ApiParam(name="budgetId",value="提交相对方信息主键",required=true)@RequestParam(value = "budgetId",required = false)String budgetId,
			@ApiParam(name="token",value="登录用户token",required=true)@RequestHeader("token") String token,
			@ApiParam(name="flowId",value="隶属流程信息主键",required=true)@RequestParam(value = "flowId",required = false)String flowId) {
		String result = null;
		try {
			Map<String,Object> resultMap = this.tblCyhwProjectbudgetService.submitOppsitePartApproval(budgetId,token,null,null,flowId);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
			
			TblCyhwProjectbudget entity = tblCyhwProjectbudgetService.findBudgetid(Integer.parseInt(budgetId));
			if(entity.getRecordparent()!=null) {
		    	String path = PropertyFileReader.getItem("file.path");
				TblCyhwUnit tcu = tblCyhwUnitService.getEntity(new BigDecimal(entity.getRecordparent()));
           	 	String fileName = "合同签章附件.pdf";
           	 	Integer conFileCount = this.tblContractAppendixsigningMapper.selectCountByContractId(tcu.getContractid(),1);
           	 	
           	 	
           	 	if(tcu.getDescribe()!=null && tcu.getDescribe()!="" && conFileCount == 0) {
           	 		
           	 		
           	 		fileName=tcu.getContractname()+".docx";
					Map<String, String> map = new HashMap<String,String>();
					logger.error("PDF转换替换前代码------>"+tcu.getDescribe());
					String describeStr = AsposeWordExtUtil.regexRep(tcu.getDescribe());
					logger.error("PDF转换替换后代码------>"+describeStr);
					map.put("repdesc",AsposeWordExtUtil.regexRep(tcu.getDescribe()));
					FreeMarkerUtil.createHtml(freeMarkerConfig, "static.ftl", request,map, path, fileName);
					String outfile=tcu.getContractname()+".pdf";
					File file = new File(path+ "/" +outfile); // 新建一个空白pdf文档
				    FileOutputStream os = new FileOutputStream(file);
				    Document doc = new Document(path+ "/" +fileName); // Address是将要被转化的word文档
				    
			        Shape watermark = new Shape(doc, ShapeType.TEXT_PLAIN_TEXT);
			        
			        //水印内容
			        watermark.getTextPath().setText("浙资运营");
			        //水印字体
			        watermark.getTextPath().setFontFamily("宋体");
			        //水印宽度
			        watermark.setWidth(500);
			        //水印高度
			        watermark.setHeight(100);
			        //旋转水印
			        watermark.setRotation(-40);
			        //水印颜色
			        watermark.getFill().setColor(Color.lightGray); 
			        watermark.setStrokeColor(Color.lightGray); 
			        
			        watermark.setRelativeHorizontalPosition(RelativeHorizontalPosition.PAGE);
			        watermark.setRelativeVerticalPosition(RelativeVerticalPosition.PAGE);
			        watermark.setWrapType(WrapType.NONE);
			        watermark.setVerticalAlignment(VerticalAlignment.CENTER);
			        watermark.setHorizontalAlignment(HorizontalAlignment.CENTER);
			 
			        Paragraph watermarkPara = new Paragraph(doc);
			        watermarkPara.appendChild(watermark);
			        //水印添加到内容中
			        for (Section sect : doc.getSections())
			        {
			            insertWatermarkIntoHeader(watermarkPara, sect, HeaderFooterType.HEADER_PRIMARY);
			            insertWatermarkIntoHeader(watermarkPara, sect, HeaderFooterType.HEADER_FIRST);
			            insertWatermarkIntoHeader(watermarkPara, sect, HeaderFooterType.HEADER_EVEN);
			        }
			        System.out.println("添加水印");
				    
				    doc.save(os, SaveFormat.PDF);
				    os.close();
				    System.out.println("pdf生成成功");
				    fileName=tcu.getContractname()+".pdf";
				    
				    path=path+ "/" +fileName;
					//String fileName = "合同签章附件.pdf";
					long timeInMillis = Calendar.getInstance().getTimeInMillis();
					String oldname = fileName.substring(0,fileName.lastIndexOf("."));
					String newname=fileName.replace(oldname, timeInMillis+"");
					InputStream  input = new FileInputStream(path)  ; 
					boolean flag = FtpUtil.constractUploadFile(newname, input);
			    	if(flag){
			    		 logger.info("上传成功");
			    	}else{
			    		 logger.info("上传失败");
			    	}
			    	TblStaffUtil loginStaff = DealUserToken.parseUserToken(token);
			    	/*TblAttachment att=new TblAttachment();
			    	att.setAttname(fileName);
			    	att.setAttpath(newname);
			    	att.setUploader(loginStaff.getRealname());
			    	att.setAttsize(file.length());
			    	att.setUploadtime(new Date());
			    	tblAttachmentService.add(att);*/
			    	
			    	TblContractAppendixsigning signing = new TblContractAppendixsigning();
			 	    signing.setConstractId(tcu.getContractid());
			 	    signing.setSingingName(fileName);
			 	    signing.setSingingSize(new BigDecimal(file.length()));
			 	    signing.setSingingType(1);
			 	    signing.setSingingStatus(0);
			 	    signing.setSingingPath(newname);
			 	    signing.setUploader(loginStaff.getStaffid().intValue());
			 	    signing.setUploadTime(new Date());
			    	this.tblContractAppendixsigningMapper.saveEntity(signing);
			 	    
			    	entity.setEqbflowid(signing.getSingingId().toString());
			    	tblCyhwProjectbudgetService.updateTcb(entity);
			    	//this.tblCyhwProjectbudgetMapper.insertAttmentRelation(signing.getSingingId().toString(),entity.getBudgetid());
			    	
				}
           }
          
			
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;


	}
	
	  private static void insertWatermarkIntoHeader(Paragraph watermarkPara, Section sect, int headerType) throws Exception
	    {
	        HeaderFooter header = sect.getHeadersFooters().getByHeaderFooterType(headerType);
	 
	        if (header == null)
	        {
	            header = new HeaderFooter(sect.getDocument(), headerType);
	            sect.getHeadersFooters().add(header);
	        }
	 
	        header.appendChild(watermarkPara.deepClone(true));
	    }

	  
	  
	  
	  
	  @RequestMapping(value = "/getattInfo",method = {RequestMethod.POST},produces = "application/html; charset=utf-8")
		@ApiOperation(value="根据合同id获取生成水印文件信息")
		public String getattInfo(HttpServletRequest request,@ApiParam(name="contractId",value="合同主键",required=true)Integer contractId,
									  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token){
			String result = null;
			try {
				Map<String,Object>  resultMap=tblAttachmentService.findAttachmentListByhtId(contractId);
				JSONObject jsonObj = new JSONObject(resultMap);
				result = jsonObj.toString();
			} catch (Exception e) {
				log.error("异常信息：", e);
			}

			return result;
		}
	  
	  
	  
	/**相对方管理加入黑名单
	 *
	 * @param request
	 * @param blackType 黑名单类型
	 * @param budgetId 想对方主键
	 * @param datetext  黑名单有效期
	 * @param token
	 * @param staffId
	 * @return
	 */
	@RequestMapping(value="/saveOppsitePartyBlack",produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="相对方管理加入黑名单")
	public String saveOppsitePartyBlack(HttpServletRequest request,
										@ApiParam(name="blackType",value="黑名单类型",required=true)@RequestParam(value="blackType",required=true)Integer blackType,
										@ApiParam(name="budgetId",value="想对方主键",required=true)@RequestParam(value="budgetId",required=true)Integer budgetId,
										@ApiParam(name="datetext",value="黑名单有效期",required=false)@RequestParam(value="datetext",required=false)String datetext,
										@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
										@ApiParam(name="staffId",value="登录用户主键",required=false)@RequestParam(value="staffId",required=false)String staffId) {
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblCyhwProjectbudgetService.saveOppsitePartyBlack(budgetId,token,staffId,blackType,datetext);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}


	@RequestMapping(value = "/oppsiteWarningList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")///cwgl/xdf_warningList
	@ApiOperation(value="相对方预警列表页面")///cwgl/xdf_warningList老项目
	public  String xdf_warningList(HttpServletRequest request,
								   @ApiParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
								 @ApiParam(name="pageSize",value="每页数量",required=false)Integer pageSize,
								   TblCyhwProjectbudget budget,
								   @RequestParam(value = "cateId",required = false)Integer cateId,
								   @RequestParam(value = "isFlowdb",required = false)Integer isFlowdb,
								   @RequestParam(value = "view",required = false)Integer view,
								   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
								   @ApiParam(name="staffId",value="登录用户主键",required=false)String staffId){
		String result = null;
		try {

			Map<String,Object>  resultMap = this.tblCyhwProjectbudgetService.finOppsiteWarningList(pageNumber,budget,isFlowdb,"HTGL001",view,pageSize,staffId,token);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}




	@RequestMapping(value = "/projectrBudgetDetail",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value="向对方预警-向对方名称查看")
	public String projectrBudgetDetail(HttpServletRequest request,
											 @RequestParam(value="flowid",required=true)BigDecimal flowid
											 ,@RequestParam(value="budgetid",required=true)Integer budgetid){
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblCyhwProjectbudget tcpb = tblCyhwProjectbudgetService.findBudgetid(budgetid);
			List<TblCyhwBudetgoods> goodsList = tblCyhwBudetgoodsService.findkFirstLevel();
			List<TblCyhwBudetgoods> goodsListCh = null;

			if(tcpb.getRecordparent() != null){
				TblCyhwUnit tcu = tblCyhwUnitService.getEntity(new BigDecimal(tcpb.getRecordparent()));
				resultMap.put("tcu", tcu);
			}
			//相对方外键
			if(tcpb.getRecordconcat() != null){
				TblCyhwProjectbudget tcpb1 = tblCyhwProjectbudgetService.findBudgetid(tcpb.getRecordconcat());
				resultMap.put("tcpb1", tcpb1);
			}
			if(tcpb.getGoodstype() != null){
				resultMap.put("choice",0);
				goodsListCh = tblCyhwBudetgoodsService.findkListByParentId(tcpb.getGoodstype());
			}else{
				resultMap.put("choice",1);
			}
			String searchUrl = this.tblFlowService.findMappingUrl(flowid);
			resultMap.put("searchUrl",searchUrl);
			resultMap.put("tcpb", tcpb);
			String flowname = request.getParameter("flowname");
			resultMap.put("flowname",flowname);
			resultMap.put("goodsList", goodsList);
			resultMap.put("goodsListCh", goodsListCh);
			resultMap.put("choiceSearch", request.getParameter("choiceSearch"));
			resultMap.put("flowid",flowid);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}

	@RequestMapping(value = "/contract/contractByList", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="相对方预警-合同签订数量")
	public String contractByList(Model model,HttpServletResponse response,HttpServletRequest request,
								 @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
								 @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
								 @RequestParam(value = "budgetid",required = false)String budgetid)throws Exception{

		PageInfo<TblCyhwUnit> pageInfo = new PageInfo<TblCyhwUnit>();

		String result = null;
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		Map<String,Object>  resultMap = this.tblCyhwUnitService.findListByXdf(pageInfo,budgetid);
		JSONObject jsonObj = new JSONObject(resultMap);
		result = jsonObj.toString();
		return result;
	}

	/**
	 * 相对方预警-异常履约数量
	 * @param model
	 * @param response
	 * @param request
	 * @param pageNumber
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/contract/abnormalPerformance", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="相对方预警-异常履约数量")
	public String abnormalPerformance(Model model,
									  HttpServletResponse response,
									  HttpServletRequest request,
									  @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
									  @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
									  @RequestParam(value = "budgetid",required = false)String budgetid
	)throws Exception{
		String result = null;
		PageInfo<TblContractSpnode> pageInfo = new PageInfo<TblContractSpnode>();
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		Map<String,Object>  resultMap =tblContractSpnodeService.findListByXdf(pageInfo,budgetid);
		JSONObject jsonObj = new JSONObject(resultMap);
		result = jsonObj.toString();
		return result;
	}
	@RequestMapping(value = "/oppsitePartyBlackList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value="相对方黑名单管理列表")///nbkz/cwgl/hmd_list
	public String oppsitePartyBlackList(HttpServletRequest request,
										TblCyhwProjectbudget tcbp,
										@RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
										@RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
										@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
			@ApiParam(name="staffId",value="登录用户主键",required=false)String staffId,
				@ApiParam(name="choiceSearch",value="判断查询框显示还是隐藏默认hide",required=false)String choiceSearch,
										@ApiParam(name = "choose",required = false)String choose){
		String result = null;
		try {

			Map<String,Object>  resultMap = this.tblCyhwProjectbudgetService.findOppsiteBlackList(pageNumber,tcbp,"HTGL001",pageSize,staffId,token,choose);
			if(choiceSearch == null || "".equals(choiceSearch)) {
				choiceSearch = "hide";
			}
			resultMap.put("choiceSearch", choiceSearch);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	/**
	 * 相对方管理-黑名单管理-相对方名称查看
	 * @param request
	 * @param flowid
	 * @param budgetid
	 * @return
	 */
	@RequestMapping(value = "/addOppsitePartyBlackList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("相对方管理-黑名单管理-相对方名称查看")
	public String addOppsitePartyBlackList(HttpServletRequest request,
			@RequestParam(value="flowid",required=true)BigDecimal flowid
			,@RequestParam(value="budgetid",required=true)Integer budgetid,
			@RequestParam(value = "flowname",required = false)String flowname){
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblCyhwProjectbudget tcpb = tblCyhwProjectbudgetService.findBudgetid(budgetid);
			String searchUrl = this.tblFlowService.findMappingUrl(flowid);
			if(searchUrl == null || "".equals(searchUrl)) {
				searchUrl = "/nbkz/cwgl/zcgl_main";
			}
			resultMap.put("searchUrl",searchUrl);
			//resultMap.put("/nbkz/contract/addOppsitePartyBlackList");
			resultMap.put("data", tcpb);
			//String flowname = request.getParameter("flowname");
			resultMap.put("flowname",flowname);
			resultMap.put("choiceSearch", request.getParameter("choiceSearch"));
			resultMap.put("flowid",flowid);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/downloadFtp/{type}", produces = "application/json; charset=utf-8")
	@ApiOperation("相对方预警-相对方名称-资质信息下载")
	public void downloadFtp(@PathVariable String type,BigDecimal id,
							HttpServletResponse response,HttpServletRequest request) {
		boolean bool =false;
		if(id!=null ){
			TblAttachment tblAttachment = this.tblAttachmentService.get(id);
			if(tblAttachment!=null){
				if(type!=null && type.equals("upload")){
					bool= FtpUtil.downUploadFile(tblAttachment, response);
				}
				if(type!=null && type.equals("uploadPython")){
					bool= FtpUtil.downUploadFilePython(tblAttachment, response);
				}
				if(type!=null && type.equals("send")){
					bool= FtpUtil.downloadSendFile(tblAttachment, response);
				}
				if(type!=null && type.equals("template")){
					bool= FtpUtil.downloadTemplateFile(tblAttachment, response);
				}
			}else{
				logger.info("附件不存在");
			}
		}else{
			if(type!=null && type.equals("czsc")){
				String fileName=request.getParameter("fileName");
				String name=request.getParameter("name");
				bool= FtpUtil.downUploadFile(name,fileName, response);
			}
		}
		if(bool){
			logger.info("下载成功");
		}else{
			logger.info("下载失败");
		}

	}

	@RequestMapping(value = "/removeBlackList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("相对方管理-黑名单管理-取消黑名单")
	public String removeBlackList(HttpServletRequest request
			, @RequestParam(value="budgetid",required=true)Integer budgetid){
		String result = String.valueOf(1);
		try {
			result = this.tblCyhwProjectbudgetService.removeOppsitePartyBlack(budgetid);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}


	@RequestMapping(value = "/cwgl/blacklist_export",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value="相对方黑名单导出接口")
	public String blacklist_export(HttpServletRequest request, HttpServletResponse response,
								   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		log.info("合同管理--相对方管理--黑名单管理---导出Excel");
		response.setContentType("application/binary;charset=UTF-8");
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();
		String result = null;
		try {
			response.setHeader("Content-Disposition", "attachment;filename=" + new String("黑名单管理".getBytes(),"UTF-8") + ".xlsx");
			ServletOutputStream outputStream = response.getOutputStream();
			List<TblCyhwProjectbudget> blacklistExport = this.tblCyhwProjectbudgetService.blacklistExport(orgid);
			if(blacklistExport == null) {
				return "导出失败";
			}
			String[] titles = {"相对方编号","相对方名称","法定代表人","内部单位","注册资本（万元）","是否加入黑名单","黑名单期限","黑名单有效期"};
			List<Object[]> blacklist = new ArrayList<Object[]>(0);
			Object[] objs = null;
			int i = 0;
			
			for (TblCyhwProjectbudget budget : blacklistExport) {
				objs = new Object[8];
				objs[0] = budget.getCounterpartno();
				objs[1] = budget.getBudgetname();
				objs[2] = budget.getProjectstagegoal();
				objs[3] = budget.getServicetype();
				objs[4] = budget.getTotaltmoney();
				objs[5] = budget.getFinancemoney();
				objs[6] = budget.getOthermoney();
				objs[7] = budget.getProjecttype();
				blacklist.add(objs);
			}
			ImportOrExportExcelUtil.exportExcel(titles, blacklist, outputStream, null);
		} catch (Exception e) {
			log.info("内控合规---问题汇总---缺陷管理---导出Excel失败");
			log.error("异常信息：", e);
		}
		return result;
	}

	/**相对方查看办理信息--合同用印查看办理信息
	 *
	 * @param request
	 * @param budgetId
	 * @param cateId
	 * @param v
	 * @param flowid
	 * @param tId
	 * @param token
	 * @param staffId
	 * @return
	 */
	@RequestMapping(value = "/viewOppsiteProcessInfo",method = {RequestMethod.GET})
	@ApiOperation(value="相对方查看办理信息、合同用印查看办理信息")
	public String viewOppsiteProcessInfo(HttpServletRequest request,@ApiParam(name="budgetId",value="相对方信息主键",required=true)String budgetId,
			@ApiParam(name="cateId",value="cateId",required=false)String cateId,@ApiParam(name="v",value="v",required=false)String v,
			@ApiParam(name="flowid",value="流程信息主键",required=true)String flowid,@ApiParam(name="tId",value="tId",required=false)String tId,
			@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,@ApiParam(name="staffId",value="登录用户主键",required=false)String staffId){
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblCyhwProjectbudgetService.findOppsiteProcessInfo(token,budgetId,cateId);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	@RequestMapping(value = "/viewOppsiteActiviti",method = {RequestMethod.GET})
	@ApiOperation(value="查看相对方流程审批信息")
	public String viewOppsiteActiviti(HttpServletRequest request,@ApiParam(name="budgetId",value="相对方信息主键",required=true)String budgetId,
			@ApiParam(name="view",value="view",required=false)String view,@ApiParam(name="businessKey",value="businessKey",required=false)String taskId,
			@ApiParam(name="flowid",value="流程信息主键",required=false)String flowid,@ApiParam(name="flowname",value="流程主键",required=false)String flowname,
			@ApiParam(name="cateId",value="cateId",required=false)String cateId,
			@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,@ApiParam(name="staffId",value="登录用户主键",required=false)String staffId) throws Exception {
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblCyhwProjectbudgetService.viewOppsiteActiviti(budgetId,view,taskId,flowid,flowname,cateId,staffId,token);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}


	@RequestMapping(value = "/blOppsiteProcessyszc",produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="办理相对方类型")
	public String blOppsiteProcessyszc(HttpServletRequest request,@ApiParam(name="budgetId",value="相对方信息主键",required=true)String budgetId,
			@ApiParam(name="taskId",value="taskId",required=false)String taskId,@ApiParam(name="processDefinitionId",value="processDefinitionId",required=false)String processDefinitionId,
			@ApiParam(name="processInstanceId",value="processInstanceId",required=false)String processInstanceId,@ApiParam(name="transitionName",value="transitionName",required=false)String transitionName,
			@ApiParam(name="examination",value="examination",required=true)String examination,@ApiParam(name="cyId",value="cyId",required=false)String cyId,
			@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,@ApiParam(name="staffId",value="登录用户主键",required=false)String staffId){
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblCyhwProjectbudgetService.blOppsiteProcessyszc(budgetId,taskId,processDefinitionId,processInstanceId,transitionName,examination,cyId,token,staffId);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}


	/**
	 * 合同管理模块上传附件接口
	 * @param request
	 * @param response
	 * @param map
	 * @param token
	 * @param staffId
	 * @return
	 */
	@RequestMapping(value = "/uploadFileAttInfo", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="合同管理模块上传附件接口")
	public String uploadFileAttInfo(HttpServletRequest request, HttpServletResponse response, Model map,MultipartFile file,
			@RequestParam(value="bid",required=false)Integer bid,
			@RequestParam(value="type",required=false)Integer type,
			@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,@ApiParam(name="staffId",value="登录用户主键",required=false)String staffId){
		String result = null;
		BigDecimal aid = null;
		// 创建一个通用的多部分解析器
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(request.getSession().getServletContext());
		// 判断 request 是否有文件上传,即多部分请求
		try {
			if (multipartResolver.isMultipart(request)) {
				// 转换成多部分request
				 MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
				 Map<String,Object> resultMap =  this.tblAttachmentService.uploadAttachment(multiRequest,token,staffId,file);
				 JSONObject jsonObj = new JSONObject(resultMap);
				 result = jsonObj.toString();
				 if(bid != null && type != null){
					 TblAttachment tblAttachment = (TblAttachment)resultMap.get("data");
					 aid = tblAttachment.getAttid();
					 tblLegalDisputregistrationService.saveBidType(type,bid,aid);
				 }

            }

		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	/**
	 * 附件查询"
	 * @param bid 业务id
	 * @param type 1-纠纷登记，2-协商过程，3-诉讼过程，4-仲裁过程
	 * @return
	 */
	@RequestMapping(value = "/contract/findAttacheMentListByBid", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="附件查询")
	public String findAttacheMentByBid(@RequestParam(value="bid",required=true)Integer bid,
												 @RequestParam(value="type",required=true)Integer type){

		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblLegalDisputregistrationService.findAttacheMentByBid(type,bid);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	/**
	 *
	 * @param aid 附件id
	 * @param type	1-纠纷登记，2-协商过程，3-诉讼过程，4-仲裁过程
	 * @return
	 */
	@RequestMapping(value = "/contract/deleAttacheMentByBid", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="附件删除")
	public String deleAttacheMentByBid(@RequestParam(value="aid",required=true)Integer aid,
									   @RequestParam(value="type",required=true)Integer type){

		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblLegalDisputregistrationService.deleteAttacheMentByBid(type,aid);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}


	/**
	 * 文件下载
	 * @param id
	 * @param httpServletResponse
	 * @return
	 */

	@ApiOperation(value = "下载", httpMethod = "POST", produces = "application/json")
	@RequestMapping(value = "/download", produces = "application/json; charset=utf-8",method = {RequestMethod.GET})
	public void fileDownLoad(@RequestParam("id") String id, HttpServletResponse httpServletResponse) {
		TblAttachment tblAttachmentEntity = tblAttachmentService.findById(id);
		FtpUtil.downUploadFile(tblAttachmentEntity,httpServletResponse);
		//return JsonBean.success();
	}

	/**
	 * 相对方维护-银行账户管理列表
	 * @param budgetId
	 * @param pageNumber
	 * @param pageSize
	 * @param bank
	 * @return
	 */
	@RequestMapping(value = "/contract/counterpartManageList", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="相对方维护-银行账户管理列表")
	public String contract_counterpartManageList(
													   @ApiParam(value="budgetId",required=true)Integer budgetId,
													   @ApiParam(value="pageNumber",required=false)Integer pageNumber,
													   @ApiParam(value="pageSize",required=false)Integer pageSize,
													   TblCounterpartBankinfo bank){
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblCounterpartBankInfoService.findAllListByBankInfo(budgetId,pageNumber,pageSize,bank);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	/**
	 * 相对方银行账户保存/修改方法
	 * @return
	 */
	@RequestMapping(value = "/contract/counterpartBankSave", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="相对方银行账户保存/修改方法")
	@ResponseBody
	public String contract_counterpartBankSave(HttpServletRequest request,
												@ApiParam(value="budgetId",required=true)Integer budgetId,
											   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
												TblCounterpartBankinfo bank){
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblCounterpartBankInfoService.saveCounterPartBankInfo(budgetId,token,bank);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	/**
	 *
	 * 相对方银行账户删除
	 * @param request
	 * @param bankId
	 * @return
	 */
	@RequestMapping(value = "/contract/counterpartBankRemove", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="相对方银行账户删除")
	@ResponseBody
	public String contract_counterpartBankRemove(HttpServletRequest request,
												 @ApiParam(value="bankId",required=true)String bankId){
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblCounterpartBankInfoService.removeBank(bankId);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	/**
	 * 相对方银行账户启用、弃用
	 * @param request
	 * @param bankId
	 * @param bankstatus
	 * @return
	 */
	@RequestMapping(value = "/contract/changeBankInfoStatus", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="相对方银行账户启用、弃用")
	@ResponseBody
	public String contract_changeBankInfoStatus(HttpServletRequest request,
												@ApiParam(value="bankId",required=true)String bankId,
												@ApiParam(value="statusValue",required=true)Integer bankstatus){
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblCounterpartBankInfoService.modifyBankStatus(bankId,bankstatus);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	/**
	 * 相对方监控列表
	 * @param pageNumber
	 * @param pageSize
	 * @param teamid
	 * @param fxtype
	 * @param companyname
	 * @param token
	 * @return
	 */
	@RequestMapping(value = "/fxyj/fxyj_list", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="相对方监控列表")
	public String fxyj_fxyj_list(@ApiParam(value="pageNumber",required=false)Integer pageNumber,
								 @ApiParam(value="pageSize",required=false)Integer pageSize,
								 @ApiParam(value="teamid",required=false)String teamid,
								 @ApiParam(name="fxtype",value="fxtype",required=false)String fxtype,
								 @ApiParam(name="companyName",value="companyName",required=false)String companyname,
								 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblyyxdfCompanyService.findByCompay(pageNumber,pageSize,teamid,fxtype,companyname,token);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}


	/**相对方监控新增、修改分组
	 *
	 * @param team
	 * @param token
	 * @return
	 */
	@RequestMapping(value = "/saveteam", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="相对方监控新增、修改分组")
	@ResponseBody
	public String saveteam(TblYyXdfTeam team,
						   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token){
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblYyXdfTeamService.saveOrupdateTeam(team,token);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	/**
	 *相对方监控分组列表
	 * @param token
	 * @return
	 */
	@RequestMapping(value = "/riskwarning/main", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="相对方监控分组列表")
	public String riskwarning_main(@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblYyXdfTeamService.findBYuseridAndCompanid(token);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	/**
	 * 相对方监控添加公司
	 * @param pageid
	 * @param priceid
	 * @param token
	 * @param company
	 * @return
	 */
	@RequestMapping(value = "/savecompany", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value="相对方监控添加公司")
	@ResponseBody
	public String savecompany(@ApiParam(name="pageid",value="pageid",required=false)String pageid,
							  @ApiParam(name="priceid",value="priceid",required=false)String priceid,
							  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
							  TblYyXdfCompany company){

		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblyyxdfCompanyService.saveOrupdateYYCompany(pageid,priceid,token,company);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}

	/**
	 * 合同管理-财务管理-收款管理-列表
	 * collectionOrgName :付款单位
	 * contractName：对应合同
	 * contractNo：合同编号
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/contract/collectionManagemen", produces = "application/json; charset=utf-8", method = {RequestMethod.POST})
	@ApiOperation(value = "合同管理-财务管理-收款管理-列表")
	public String contract_collectionManagemen(HttpServletResponse response,
											   HttpServletRequest request,
											   TblContractCollection collection,
											   @RequestParam(value="choiceSearch",required=false)String choiceSearch,
											   @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
											   @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
											   TblCyhwUnit unit,
											   @RequestParam(value = "flowid",required = false)String flowid,
											    @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
											   @RequestParam(value = "staffid",required = false)String staffid,
											   @RequestParam(value = "contractname",required = false)String contractname,
											   @RequestParam(value = "contractno",required = false)String contractno)throws Exception{
		String result = null;
		Map<String,Object> resultMap = new HashMap<String,Object>(0);
		if(flowid==null || flowid.equals("")){
			flowid= (String) request.getSession().getAttribute("flowid");
		}
		PageInfo<TblContractCollection> pageInfo = new PageInfo<TblContractCollection>();

		TblStaffUtil staff = DealUserToken.parseUserToken(token);

		BigDecimal pid = staff.getCurrentOrg().getOrgid();
		if(!JudgeRoleRight.judgeRoleRight("合同管理员",staff.getRoleNames())) {
			collection.setCreatestaff(staff.getStaffid());
		}
		collection.setContract(unit);
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		this.tblContractCollectionService.findCollectionListByPageInfo(pageInfo,staffid,contractname,contractno,pid,collection);
		TblFlow flow = tblFlowService.findById(flowid);
		request.getSession().setAttribute("UETempType",flow.getFlowname());
		if(choiceSearch == null || "".equals(choiceSearch)) {
			choiceSearch = "hide";
		}
		resultMap.put("choiceSearch", choiceSearch);
		resultMap.put("date", pageInfo);
		resultMap.put("flowname",flow.getFlownumber());
		resultMap.put("flow", flow);

		JSONObject jsonObject = new JSONObject(resultMap);
		result = jsonObject.toString();
		return result;
	}


	@RequestMapping(value = "/contract/saveCollectionManagemen", method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation(value = "合同管理-财务管理-收款管理-新增接口")
	public String saveCollectionManagemen(HttpServletRequest request,
										  @RequestParam(value="nodeid",required=true)BigDecimal nodeid,
										  @RequestParam(value="contractid",required=true)BigDecimal contractid,
										  @RequestParam(value="invoiceid",required=true)BigDecimal invoiceid,
										  @RequestParam(value="invoicemoney",required=true)BigDecimal invoicemoney,
										  @RequestParam(value="bankbankid",required=true)BigDecimal bankbankid,
										  @RequestParam(value="bankid",required=true)BigDecimal bankid,
										  TblContractCollection collection,
										   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token																) {
		String result = null;
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal pid = staff.getCurrentOrg().getOrgid();
			BigDecimal staffid = staff.getStaffid();

			TblCyhwUnit unit = new TblCyhwUnit();
			unit.setContractid(contractid);
			TblContractPlannode node = new TblContractPlannode();
			node.setNodeid(nodeid);
			TblContractInvoicesmanagemen invoice = new TblContractInvoicesmanagemen();
			invoice.setInvoiceid(invoiceid);
			TblCounterpartBankinfo counterBank = new TblCounterpartBankinfo();
			counterBank.setBankid(bankbankid);//合同管理-财务管理-收款管理-新增-付款银行账号
			TblOrgBankaccount orgBank = new TblOrgBankaccount();
			orgBank.setBankid(bankid);//财务管理-收款管理-新增-收款银行账号
			Integer money = this.tblContractCollectionService.checkMoney(nodeid);
			BigDecimal skMoney = new BigDecimal(this.tblContractCollectionService.getSkMoney(nodeid));
			//开票金额应<=本期应收金额
//			if(invoicemoney.compareTo(skMoney)!=1)
//			{    //开票金额应<=本期待收金额
//				if(Integer.valueOf(money.toString())>0&&invoicemoney.compareTo(new BigDecimal(money))==1){
//					return "开票金额应该少于本期代收金额";
//				}
//			} else{
//				return "-1";
//			}

			if(collection.getCollectionid() != null) {
				TblContractCollection oldCollection = this.tblContractCollectionService.findById(collection.getCollectionid());
				//oldCollection.setContract(unit);
				oldCollection.setContractid(contractid);
				oldCollection.setNodeid(nodeid);
				oldCollection.setInvoiceid(invoiceid);
				//oldCollection.setCollectionskdate(collection.getCollectionskdate());
				oldCollection.setCreatestaff(staffid);
				//oldCollection.setCreatedate(new Date());
				oldCollection.setCollectionbank(collection.getCollectionbank());
				oldCollection.setCollectionaccount(collection.getCollectionaccount());
				oldCollection.setCollectionorgname(collection.getBudgetname());
				oldCollection.setCounterbank(bankbankid);//合同管理-财务管理-收款管理-新增-收款银行账号
				oldCollection.setOrgbank(bankid);//财务管理-收款管理-新增-付款银行账号
				oldCollection.setLinkorg(pid);
				this.tblContractCollectionService.mengerCollectionEntity(oldCollection);
				return JsonBean.success("修改成功");
			}else {
				collection.setContractid(contractid);//收款合同id
				collection.setNodeid(nodeid);//nodeid//对应收款项id
				collection.setInvoiceid(invoiceid);//invoiceid发票号
				collection.setCollectionskdate(collection.getCollectionskdate());
				collection.setCreatestaff(staffid);//staffid
				//collection.setCreatedate(new Date());
				collection.setLinkorg(pid);//pid
				collection.setCollectionorgname(collection.getBudgetname());
				collection.setCounterbank(bankbankid);//合同管理-财务管理-收款管理-新增-收款银行账号
				collection.setOrgbank(bankid);//财务管理-收款管理-新增-付款银行账号
				 this.tblContractCollectionService.SaveMengerCollectionEntity(collection);
				return JsonBean.success("新增成功");
			}
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;

	}


	@RequestMapping(value = "/contract/collectionChoiceContract", method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "合同管理-财务管理-收款管理-新增-收款合同")
	public String contract_collectionChoiceContract(HttpServletRequest request,
													@RequestParam(value="choiceSearch",required=false)String choiceSearch,
													@RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
													@RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
													TblCyhwUnit unit,
													 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
													@RequestParam(value = "contractno",required = false)String contractno,
													@RequestParam(value = "contractname",required = false)String contractname) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {

			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			if(staff == null) {
				resultMap.put("code", "0");
				resultMap.put("msg", "用户已失效！");
				return String.valueOf(resultMap);
			}
			BigDecimal pid = staff.getCurrentOrg().getOrgid();

			PageInfo<TblCyhwUnit> pageInfo = new PageInfo<TblCyhwUnit>();
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			this.tblCyhwUnitService.findCollectionChoiceContract(pageInfo,pid,contractno,contractname);

			resultMap.put("date", pageInfo);
			if(choiceSearch == null || "".equals(choiceSearch)) {
				choiceSearch = "hide";
			}
			resultMap.put("choiceSearch", choiceSearch);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}

		JSONObject jsonObj = new JSONObject(resultMap);
		result = jsonObj.toString();
		return result;
	}


	@RequestMapping(value = "/choicePlanNodeConlletion", method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "合同管理-财务管理-收款管理-新增-对应收款项（合同收款管理，通过合同选择对应的履行内容）")
	public String contract_choicePlanNodeConlletion(HttpServletRequest request,
													@RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
													@RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
													TblContractPlannode node,
													@RequestParam(value="contractid",required=false)BigDecimal contractid
													) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {

			PageInfo<TblContractPlannode> pageInfo = new PageInfo<TblContractPlannode>();
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			node.setProjectid(contractid);
//			pageInfo.setCondition(node);
			this.tblContractPlannodeService.findPlanNodeListForCollection(pageInfo,node);
			resultMap.put("date", pageInfo);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		JSONObject jsonObj = new JSONObject(resultMap);
		result = jsonObj.toString();
		return result;
	}


	@RequestMapping(value = "/choiceCounterPartBankInfo", method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "合同管理-财务管理-收款管理-新增-付款银行账号（合同收款管理 选择发票信息）/付款管理-新增-收款银行账号")
	public String contract_choiceCounterPartBankInfo(HttpServletRequest request,
													 @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
													 @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
													 @RequestParam(value="budgetId",required=false)BigDecimal budgetId,
													 TblCounterpartBankinfo bank) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			PageInfo<TblCounterpartBankinfo> pageInfo = new PageInfo<TblCounterpartBankinfo>();
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			bank.setBudgetid(budgetId);
			bank.setBankstatus(BigDecimal.valueOf(1));
			//pageInfo.setCondition(bank);
			 resultMap = this.tblCounterpartBankInfoService.findListByPageInfo(pageInfo,bank);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		JSONObject jsonObj = new JSONObject(resultMap);
		result = jsonObj.toString();
		return result;
	}

	@RequestMapping(value = "/choiceOrgselfBankInfo", method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "合同管理-财务管理-收款管理-新增-收款账户（右侧选择接口）【付款管理-新增-付款银行账号】")
	public String contract_choiceOrgselfBankInfo(HttpServletRequest request,
			@RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
			@RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
			TblOrgBankaccount bank,
			 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			if(pageNumber == null){
				pageNumber = 1 ;
			}
			//TblOrganization attribute = (TblOrganization) request.getSession().getAttribute("hbOrgEntity");
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal pid = staff.getCurrentOrg().getOrgid();
			PageInfo<TblOrgBankaccount> pageInfo = new PageInfo<TblOrgBankaccount>();
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			bank.setBankstatus(BigDecimal.valueOf(1));
			bank.setBankstate(BigDecimal.valueOf(0));
			bank.setOrgid(pid);
			pageInfo.setCondition(bank);
			this.tblOrgBankAccountService.findListByPageInfo(pageInfo,bank);
			resultMap.put("date", pageInfo);

		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		JSONObject jsonObj = new JSONObject(resultMap);
		result = jsonObj.toString();
		return result;
	}


	@RequestMapping(value = "/collectionChoiceInvoice", method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "合同管理-财务管理-收款管理-新增-发票信息/财务管理-新增-发票号")
	public String contract_collectionChoiceInvoicen(HttpServletRequest request,
													@RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
													@RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
													@RequestParam(value="budgetId",required=false)BigDecimal budgetId,
													 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
													TblContractInvoicesmanagemen invoice) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {

			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal pid = staff.getCurrentOrg().getOrgid();
			PageInfo<TblContractInvoicesmanagemen> pageInfo = new PageInfo<TblContractInvoicesmanagemen>();
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			invoice.setInvoiceogr(pid);
			//pageInfo.setCondition(invoice);
			TblCyhwProjectbudget budget = new TblCyhwProjectbudget();
			budget.getBudgetid();
			invoice.setBudget(budget);
			invoice.setBudgetId(budgetId);
			this.tblContractInvoicesmanagemenService.findInvoiceInfoListForCollection(pageInfo,invoice);
			resultMap.put("date", pageInfo);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		JSONObject jsonObj = new JSONObject(resultMap);
		result = jsonObj.toString();
		return result;
	}


	@RequestMapping(value = "/skgl/to_sptzgl_info", method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "合同管理-财务管理-收款管理-办理")
	@ResponseBody
	public  String to_sptzgl_infojc(HttpServletRequest request,
			 @RequestParam(value = "collectionId", required = true)String collectionId,
			 @RequestParam(value = "flowid", required = false)@ApiParam(name="flowid",value="流程id",required=false)String flowid,
			 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token")String token) throws Exception{
		String result = null;
		Map<String,Object> resultMap = new HashMap<String,Object>(0);
		Map<String,Object> dataMap = new HashMap<String,Object>(0);
		TblContractCollection entity = tblContractCollectionService.findById(new BigDecimal(collectionId.toString()));
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		if(staff == null) {
			resultMap.put("code", "0");
			resultMap.put("msg", "用户已失效！");
			return String.valueOf(resultMap);
		}
        TblCirculation cy = this.tblCirculationMapper.selectCiculaInfoById(collectionId.toString());
		List<TblMyTask> list =null;
		if(staff.getTrole() !=null && staff.getTrole().getRname()!=null){
			list= HttpClient.findByTask(staff.getTrole().getRname(), staff.getStaffid().toString(), 1, 10000);
		}else{
			list= HttpClient.findByTask("", staff.getStaffid().toString(), 1, 3000);
		}
		if(list!=null && list.size()>0){

			for (TblMyTask myTask : list) {
				if(cy!=null && myTask.getProcessInstanceId().equals(cy.getBusinesskey())){
					Map<String, Object> map = HttpClient.lczxProcessJson(myTask.getTaskId());
					String obj = (String) map.get("data");
					String[] results=obj.replace("\"", "").split(",");
					if(results.length>0 && !results[0].equals("")){
						List<String> stringB = Arrays.asList(results);
						dataMap.put("results",stringB);
						dataMap.put("number",stringB.size());
					}else{
						dataMap.put("results",null);
						dataMap.put("number",0);
					}
					dataMap.put("task",myTask);
					break;
				}
			}

		}

		if(flowid == null || "".equals(flowid)){
			flowid = this.tblCyhwUnitService.findecontracFlowIdByContractId(entity.getContractid().intValue());

		}
		TblCyhwUnit unit = tblCyhwUnitService.findContractMoneyById(entity.getContractid());
		TblContractPlannode node = tblContractPlannodeService.findPlannodeById(entity.getNodeid());
		BigDecimal money = unit.getContractmoney().multiply(new BigDecimal(node.getNodepost()));
		TblContractPlannode tcp = new TblContractPlannode();
		tcp.setYfMoney(money.divide(new BigDecimal(100)));
		TblFlow flow = tblFlowService.findById(flowid);
		dataMap.put("flowname",flow.getFlownumber());
		dataMap.put("flow",flow);

		dataMap.put("flowid",flowid);
		dataMap.put("searchUrl",flow.getFlowmappingurl());
		String cateId = request.getParameter("cateId");
		dataMap.put("cy",cy);
		dataMap.put("cateId",cateId);
		dataMap.put("collection",entity);
		if(staff.getTrole()!=null && staff.getTrole().getRname()!=null){
			dataMap.put("rolename",staff.getTrole().getRname()!=null);
		}
		dataMap.put("v",request.getParameter("v"));
		resultMap.put("code", "1");
		resultMap.put("msg", "成功！");
		resultMap.put("data",dataMap);
		JSONObject jsonObj = new JSONObject(resultMap);
		result = jsonObj.toString();
		return result;
	}

	/**
	 * 收款管理-办理页面-查询流程图
	 * @param request
	 * @param collectionid
	 * @param flowid
	 * @return
	 * @throws Exception
	 */
	@ApiOperation(value = "收款管理-办理页面-查询流程图")
	@RequestMapping(value = "/skgl/approval_process", method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	public String approval_processjc(HttpServletRequest request,
			 @RequestParam(value = "collectionId", required = true)@ApiParam(name="collectionId",value="主键",required=true)String collectionId,
									 @RequestParam(value = "flowid",required = false)String flowid,
									 @RequestParam(value = "taskId",required = false) String taskId) throws Exception {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblContractCollection aw=tblContractCollectionService.findById(new BigDecimal(collectionId));
		List<TblMyTask> list = tblMyTaskService.getByFromid(collectionId);
		resultMap.put("aw", aw);
		resultMap.put("taskId", taskId);
		resultMap.put("data", list);
		resultMap.put("flowid",flowid);
		resultMap.put("flowname",request.getParameter("flowname"));
		resultMap.put("url", HttpClient.jkurl+taskId);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = {"/skgl/tjsp_zcgl"},produces = {"application/json; charset=utf-8"}, method = {RequestMethod.POST})
	@ApiOperation(value = "财务管理-收款管理-提交审批")
	@ResponseBody
	public String tjsp_tzgl(HttpServletRequest request, Integer collectionId,
							 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
							@ApiParam(name = "staffId",required = false)String staffId,
							@RequestParam(value = "flowid",required = false)String flowid) {
		TblContractCollection aw = this.tblContractCollectionService.findByCollectionId(collectionId);
		String msg = null;
		try {
			if (aw.getCollectionstatus() != null && aw.getCollectionstatus() == 1) {
				return JsonBean.error("流程审批中");
			} else if (aw.getCollectionstatus() != null && aw.getCollectionstatus() == 3) {
				return JsonBean.error("流程已通过");
			} else if ((aw.getCollectionstatus() == null || aw.getCollectionstatus() != 4) && (aw.getCollectionstatus() == null || aw.getCollectionstatus() != 5) && (aw.getCollectionstatus() == null || aw.getCollectionstatus() != 6)) {
				TblStaffUtil staff = DealUserToken.parseUserToken(token);
				if (flowid == null || flowid == "") {
					flowid = (String)request.getSession().getAttribute("flowid");
				}

				TblFlow flow = this.tblFlowService.findById(flowid);
				aw.setCollectionstatus(Long.valueOf(TblContractCollection.STATE_SP));
				this.tblContractCollectionService.mengerCollectionEntity(aw);
				List<TblProcessAnalysis> list = this.tblProcessAnalysisService.getByModuel(flow.getSettingid());
				HashMap<String, Object> fields = new HashMap();
				if (list != null && list.size() > 0) {
					Iterator var9 = list.iterator();

					while(var9.hasNext()) {
						TblProcessAnalysis tblAnalysis = (TblProcessAnalysis)var9.next();
						TblProcessAnalusisUser analysisUser = this.tblProcessAnalusisUserService.findOnd(tblAnalysis.getAnalid().toString(), collectionId.toString());
						if (analysisUser == null) {
							analysisUser = new TblProcessAnalusisUser();
							analysisUser.setAnalid(tblAnalysis.getAnalid().toString());
							analysisUser.setFromid(collectionId.toString());
							analysisUser.setSpdate(new Date());
							if (tblAnalysis.getUserid() != null) {
								analysisUser.setStaffid(staff.getRealname());
							} else {
								analysisUser.setStaffid(tblAnalysis.getRolename());
							}

							this.tblProcessAnalusisUserService.insertSetting(analysisUser);
						}

						if (tblAnalysis.getUserid() != null && !tblAnalysis.getUserid().equals("")) {
							fields.put(tblAnalysis.getUserid(), staff.getStaffid().toString());
						}
					}
				}

				net.sf.json.JSONObject jsonObject = net.sf.json.JSONObject.fromObject(fields);
				Map<String, Object> map = HttpClient.startProcessAll(flow.getSettingid(), jsonObject.toString());
				String obj = (String)map.get("result");
				String processInstanceId = (String)map.get("processInstanceId");
				String processDefinitionKey = (String)map.get("processDefinitionKey");
				TblCyhwUnit unit = tblCyhwUnitService.findContractMoneyById(aw.getContractid());
				TblCirculation cir = this.tblCirculationService.saveTblCirculationnew("收款管理", unit.getContractno(), unit.getContractname(), "/htgl/skgl/to_sptzgl_info?collectionId=" + collectionId, staff.getStaffid(), processInstanceId, processDefinitionKey, collectionId.toString());
				if (obj != null && obj.equals("true")) {
					List<TblMyTask> tasks = HttpClient.findByTask("", staff.getStaffid().toString(), 1, 10000);
					if (tasks != null && tasks.size() > 0) {
						Iterator var16 = tasks.iterator();

						while(var16.hasNext()) {
							TblMyTask task = (TblMyTask)var16.next();
							if (task.getProcessInstanceId().equals(processInstanceId)) {
								Map<String, Object> map1 = HttpClient.handleProcessJson(staff.getStaffid().toString(), "通过", task.getTaskId());
								String result = (String)map1.get("result");
								if (result != null && result.equals("true")) {
									String blande = "";
									TblProcessAnalysis analysis = this.tblProcessAnalysisService.findOndBytakdidstart(blande, flow.getSettingid());
									Integer number = 1;
									String usertaskid = analysis.getUsertaskid();
									Integer num = Integer.parseInt(usertaskid.substring(usertaskid.length() - 1, usertaskid.length())) + number;
									blande = usertaskid.substring(0, usertaskid.length() - 1) + num;
									TblProcessAnalysis analysis1 = this.tblProcessAnalysisService.findOndBytakdidstart(blande, flow.getSettingid());
									TblProcessAnalusisUser analysisUser = this.tblProcessAnalusisUserService.findOnd(analysis1.getAnalid().toString(), collectionId.toString());
									task.setFromid(collectionId.toString());
									task.setApprover(staff.getRealname());
									task.setUsrid(staff.getStaffid().toString());
									task.setExamination("提交申请");
									task.setProcessName(flow.getSettingid());
									task.setApprovalrole(staff.getTrole().getRname());
									task.setApprovaldate(new Date());
									task.setResult("通过");
									task.setCirid(cir.getCyid().toString());
									task.setHandle(analysisUser.getStaffid());
									task.setAnalid(analysisUser.getAnalid().toString());
									this.tblMyTaskService.insertMyTaskSetting(task);
								}
							}
						}
					}
					return JsonBean.success();
				} else {
					return JsonBean.error("发送审批失败");
				}
			} else {
				return JsonBean.error("流程已完成");
			}
		} catch (Exception var27) {
			var27.printStackTrace();
			return JsonBean.error("发送审批失败");
		}
//		if (!StringUtil.isNullOrEmpty(msg)) {
//			return JsonBean.error(msg);
//		} else {
//			return JsonBean.success("审批已发送");
//		}

	}


	@RequestMapping(value = "/contract/removeCollectionManagemen", produces = "application/json; charset=utf-8", method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation(value = "财务管理-收款管理-删除")
	public String contract_removeCollectionManagemen(HttpServletResponse response,HttpServletRequest request,
													 @RequestParam(value="collectionId",required=true)Integer collectionId
	)throws Exception{
		this.tblContractCollectionService.removeContractCollection(collectionId);
		return JsonBean.success("删除成功");
	}


	@RequestMapping(value = "/contract/paymentManagemen", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value = "财务管理-付款管理-列表")
	public String contract_paymentManagemen(HttpServletResponse response,HttpServletRequest request,
											@RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
											@RequestParam(value = "pageSize",required = false,defaultValue = "15")Integer pageSize,
											 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
											@ApiParam(name = "staffId",required = false)String staffId,
											TblCyhwUnit unit,TblContractPayment payment,
											@RequestParam(value = "flowid",required = false)String flowid)throws Exception{
		//String flowid = request.getParameter("flowid");
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		if(flowid==null || flowid.equals("")){
			flowid= (String) request.getSession().getAttribute("flowid");
		}
		PageInfo<TblContractPayment> pageInfo = new PageInfo<TblContractPayment>();

		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		if(staff == null) {
			resultMap.put("code", "0");
			resultMap.put("msg", "用户已失效！");
			return String.valueOf(resultMap);
		}
		String orgid = staff.getCurrentOrg().getOrgid().toString();
		BigDecimal staffid = staff.getStaffid();
		if(!JudgeRoleRight.judgeRoleRight("合同管理员",staff.getRoleNames())) {
			payment.setCreatestaff(staffid);
		}
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		resultMap = this.tblContractPaymenService.findPaymentManagemenByPageInfo(pageInfo,orgid,payment,unit,flowid);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}

	@RequestMapping(value = "/savePaymentManagemen",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation(value = "财务管理-付款管理-新增")
	public String savePaymentManagemen(HttpServletRequest request,
									   TblContractPayment payment ,
									   @RequestParam(value="contractid",required=true)Integer contractid,//合同id
									 //  @RequestParam(value="budgetid",required=false)Integer budgetid,//付款去掉
									   @RequestParam(value="applyStaffId",required=false)BigDecimal applyStaffId,//部门id
									   @RequestParam(value="applyOrgId",required=true)BigDecimal applyOrgId,//部门id
									   @RequestParam(value="bankbankid",required=false)BigDecimal bankbankid,//付款
									   @RequestParam(value="bankid",required=false)BigDecimal bankid,//收款
									   @RequestParam(value="nodeid",required=true)BigDecimal nodeid,//付款计划id
									   @RequestParam(value="invoiceid",required=true)BigDecimal invoiceid,//发票id
									   @RequestParam(value="applyDateStr",required=false)String applyDateStr,//申请日期
									   @RequestParam(value="payLateDateStr",required=false)String payLateDateStr,//最晚付款日期
									    @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {

		try {
			
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

			
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal pid = staff.getCurrentOrg().getOrgid();

			if(applyStaffId == null) {
				applyStaffId = staff.getStaffid();;
			}
			TblContractInvoicesmanagemen invoice = this.tblContractInvoicesmanagemenService.findInvoiceInfoByInvoiceId(invoiceid);
			if(payment.getPaymentid() != null) {
				payment.setApplyorg(applyOrgId);
				payment.setApplystaff(applyStaffId);
				payment.setCounterbank(bankbankid);
				payment.setOrgbank(bankid);
				payment.setLinkorg(pid);
				if(applyDateStr != null && !"".equals(applyDateStr)) {
					payment.setApplydate(sdf.parse(applyDateStr));
				}
				if(payLateDateStr != null && !"".equals(payLateDateStr)) {
					payment.setPaymentlatedate(sdf.parse(payLateDateStr));
				}
				//payment.setBudgetid(new BigDecimal(budgetid));
				payment.setContractid(contractid);
				payment.setNodeid(nodeid);
				payment.setInvoiceid(invoiceid);
				payment.setPaymenmoney(invoice.getInvoicemoney());
				this.tblContractPaymenService.updatePaymentInfo(payment);
				return JsonBean.success("修改成功");
			}else {
				payment.setApplyorg(applyOrgId);
				payment.setApplystaff(applyStaffId);
				payment.setCounterbank(bankbankid);
				payment.setOrgbank(bankid);
				payment.setLinkorg(pid);
				if(applyDateStr != null && !"".equals(applyDateStr)) {
					payment.setApplydate(sdf.parse(applyDateStr));
				}
				if(payLateDateStr != null && !"".equals(payLateDateStr)) {
					payment.setPaymentlatedate(sdf.parse(payLateDateStr));
				}
				//payment.setBudgetid(new BigDecimal(budgetid));
				payment.setContractid(contractid);
				payment.setNodeid(nodeid);
				payment.setInvoiceid(invoiceid);
				payment.setCreatestaff(staff.getStaffid());
				payment.setPaymenmoney(invoice.getInvoicemoney());
				this.tblContractPaymenService.addPaymentInfo(payment);//savePaymentInfo(payment);
				return JsonBean.success("新增成功");
			}
		} catch (Exception e) {
			log.error("异常信息：", e);
			return JsonBean.error();
		}
	}


// 部门申请走/findOrganizationByTreeNbkz

	@RequestMapping(value = "/pjlx/findOrganizationByTreeAllss", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value = "付款管理-新增-经办人选择接口-左侧列表(纠纷登记-新建-纠纷承办人-左侧列表)")
	public @ResponseBody String pjlxfindOrganizationByTree(BigDecimal nodeId, String type,
														   BigDecimal orgId,
														   HttpServletRequest request,
														    @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
														   @ApiParam(name = "staffId",required = false)String staffId,
														   @RequestParam(value = "json",required = false)String json,
														   @RequestParam(value = "idname",required = false)String idname,
														   @RequestParam(value = "textname",required = false)String textname) throws Exception {

		if (null == nodeId) {
			nodeId = orgId;
			if (null == orgId) {
				TblStaffUtil staff = DealUserToken.parseUserToken(token);
				nodeId = staff.getLinkOrg().getOrgid();
			}
		}
		if (StringUtils.isNotBlank(type)) {
			List<Tree> list = this.tblOrganizaService.getTree(nodeId);
			for (Tree tree : list) {
				if (!tree.getIsParent()) {
					tree.setTarget("mainFramex");
					tree.setUrl("/nbkz/pjlx/list?pid=" + tree.getId() + "&idname=" + idname + "&textname=" + textname);
				}
			}
			json = JSONObject.toJSONString(list);
		} else {
			List<Tree> list = this.tblOrganizaService.getNodeAll(nodeId);
			for (Tree tree : list) {
				setUrlByTree(tree, "/nbkz/pjlx/list?idname=" + idname + "&textname=" + textname + "&pid=");
			}
			json = JSONObject.toJSONString(list);
		}
		return json;
	}
	private void setUrlByTree(Tree tree, String url) {
		for (Tree tre : tree.getChildren()) {
			/// if (!tre.getIsParent()) {
			tre.setTarget("mainFramex");
			tre.setUrl(url + tre.getId());
			// }
			if (tre.getChildren().size() > 0) {
				setUrlByTree(tre, url);
			}
		}
	}

	@RequestMapping(value = "/pjlx/list",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "付款管理-新增-经办人选择接口-右侧列表接口（纠纷登记-新建-纠纷继承人-左侧列表）(协商过程-新建-协商过程信息-新建-我方谈判人)")
	public String pjlxuserListss(HttpServletRequest request,TblOrganization organization,
									   @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
									   @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
									   @RequestParam(value = "pid",required = false)String pid,
									   @RequestParam(value = "localset",required = false)String localset,
									    @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {


		String result = null;
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();
		if (pid != null && !pid.equals("")) {
			orgid = tblOrganizaService.findById(pid);
		}

		Map<String, Object> resultMap = tblStaffService.findAllPageBeanPid(pageNumber, pageSize, orgid,organization);

		if (localset != null && !"".equals(localset)) {
			String[] locals = localset.split(",");
			resultMap.put("idname", locals[0]);
			resultMap.put("textname", locals[1]);
		} else {
			resultMap.put("idname", request.getParameter("idname"));
			resultMap.put("textname", request.getParameter("textname"));
		}
		resultMap.put("pid", pid);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/paymentChoiceContract",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "付款管理-新增-合同名称选择接口")
	public String contract_paymentChoiceContract(HttpServletRequest request,
													   TblCyhwUnit unit,
												 @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
												 @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
												  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal pid = staff.getCurrentOrg().getOrgid();

			PageInfo<TblCyhwUnit> pageInfo = new PageInfo<TblCyhwUnit>();
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			unit.setOrgid(pid);
			//pageInfo.setCondition(unit);

			this.tblCyhwUnitService.findPaymentChoiceContract(pageInfo,unit);

			resultMap.put("date", pageInfo);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;

	}


//	@RequestMapping(value = "/contract/choiceOrgselfBankInfo",method = {RequestMethod.POST})
//	@ApiOperation(value = "付款管理-新增-付款银行账号")
//	public String contract_choiceOrgselfBankInfo(HttpServletRequest request,
//													   @RequestParam(value="choiceSearch",required=false)String choiceSearch,
//												 @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
//												 @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
//												  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
//												 TblOrgBankaccount bank) {
//		String result = null;
//		Map<String,Object> resultMap = new HashMap<String, Object>(0);
//		try {
//			if(pageNumber == null){
//				pageNumber = 1 ;
//			}
////			TblOrganization attribute = (TblOrganization) request.getSession().getAttribute("hbOrgEntity");
//			Claims claims = JwtUtils.parseJwt(token);
//			Object object = claims.get("staffInfo");
//			net.sf.json.JSONObject objJson = net.sf.json.JSONObject.fromObject(object);
//			TblStaff staff = (TblStaff) net.sf.json.JSONObject.toBean(objJson,TblStaff.class);
//			BigDecimal pid = staff.getCurrentOrg().getOrgid();
//
//			PageInfo<TblOrgBankaccount> pageInfo = new PageInfo<TblOrgBankaccount>();
//			pageInfo.setCurrentPage(pageNumber);
//			bank.setBankstatus(BigDecimal.valueOf(1));
//			bank.setBankstate(BigDecimal.valueOf(0));
//			bank.setOrgid(pid);
//			//pageInfo.setCondition(bank);
//			this.tblOrgBankAccountService.findListPageInfo(pageInfo,bank);
//			resultMap.put("pageInfo", pageInfo);
//			if(choiceSearch == null || "".equals(choiceSearch)) {
//				choiceSearch = "hide";
//			}
//			// 从父页面自定义 name名称 保存id值
//			resultMap.put("idname", request.getParameter("idname"));
//			resultMap.put("textname", request.getParameter("textname"));
//			resultMap.put("choiceSearch", choiceSearch);
//		} catch (Exception e) {
//			log.error("异常信息：", e);
//		}
//		JSONObject jsonObjectMV = new JSONObject(resultMap);
//		result = jsonObjectMV.toString();
//		return result;
//	}


	@RequestMapping(value = "/choicePlanNodePayment",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "付款管理-新增-付款计划")
	public String contract_choicePlanNodePayment(HttpServletRequest request,
												 @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
												 @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
												 TblContractPlannode node,
												 @RequestParam(value="contractId",required=true)BigDecimal contractId) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {

			PageInfo<TblContractPlannode> pageInfo = new PageInfo<TblContractPlannode>();
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			node.setProjectid(contractId);
			//pageInfo.setCondition(node);
			this.tblContractPlannodeService.findPlanNodeListForPayment(pageInfo,node);
			resultMap.put("date", pageInfo);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/fkgl/to_sptzgl_info",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "付款管理-办理")
	public  String to_sptzgl_infofk(HttpServletRequest request,
									@ApiParam(name="paymentId",value="paymentId",required=true)@RequestParam(value = "paymentId",required = true)Integer paymentId,
									 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
									@RequestParam(value = "flowid",required = false)String flowid) throws Exception{

		TblContractPayment entity = this.tblContractPaymenService.findPaymentInfoByParmentId(paymentId);
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		Map<String,Object> dataMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);

		TblCirculation cy = tblCirculationService.getOneBytaskid(paymentId.toString());
		List<TblMyTask> list =null;
		if(staff.getTrole() !=null && staff.getTrole().getRname()!=null){
			list= HttpClient.findByTask(staff.getTrole().getRname(), staff.getStaffid().toString(), 1, 10000);
		}else{
			list= HttpClient.findByTask("", staff.getStaffid().toString(), 1, 10000);
		}
		if(list!=null && list.size()>0){

			for (TblMyTask myTask : list) {
				if(cy!=null && myTask.getProcessInstanceId().equals(cy.getBusinesskey())){
					Map<String, Object> map = HttpClient.lczxProcessJson(myTask.getTaskId());
					String obj = (String) map.get("data");
					String[] results=obj.replace("\"", "").split(",");
					if(results.length>0 && !results[0].equals("")){
						List<String> stringB = Arrays.asList(results);
						dataMap.put("results",stringB);
						dataMap.put("number",stringB.size());
					}else{
						dataMap.put("results",null);
						dataMap.put("number",0);
					}
					dataMap.put("task",myTask);
					break;
				}
			}

		}
		if(flowid == null || "".equals(flowid)){
			flowid = this.tblCyhwUnitService.findecontracFlowIdByContractId(entity.getContractid());
		}

		TblFlow flow = tblFlowService.findById(flowid);
		dataMap.put("flow",flow);
		dataMap.put("flowname",flow.getFlownumber());
		dataMap.put("flowid",flowid);
		dataMap.put("searchUrl",flow.getFlowmappingurl());
		String cateId = request.getParameter("cateId");
		dataMap.put("cy",cy);
		dataMap.put("cateId",cateId);
		dataMap.put("payment",entity);
		if(staff.getTrole() !=null && staff.getTrole().getRname()!=null){
			dataMap.put("rolename",staff.getTrole().getRname()!=null);
		}
		dataMap.put("v",request.getParameter("v"));
		resultMap.put("code",1);
		resultMap.put("msg","成功");
		resultMap.put("data",dataMap);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/fkgl/approval_process",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "付款管理-办理-查询流程图")
	public String approval_processfk(HttpServletRequest request,
									 @RequestParam(value = "paymentid",required = false)Integer paymentid,
									 @RequestParam(value = "taskid",required = false)String taskid,
									 @RequestParam(value = "flowid",required = false)String flowid) throws Exception {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblContractPayment aw = tblContractPaymenService.findPaymentInfoByParmentId(paymentid);
		List<TblMyTask> list = tblMyTaskService.getByFromid(paymentid.toString());
		resultMap.put("aw", aw);
		resultMap.put("taskid", taskid);
		resultMap.put("list", list);
		resultMap.put("flowid",flowid);
		resultMap.put("flowname",request.getParameter("flowname"));
		resultMap.put("url", HttpClient.jkurl+taskid);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(
			value = {"/fkgl/tjsp_zcgl"},
			produces = {"application/json; charset=utf-8"},method = {RequestMethod.POST}
	)
	@ResponseBody
	@ApiOperation(value = "财务管理-付款管理-提交审批")
	public String tzgl_tzgl(HttpServletRequest request,
							@ApiParam(name="token",value="登录用户token",required=true)@RequestHeader("token") String token,
		 					@ApiParam(name="parmentId",value="主键",required=true)@RequestParam(value = "parmentId",required = false)BigDecimal parmentId,
							@ApiParam(name="flowid",value="flowid",required=true)@RequestParam(value = "flowid",required = false)String flowid) {
		TblContractPayment aw = null;
		String msg = null;
		try {
			aw = this.tblContractPaymenService.findByParmentId(parmentId);
		} catch (Exception var27) {
			var27.printStackTrace();
			msg = "发送审批失败";
		}

		try {
			if (aw.getPaymentstatus() != null && aw.getPaymentstatus() == 1) {
				msg =  "流程审批中";
			} else if (aw.getPaymentstatus() != null && aw.getPaymentstatus() == 3) {
				msg =  "流程已通过";
			} else if ((aw.getPaymentstatus() == null || aw.getPaymentstatus() !=4) && (aw.getPaymentstatus() == null || aw.getPaymentstatus() != 5) && (aw.getPaymentstatus() == null || aw.getPaymentstatus() != 6)) {
				TblStaffUtil staff = DealUserToken.parseUserToken(token);
				if (flowid == null || flowid == "") {
					flowid = (String)request.getSession().getAttribute("flowid");
				}

				TblFlow flow = this.tblFlowService.findById(flowid);
				aw.setPaymentstatus(TblContractPayment.STATE_SP);
				this.tblContractPaymenService.savePaymentInfo(aw);
				List<TblProcessAnalysis> list = this.tblProcessAnalysisService.getByModuel(flow.getSettingid());
				HashMap<String, Object> fields = new HashMap();
				if (list != null && list.size() > 0) {
					Iterator var9 = list.iterator();

					while(var9.hasNext()) {
						TblProcessAnalysis tblAnalysis = (TblProcessAnalysis)var9.next();
						TblProcessAnalusisUser analysisUser = this.tblProcessAnalusisUserService.findOnd(tblAnalysis.getAnalid().toString(), parmentId.toString());
						if (analysisUser == null) {
							analysisUser = new TblProcessAnalusisUser();
							analysisUser.setAnalid(tblAnalysis.getAnalid().toString());
							analysisUser.setFromid(parmentId.toString());
							analysisUser.setSpdate(new Date());
							if (tblAnalysis.getUserid() != null) {
								analysisUser.setStaffid(staff.getRealname());
							} else {
								analysisUser.setStaffid(tblAnalysis.getRolename());
							}

							this.tblProcessAnalusisUserService.insertSetting(analysisUser);
						}

						if (tblAnalysis.getUserid() != null && !tblAnalysis.getUserid().equals("")) {
							fields.put(tblAnalysis.getUserid(), staff.getStaffid().toString());
						}
					}
				}

				net.sf.json.JSONObject jsonObject = net.sf.json.JSONObject.fromObject(fields);
				Map<String, Object> map = HttpClient.startProcessAll(flow.getSettingid(), jsonObject.toString());
				String obj = (String)map.get("result");
				String processInstanceId = (String)map.get("processInstanceId");
				String processDefinitionKey = (String)map.get("processDefinitionKey");
				TblCyhwProjectbudget bud = this.tblCyhwProjectbudgetService.findBudgetid(Integer.parseInt(aw.getBudgetid().toString()));
				TblCirculation cir = this.tblCirculationService.saveTblCirculationnew("付款管理", aw.getPaymenttitle(), bud.getBudgetname(), "/htgl/fkgl/to_sptzgl_info?parmentId=" + parmentId, staff.getStaffid(), processInstanceId, processDefinitionKey, parmentId.toString());
				if (obj != null && obj.equals("true")) {
					List<TblMyTask> tasks = HttpClient.findByTask("", staff.getStaffid().toString(), 1, 10000);
					if (tasks != null && tasks.size() > 0) {
						Iterator var16 = tasks.iterator();

						while(var16.hasNext()) {
							TblMyTask task = (TblMyTask)var16.next();
							if (task.getProcessInstanceId().equals(processInstanceId)) {
								Map<String, Object> map1 = HttpClient.handleProcessJson(staff.getStaffid().toString(), "通过", task.getTaskId());
								String result = (String)map1.get("result");
								if (result != null && result.equals("true")) {
									String blande = "";
									TblProcessAnalysis analysis = this.tblProcessAnalysisService.findOndBytakdidstart(blande, flow.getSettingid());
									Integer number = 1;
									String usertaskid = analysis.getUsertaskid();
									Integer num = Integer.parseInt(usertaskid.substring(usertaskid.length() - 1, usertaskid.length())) + number;
									blande = usertaskid.substring(0, usertaskid.length() - 1) + num;
									TblProcessAnalysis analysis1 = this.tblProcessAnalysisService.findOndBytakdidstart(blande, flow.getSettingid());
									TblProcessAnalusisUser analysisUser = this.tblProcessAnalusisUserService.findOnd(analysis1.getAnalid().toString(), parmentId.toString());
									task.setFromid(parmentId.toString());
									task.setApprover(staff.getRealname());
									task.setUsrid(staff.getStaffid().toString());
									task.setExamination("提交审批");
									task.setProcessName(analysis.getProcessname());
									task.setApprovalrole(staff.getTrole().getRname());
									task.setApprovaldate(new Date());
									task.setResult("通过");
									task.setCirid(cir.getCyid().toString());
									task.setHandle(analysisUser.getStaffid());
									task.setAnalid(analysisUser.getAnalid().toString());
									this.tblMyTaskService.insertMyTaskSetting(task);
								}
							}
						}
					}
				} else {
					msg = "发送审批失败";
				}
			} else {
				msg =  "流程已完成";
			}
		} catch (Exception var28) {
			var28.printStackTrace();
			msg = "发送审批失败";
		}
		if (StringUtil.isNullOrEmpty(msg)) {
			return JsonBean.success("审批已发送");
		} else {
			return JsonBean.error(msg);
		}
	}


	@RequestMapping(value = "/contract/removePaymentManagemen",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation(value = "财务管理-付款管理-删除")
	public String contract_removePaymentManagemen(HttpServletRequest request,@RequestParam(value="parmentId",required=true)Integer paymentId) {
		try {
			this.tblContractPaymenService.removePaymentInfo(paymentId);
		} catch (Exception e) {
			log.error("异常信息：", e);
			return JsonBean.error("失败");
		}
		return JsonBean.success("修改成功");
	}


	@RequestMapping(value = "/contract/invoicesManageMen", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value = "财务管理-发票管理-列表")
	public String contract_invoicesManageMen(HttpServletResponse response,
											 HttpServletRequest request,
											 TblContractInvoicesmanagemen invoice,
											 @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
											 @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
											 @RequestParam(value = "flowid",required = false)String flowid,
											  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token)throws Exception{

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		if(flowid==null || flowid.equals("")){
			flowid= (String) request.getSession().getAttribute("flowid");
		}
		PageInfo<TblContractInvoicesmanagemen> pageInfo = new PageInfo<TblContractInvoicesmanagemen>();

		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();
		BigDecimal staffid = staff.getStaffid();
		if(!JudgeRoleRight.judgeRoleRight("合同管理员",staff.getRoleNames())) {
			invoice.setCreatestaff(staffid);
		}
		invoice.setInvoiceogr(orgid);
		//pageInfo.setCondition(invoice);
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		this.tblContractInvoicesmanagemenService.findContractInvociesManaeMenPageInfo(pageInfo,invoice);
		TblFlow flow = tblFlowService.findByFlowid(flowid);
		request.getSession().setAttribute("UETempType",flow.getFlowname());
		resultMap.put("date", pageInfo);
		resultMap.put("flowname",flow.getFlownumber());
		resultMap.put("flow", flow);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}

	@RequestMapping(value = "/contract/saveInvoiceManageMen",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation(value = "财务管理-发票管理-新增")
	public String contract_saveInvoiceManageMen(HttpServletRequest request,
												@RequestParam(value="budgetId",required=true)Integer budgetId,
												@RequestParam(value="startdate1",required=true)String startdate1,
												@RequestParam(value="enddate1",required=true)String enddate1,
												TblContractInvoicesmanagemen invoice,
												 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
												@ApiParam(name = "staffId",required = false)String staffId) {
		String result=null;
		try {
//			TblOrganization attribute = (TblOrganization) request.getSession().getAttribute("hbOrgEntity");
//			TblStaff user = (TblStaff) request.getSession().getAttribute("longUser");
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal orgid = staff.getCurrentOrg().getOrgid();
			BigDecimal staffid= staff.getStaffid();
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			TblCyhwProjectbudget budget = new TblCyhwProjectbudget();
			budget.setBudgetid(budgetId);
			if(invoice.getInvoiceid() != null) {
				TblContractInvoicesmanagemen oldInvoice = this.tblContractInvoicesmanagemenService.findInvoiceInfoByInvoiceId(invoice.getInvoiceid());
				oldInvoice.setInvoiceogr(orgid);
				oldInvoice.setCreatestaff(staffid);
				oldInvoice.setBudget(budget);
				oldInvoice.setInvoicedate(df.parse(startdate1));
				oldInvoice.setInvoicespdate(df.parse(enddate1));
				oldInvoice.setInvoiceheadtext(invoice.getInvoiceheadtext());
				oldInvoice.setInvoiceno(invoice.getInvoiceno());
				oldInvoice.setInvoicemoney(invoice.getInvoicemoney());
				oldInvoice.setInvoicepost(invoice.getInvoicepost());
				oldInvoice.setInvoicecontent(invoice.getInvoicecontent());
				oldInvoice.setInvoicetype(invoice.getInvoicetype());
				oldInvoice.setInvoicestatus(invoice.getInvoicestatus());
				oldInvoice.setInvoicesporg(invoice.getInvoicesporg());
				oldInvoice.setInvoicekporg(invoice.getInvoicekporg());
				oldInvoice.setTinumber(invoice.getTinumber());
				result = this.tblContractInvoicesmanagemenService.saveInvoicemanageMen(oldInvoice);
				return JsonBean.success("修改成功");
			}else {
				invoice.setInvoiceogr(orgid);
				invoice.setCreatestaff(staffid);
				invoice.setBudget(budget);
				invoice.setInvoicedate(df.parse(startdate1));
				invoice.setInvoicespdate(df.parse(enddate1));
				result = this.tblContractInvoicesmanagemenService.saveInvoicemanageMen(invoice);
				return JsonBean.success("新增成功");
			}

		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}


	@RequestMapping(value = "/contract/invoiceCounterpartInfoList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "财务管理-发票管理-新增-票据相对方选择接口")
	public String contract_invoiceCounterpartInfoList(HttpServletRequest request,
													  @RequestParam(value="choiceSearch",required=false)String choiceSearch,
													  @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
													  @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
													  TblCyhwProjectbudget tcpb,
													   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			//TblOrganization attribute = (TblOrganization) request.getSession().getAttribute("hbOrgEntity");
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal pid = staff.getCurrentOrg().getOrgid();

			PageInfo<TblCyhwProjectbudget> pageInfo = new PageInfo<TblCyhwProjectbudget>();
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			tcpb.setOrgid(pid);
			tcpb.setInspectionstatus(6);
			tcpb.setIsblack(2);
			//pageInfo.setCondition(tcpb);
			tblCyhwProjectbudgetService.invoiceCounterpartInfoListByPageInfo(pageInfo,tcpb);
			resultMap.put("date", pageInfo);
			if(choiceSearch == null || "".equals(choiceSearch)) {
				choiceSearch = "hide";
			}
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		resultMap.put("choiceSearch", choiceSearch );
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/contract/modifyInvoiceStatus", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation(value = "财务管理-发票管理-操作（状态）")
	public String contract_modifyInvoiceStatusn(HttpServletResponse response,
												HttpServletRequest request,
												@RequestParam(value="invoiceId",required=true)Integer invoiceId,
												@RequestParam(value="status",required=true)Integer status)throws Exception{
		this.tblContractInvoicesmanagemenService.modifyInvoiceStatus(invoiceId,status);
		return JsonBean.success("修改成功");
	}


	@RequestMapping(value = "/contract/removeInvoiceManageMen", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation(value = "财务管理-发票管理-删除")
	public String contract_removeInvoiceManageMen(HttpServletResponse response,
												  HttpServletRequest request,
												  @RequestParam(value="invoiceId",required=true)Integer invoiceId)throws Exception{
		this.tblContractInvoicesmanagemenService.removeInvoiceInfo(invoiceId);
		return JsonBean.success("删除成功");
	}


	@RequestMapping(value = "/contract/viewInvoice", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation(value = "财务管理-发票管理-查看详情")
	public String contract_viewInvoice(HttpServletResponse response,HttpServletRequest request,
									   @RequestParam(value="invoiceId",required=true)BigDecimal invoiceId)throws Exception{
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblContractInvoicesmanagemen invoice = this.tblContractInvoicesmanagemenService.findInvoiceInfoByInvoiceId(invoiceId);

		resultMap.put("invoice",invoice);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}

	/**
	 * 2-已开票，3-未收款，4-已收款，5-已退票
	 * @param request
	 * @param collectionId
	 * @return
	 */
	@RequestMapping(value = "/contract/viewCollectionManagemen",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("财务管理-收款管理-查看详情")
	public String contract_viewCollectionManagemen(HttpServletRequest request,
												   @RequestParam(value="collectionId",required=true)Integer collectionId) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblContractCollection collection = this.tblContractCollectionService.findCollectionInfoByCollectId(collectionId);
			resultMap.put("collection", collection);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/contract/viewPaymentManagemen",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "财务管理-付款管理-查看详情")
	public String contract_viewPaymentManagemen(HttpServletRequest request,
												@RequestParam(value="paymentId",required=true)Integer paymentId) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblContractPayment payment = this.tblContractPaymenService.findPaymentInfoByParmentId(paymentId);
			resultMap.put("payment", payment);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/contract/bankAccountDetail",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "财务管理-银行账户-查看详情")
	public String  contract_bankAccountDetail(HttpServletRequest request,
												   @RequestParam(value="bankId",required=true)Integer bankId) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblOrgBankaccount bank = this.tblOrgBankAccountService.findById(bankId);
			resultMap.put("bank", bank);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/contract/bankAccountList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "财务管理-银行账户-列表")
	public String contract_bankAccountList(HttpServletRequest request,
												 @RequestParam(value="choiceSearch",required=false)String choiceSearch,
												 @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
												 @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
												 TblOrgBankaccount bank,
										    @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			//TblOrganization attribute = (TblOrganization) request.getSession().getAttribute("hbOrgEntity");
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			if(staff == null) {
				resultMap.put("code", "0");
				resultMap.put("msg", "用户已失效！");
				return String.valueOf(resultMap);
			}
			BigDecimal pid = staff.getCurrentOrg().getOrgid();
			PageInfo<TblOrgBankaccount> pageInfo = new PageInfo<TblOrgBankaccount>();
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			//bank.setOrgid(pid);
			//pageInfo.setCondition(bank);
			tblOrgBankAccountService.findListByPageInfoPid(pageInfo,pid,bank);
			resultMap.put("date", pageInfo);
			if(choiceSearch == null || "".equals(choiceSearch)) {
	  			choiceSearch = "hide";
	  		}
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		resultMap.put("choiceSearch", choiceSearch );
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/contract/orgBankInfoSave",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
    @ResponseBody
	@ApiOperation(value = "财务管理-银行账户-新增")
	public String contract_orgBankInfoSave(HttpServletRequest request,
										   TblOrgBankaccount bank,
										    @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
    	try {
			if(bank.getBankid() != null) {
				TblOrgBankaccount oldEntity = this.tblOrgBankAccountService.findByBankId(bank.getBankid());
				oldEntity.setBankaccname(bank.getBankaccname());
				oldEntity.setBankaccnum(bank.getBankaccnum());
				oldEntity.setBankcode(bank.getBankcode());
				oldEntity.setBankkhyh(bank.getBankkhyh());
				oldEntity.setBankname(bank.getBankname());
				oldEntity.setBankstate(bank.getBankstate());
				oldEntity.setBankstatus(bank.getBankstatus());
				oldEntity.setBankyhlb(bank.getBankyhlb());
				this.tblOrgBankAccountService.UpdateModifyBankInfo(oldEntity);
				return JsonBean.success("修改成功");
			}else {

				TblStaffUtil staff = DealUserToken.parseUserToken(token);
				BigDecimal pid = staff.getCurrentOrg().getOrgid();
				bank.setOrgid(pid);
				bank.setCreatedate(new Date());
				this.tblOrgBankAccountService.savebankInfo(bank);
				return JsonBean.success("新增成功!");
			}
		} catch (Exception e) {
			log.error("异常信息：", e);
			//return JsonBean.error("失败");
		}

		return bank.getBankid().toString();
	}


	@RequestMapping(value = "/contract/removeOrgBankInfo",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation(value = "财务管理-银行账户-删除")
	public String contract_removeOrgBankInfo(HttpServletRequest request,@RequestParam(value="bankId",required=true)Integer bankId) {
		String result = "0";
		try {
			result = tblOrgBankAccountService.removeOrgBankInfo(bankId);
		} catch (Exception e) {
			log.error("异常信息：", e);
			//return JsonBean.error("删除失败");
		}
		return JsonBean.success("删除成功");
	}








	
	
	@PostMapping(value = "/scStatus",produces = "application/json; charset=utf-8")
	@ApiOperation("合同办理-加签")
	public @ResponseBody String scStatus(HttpServletRequest request,
			@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token
			,String contractId//合同id
			,String cyId
			,String transitionName
			,String examination
			,String zfstaffid
			,String zfstaffname
			,String imgBaseStr
			,String describe) throws Exception{
			TblCirculation circulation = tblCirculationService.get(cyId);
			
	    	TblStaffUtil user = DealUserToken.parseUserToken(token);
	    	logger.info("======contractId========"+contractId);
	    	logger.info("======cyId========"+cyId);
	    	logger.info("======transitionName========"+transitionName);
	    	//增加审批记录，并修改合同数据为审查状态
		try {
			TblCyhwUnit aw = tblCyhwUnitService.getEntity(new BigDecimal(contractId));
			TblMyTask task=new TblMyTask();//新增审批记录
			TblMyTask oldtask = tblMyTaskService.findOndbyFrom(contractId.toString());
				//查询执行人 TODO
				task.setApprovaldate(new Date());
				if(user.getTrole()!=null && user.getTrole().getRname()!=null){
					task.setApprovalrole(user.getTrole().getRname());
				}
				task.setApprover(user.getRealname());
				task.setExamination(examination);//审查意见
				task.setFromid(contractId.toString());
				//task.setProcessDefinitionId(processDefinitionId);
				task.setUsrid(user.getStaffid().toString());
				task.setCirid(cyId);
				task.setResult(transitionName);
				task.setProcessName(oldtask.getProcessName());
				//task.setTaskId(taskId);
				task.setProcessInstanceId(circulation.getBusinesskey());
				task.setImgbasestr(imgBaseStr);
				task.setTaskId(oldtask.getTaskId());
				String nextTodo="";
				//修改合同状态为审查各状态
				if(transitionName.equals("审查提交")){
					//转发
					aw.setContractstatus(TblCswlManagement.STATE_SC);//审查中
					task.setHandle(zfstaffname);//下一处理人(审查人)
					task.setFxexam(zfstaffid);//下一处理人(审查人)
					nextTodo=zfstaffid;
					
//					if(user.getLinkDetp().getOrgname().contains("财务")) {
//						task.setKzjzexam("财务");
//					}
//					if(user.getLinkDetp().getOrgname().contains("法律")) {
//						task.setKzjzexam("法务");
//					}
					task.setKzjzexam(user.getLinkDetp().getOrgname());
					
					tblMyTaskService.updateSetting(task,"");//新增流程
				}
				if(transitionName.equals("审查调整")){
					TblStaff formcreater = tblStaffService.findById(circulation.getCystaffid());
					aw.setContractstatus(TblCswlManagement.STATE_SCTZ);//审查调整中
					task.setHandle(formcreater.getRealname());//下一处理人(合同创建人)
					task.setFxexam(formcreater.getStaffid().toString());//下一处理人(合同创建人)
					nextTodo=formcreater.getStaffid().toString();
//					if(user.getLinkDetp().getOrgname().contains("财务")) {
//						task.setKzjzexam("财务");
//					}
//					if(user.getLinkDetp().getOrgname().contains("法律")) {
//						task.setKzjzexam("法务");
//					}
					task.setKzjzexam(user.getLinkDetp().getOrgname());
					
					tblMyTaskService.updateSetting(task,"");//新增流程
				}
				List<TblMyTask> tj = tblMyTaskService.findByObj(contractId.toString(), "审查",user.getStaffid().toString(),user.getLinkDetp().getOrgname());
				if(transitionName.equals("审查调整完成")&&tj.size()>0){
					aw.setContractstatus(TblCswlManagement.STATE_SC);//审查调整中
					task.setHandle(tj.get(0).getApprover());//下一处理人(审查人)
					task.setFxexam(tj.get(0).getUsrid());//下一处理人(审查人)
					nextTodo=tj.get(0).getUsrid();
					TblStaff nexperson = tblStaffService.findById(tj.get(0).getUsrid());
					TblOrganization findByOrgid = tblOrganizaService.findByOrgid(nexperson.getOrgid().toString());
//					if(findByOrgid.getOrgname().contains("法律")) {
//						task.setKzjzexam("法务");
//					}
					task.setKzjzexam(user.getLinkDetp().getOrgname());
					
					tblMyTaskService.updateSetting(task);//新增流程
				}
//				List<TblMyTask> tj2 = tblMyTaskService.findByObj(contractId.toString(), "审查",user.getStaffid().toString(),"财务");
//				if(transitionName.equals("审查调整完成")&&tj2.size()>0){
//					aw.setContractstatus(TblCswlManagement.STATE_SC);//审查调整中
//					task.setHandle(tj2.get(0).getApprover());//下一处理人(审查人)
//					task.setFxexam(tj2.get(0).getUsrid());//下一处理人(审查人)
//					nextTodo=tj2.get(0).getUsrid();
//					TblStaff nexperson = tblStaffService.findById(tj2.get(0).getUsrid());
//					TblOrganization findByOrgid = tblOrganizaService.findByOrgid(nexperson.getOrgid().toString());
//					if(findByOrgid.getOrgname().contains("财务")) {
//						task.setKzjzexam("财务");
//					}
//					tblMyTaskService.updateSetting(task,"");//新增流程
//				}
				if(transitionName.equals("审查完成")){
					//查询执行人 TODO
					String nextapprover = HttpClient.nextapprover(circulation.getBusinesskey());
					aw.setContractstatus(TblCswlManagement.STATE_SP);//合同审查完成后，将状态修改回审批中
					nextTodo=nextapprover;
//					if(user.getLinkDetp().getOrgname().contains("财务")) {
//						task.setKzjzexam("财务");
//					  if(nextTodo.contains(",")) {
//						  nextTodo="财务负责人";
//						  task.setHandle(nextTodo);//下一处理人(审查人)
//					  }
//					}
//					if(user.getLinkDetp().getOrgname().contains("法律")) {
//						task.setKzjzexam("法务");
//						if(nextTodo.contains(",")) {
//							nextTodo="法务负责人";
//							task.setHandle(nextTodo);//下一处理人(审查人)
//						}
//					}
					task.setKzjzexam(user.getLinkDetp().getOrgname());
					if(nextTodo.contains(",")) {
						nextTodo=user.getLinkDetp().getOrgname()+"负责人";
						task.setHandle(nextTodo);//下一处理人(审查人)
					}
					
					tblMyTaskService.updateSetting(task,"");//新增流程
				}
				//String describe = request.getParameter("describe");
				if(describe!=null) {
					aw.setDescribe(describe);//页面元素禁用可能会导致参数无值
				}
				tblCyhwUnitService.updateCyhwUnitNoDescrpib(aw);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return JsonBean.success();
	}
	
	
	/**
	 * 加入黑名单-提交审批
	 */
	@RequestMapping(value = "/htgl/tjsp_hmd",method = {RequestMethod.POST},produces = "application/html; charset=utf-8")
	@ApiOperation(value="加入黑名单-提交审批")
	public @ResponseBody String tjsp_hmd(HttpServletRequest request,
			@ApiParam(name="blackType",value="黑名单类型",required=true)@RequestParam(value="blackType",required=true)Integer blackType,
			@ApiParam(name="budgetId",value="想对方主键",required=true)@RequestParam(value="budgetId",required=true)Integer budgetId,
			@ApiParam(name="datetext",value="黑名单有效期",required=false)@RequestParam(value="datetext",required=false)String datetext,
			@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
			@ApiParam(name="staffId",value="登录用户主键",required=false)@RequestParam(value="staffId",required=false)String staffId){
		String result = null;
		try {
			Map<String,Object>  resultMap = new HashMap<String,Object>(0);
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			if (staff == null) {
				resultMap.put("code", "0");
				resultMap.put("msg", "用户已失效！");
				JSONObject jsonObj = new JSONObject(resultMap);
				result = jsonObj.toString();
				return result;
			}
			TblCyhwProjectbudget entity = tblCyhwProjectbudgetMapper.getEntity(budgetId);
            if (entity.getBlackAprStatus() != null && entity.getBlackAprStatus() == 1) {
                resultMap.put("code", "0");
                resultMap.put("msg", "流程审批中！");
                JSONObject jsonObj = new JSONObject(resultMap);
    			result = jsonObj.toString();
    			return result;
            }
            if (entity.getBlackAprStatus() != null && entity.getBlackAprStatus() == 3) {
                resultMap.put("code", "0");
                resultMap.put("msg", "流程已通过！");
                JSONObject jsonObj = new JSONObject(resultMap);
    			result = jsonObj.toString();
    			return result;
            }
            if ((entity.getBlackAprStatus() != null && (entity.getBlackAprStatus() == 4)|| entity.getBlackAprStatus() == 5 || entity.getBlackAprStatus() == 6)) {
                resultMap.put("code", "0");
                resultMap.put("msg", "流程已完成！");
                JSONObject jsonObj = new JSONObject(resultMap);
    			result = jsonObj.toString();
    			return result;
            }
            entity.setBlacktype(blackType);
			
			if (datetext != null) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				Date date = null;
				date = sdf.parse(datetext);
				entity.setEffectdate(date);
			}
			resultMap = this.tblCyhwUnitService.tjspHmd(entity,"通过",token,staff);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}
	
	/**
	 * 加入黑名单-办理流程
	 */
	@ApiOperation(value="加入黑名单-办理流程")
	@PostMapping(value = "/htgl/blprocesshmd",produces = "application/json; charset=utf-8")
	public @ResponseBody JsonBean blprocesshmd(HttpServletRequest request,
			@ApiParam(name="JsonBean",value="登录用户token",required=true) @RequestHeader("token") String token,
			@ApiParam(name="budgetId",value="相对方主键",required=false)@RequestParam(value = "budgetId",required = true)Integer budgetId,	
			@ApiParam(name="taskId",value="任务ID task.taskId",required=true)@RequestParam(value = "taskId",required = true)String taskId,
			@ApiParam(name="processDefinitionId",value="processDefinitionId task.processDefinitionId",required=true)@RequestParam(value = "processDefinitionId",required = true)String processDefinitionId,
			@ApiParam(name="processInstanceId",value="processInstanceId task.processInstanceId",required=true)@RequestParam(value = "processInstanceId",required = true)String processInstanceId,
			@ApiParam(name="transitionName",value="审批结果 通过、退回",required=true)@RequestParam(value = "transitionName",required = true)String transitionName,
			@ApiParam(name="examination",value="审批意见",required=true)@RequestParam(value = "examination",required = true)String examination,
			@ApiParam(name="cyId",value="cyId cy.cyId",required=true)@RequestParam(value = "cyId",required = true)String cyId,
			@ApiParam(name="blackType",value="黑名单类型",required=false)@RequestParam(value="blackType",required=false)Integer blackType,
			@ApiParam(name="datetext",value="黑名单有效期",required=false)@RequestParam(value="datetext",required=false)String datetext) {
		JsonBean jsonBean = null;
  		try {
  			jsonBean = this.tblCyhwProjectbudgetService.blprocesshmd(token,budgetId,taskId,cyId,transitionName,examination,processInstanceId,processDefinitionId,blackType,datetext);
  		} catch (Exception e) {
  			ResponseFormat.retParam(0,1000,e.getMessage());
  		}
  		return jsonBean;
	}
	
	/**
	 * 加入黑名单-查看办理信息
	 */
	@RequestMapping(value = "/viewHmdProcessInfo",method = {RequestMethod.GET})
	@ApiOperation(value="加入黑名单-查看办理信息")
	public String viewHmdProcessInfo(HttpServletRequest request,
     		@ApiParam(name="cyId",value="审批记录主键",required=true) @RequestParam(value ="cyId", required = true) String cyId,
			@ApiParam(name="budgetId",value="相对方信息主键",required=true)String budgetId,
			@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token
			){
		String result = null;
		try {
			Map<String,Object>  resultMap = this.tblCyhwProjectbudgetService.findOppsiteProcessInfo(token,budgetId,cyId);
			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}
	
	
	/**
	 * 合同首页查询柱状图饼状图
	 */
	@RequestMapping(value ="/contract/getReportContractData",method = {RequestMethod.GET})
	@ApiOperation(value="合同首页")
	public @ResponseBody String getReportContractData(HttpServletRequest request,@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,@RequestParam(value="year",required=false)Integer year,
			@RequestParam(value="month",required=false)Integer month) throws Exception{
		String result = null;
  		try {
  			log.info("合同首页接口");
  			Map<String,Object>  resultMap = this.tblCyhwUnitService.getReportContractData(token,year,month);
			resultMap.put("month", month);
			resultMap.put("year", year);
  			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
  		} catch (Exception e) {
  			ResponseFormat.retParam(0,1000,e.getMessage());
  		}
  		return result;
	}
	
	
	
	/**
	 * 纠纷管理
	 */
	@RequestMapping(value = "/legal/getDisputeNo",method = {RequestMethod.GET},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "法务管理-纠纷登记-查看详情")
	public JsonBean legal_getDisputeNo(HttpServletRequest request,
			@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {

		JsonBean jsonBean = null;
		try {
			jsonBean =  this.tblLegalDisputregistrationService.getDisputeNo(token);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return jsonBean;
	}
	
	/**
	 * 纠纷管理
	 */
	@RequestMapping(value = "/legal/disputeRegisterDetail",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation(value = "法务管理-纠纷登记-查看详情")
	public String legal_disputeRegisterDetail(HttpServletRequest request,
													@RequestParam(value="disputeId",required=true)Integer disputeId) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblLegalDisputregistration dispute = this.tblLegalDisputregistrationService.findBydisputeId(disputeId);
		resultMap.put("dispute", dispute);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/caseInformationList",method = {RequestMethod.POST}, produces = "application/json; charset=utf-8")
	@ApiOperation("纠纷登记页面列表")
	public String legal_caseInfomationList(HttpServletRequest request,
										   TblLegalDisputregistration dispute,
										   @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
										   @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
										   @RequestParam(value = "companyId",required = false)String companyId,
										   @RequestParam(value="flowid",required=false)String flowid,
										    @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal pid = staff.getCurrentOrg().getOrgid();

//		TblFlow flow = tblFlowService.findById(flowid);

		PageInfo<TblLegalDisputregistration> pageInfo = new PageInfo<TblLegalDisputregistration>();
		//dispute.setLinkorg(pid);
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		//pageInfo.setCondition(dispute);
		dispute.setContractname(dispute.getContractname());
		this.tblLegalDisputregistrationService.findListByPageInfo(companyId,pageInfo,dispute,pid);
		resultMap.put("date", pageInfo);
//		resultMap.put("flow", flow);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}

	@RequestMapping(value = "/legal/checkCaseList",method = {RequestMethod.POST}, produces = "application/json; charset=utf-8")
	@ApiOperation("隶属纠纷列表")
	public String checkCaseList(HttpServletRequest request,
										   TblLegalDisputregistration dispute,
										   @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
										   @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
										   @RequestParam(value = "companyId",required = false)String companyId,
										   @RequestParam(value="flowid",required=false)String flowid,
										    @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal pid = staff.getCurrentOrg().getOrgid();

//		TblFlow flow = tblFlowService.findById(flowid);

		PageInfo<TblLegalDisputregistration> pageInfo = new PageInfo<TblLegalDisputregistration>();
		//dispute.setLinkorg(pid);
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		//pageInfo.setCondition(dispute);
		dispute.setContractname(dispute.getContractname());
		dispute.setDisputestatus(6);
		this.tblLegalDisputregistrationService.findListLSByPageInfo(companyId,pageInfo,dispute,pid);
		resultMap.put("date", pageInfo);
//		resultMap.put("flow", flow);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}

	@RequestMapping(value = "/legal/caseInformationSave",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation(value = "法务管理-纠纷登记-新增")
	public String legal_caseInformationSave(HttpServletRequest request,
											 TblLegalDisputregistration dispute,
											 @ApiParam(value="contractid",required=false)@RequestParam(value = "contractid",required = false)Integer contractid,
											 @ApiParam(value="zxstaffid",required=false)@RequestParam(value = "zxstaffid",required = false)Integer zxstaffid,
											 @ApiParam(value="enddate1",required=false)@RequestParam(value = "enddate1",required = false)String enddate1,
											// @RequestParam(value="attids",required=false)String attids,
											  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal pid = staff.getCurrentOrg().getOrgid();
			BigDecimal staffid = staff.getStaffid();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			dispute.setDisputeundertaker(staffid);
			if (contractid != null) {
				TblCyhwUnit unit = this.tblCyhwUnitService.findContractById(contractid);
				this.tblCyhwUnitService.modifyJiuFenContractStatus(new BigDecimal(unit.getContractstatus()), 12, unit.getContractid());
				dispute.setContractinfo(new BigDecimal(contractid));
			}
			if (!StringUtil.isNullOrEmpty(enddate1)) {
				dispute.setLastdealdate(sdf.parse(enddate1));
			}
			dispute.setLinkorg(pid);
			dispute.setCreatestaff(staffid);
			dispute.setDisputestatus(0);
			dispute.setCreatetime(new Date());
			this.tblLegalDisputregistrationService.addDiputregistration(dispute);

			Map<String, Object> resultMap = new HashMap<String, Object>(0);
			resultMap.put("data", dispute.getDisputeid());
			resultMap.put("msg", "新增成功");
			resultMap.put("code", 1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			return jsonObjectMV.toString();
		}catch (Exception e) {
			log.error("接口异常：",e);
		}
		return JsonBean.error("保存失败");
	}

	@RequestMapping(value = "/legal/caseInformationModify",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation(value = "法务管理-纠纷登记-修改")
	public String legal_caseInformationModify(HttpServletRequest request,
											   TblLegalDisputregistration dispute,
											   @RequestParam(value="contractId",required=false)Integer contractId,
											   @RequestParam(value="zxstaffid",required=false)BigDecimal zxstaffid,
											   @RequestParam(value="enddate1",required=false)String enddate1,
											   @RequestParam(value="attids",required=false)String attids) {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			TblLegalDisputregistration oldDispute = this.tblLegalDisputregistrationService.findByDisputeId(dispute.getDisputeid());
//			if(zxstaffid != null) {
//				TblStaff staff = this.tblStaffService.findById(zxstaffid.toString());
//
//			}
			oldDispute.setDisputeundertaker(zxstaffid);
//			dispute.setContractinfo(contractId);
			if(oldDispute.getContractinfo() != null) {
				TblCyhwUnit oldunit = this.tblCyhwUnitService.findContractById(contractId);
				this.tblCyhwUnitService.modifyContractStatus(new BigDecimal(oldunit.getContractstatus()),oldunit.getHistoryStatus(),oldDispute.getContractinfo());
			}
			if(contractId != null) {
				TblCyhwUnit unit = this.tblCyhwUnitService.findContractById(contractId);
				this.tblCyhwUnitService.modifyJiuFenContractStatus(new BigDecimal(unit.getContractstatus()), 12, unit.getContractid());
				dispute.setContractinfo(new BigDecimal(contractId));
				oldDispute.setContractinfo(new BigDecimal(contractId));
			}
			
//			if(enddate1 != null || !"".equals(enddate1)){
//				oldDispute.setLastdealdate(sdf.parse(enddate1));
//			}
			if(StringUtils.isNotBlank(enddate1)){
				oldDispute.setLastdealdate(sdf.parse(enddate1));
			}
			//oldDispute.setLastdealdate(dispute.getLastdealdate());
			oldDispute.setAttorney(dispute.getAttorney());
			oldDispute.setAttorneyphont(dispute.getAttorneyphont());
			oldDispute.setIsattorney(dispute.getIsattorney());
			oldDispute.setDefendant(dispute.getDefendant());
			oldDispute.setDisputecours(dispute.getDisputecours());
			oldDispute.setDisputeitem(dispute.getDisputeitem());
			oldDispute.setDisputeno(dispute.getDisputeno());
			oldDispute.setDisputetype(dispute.getDisputetype());
			oldDispute.setIsuegent(dispute.getIsuegent());
			oldDispute.setWhethersued(dispute.getWhethersued());
			oldDispute.setPlaintiff(dispute.getPlaintiff());
			oldDispute.setUrgentmemo(dispute.getUrgentmemo());
			oldDispute.setSolutionsuggestions(dispute.getSolutionsuggestions());
			oldDispute.setBusinessdate(dispute.getBusinessdate());
			if(dispute!=null && dispute.getLitigationamount()!=null)
			oldDispute.setLitigationamount(dispute.getLitigationamount());
			if (StringUtils.isNotBlank(attids)){
				String[] ids = attids.split(",");
				for (int i = 0; i < ids.length; i++) {
					TblAttachment att = tblAttachmentService.findById(ids[i].trim());
					oldDispute.getAttList().add(att);
				}
			}
			this.tblLegalDisputregistrationService.modifyDiputregistration(oldDispute);
			//return JsonBean.success("修改成功");
			TblCyhwUnit tcu = new TblCyhwUnit();
			tcu.setJbstaff(dispute.getJbstaff());
			tcu.setContractname(dispute.getContractname());
			this.tblCyhwUnitService.saveCyhwUnitTcu(tcu);
			return JsonBean.success("修改成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return dispute.getDisputeid().toString();
	}


	@RequestMapping(value = "/legal/findContractInfo",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-纠纷登记-新增-合同名称")
	public String legal_findContractInfo(HttpServletRequest request,
										 TblCyhwUnit tcu,
										 @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
										 @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
										  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		if(staff == null) {
			resultMap.put("code", "0");
			resultMap.put("msg", "用户已失效！");
			return String.valueOf(resultMap);
		}
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();
		PageInfo<TblCyhwUnit> pageInfo = new PageInfo<TblCyhwUnit>();
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
//		TblCyhwUnit tcu = new TblCyhwUnit();
		tcu.setOrgid(orgid);
		pageInfo.setCondition(tcu);
		tblCyhwUnitService.findLegalContractListByPageInfo(pageInfo,tcu);
		resultMap.put("data", pageInfo);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/uploadutf8",method = {RequestMethod.POST})
	@ApiOperation("法务管理-纠纷登记-新增-上传附件")
	public String uploadutf8(HttpServletRequest request, HttpServletResponse response, String filecollbackurl){
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		// 创建一个通用的多部分解析器
		CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(request.getSession().getServletContext());
		String myFileName = "";
		// 判断 request 是否有文件上传,即多部分请求
		try {
			if (multipartResolver.isMultipart(request)) {
				// 转换成多部分request
				MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
				// 取得request中的所有文件名
				Iterator<String> iter = multiRequest.getFileNames();
				while (iter.hasNext()) {
					// 记录上传过程起始时的时间，用来计算上传时间
					int pre = (int) System.currentTimeMillis();
					// 取得上传文件
					MultipartFile file = multiRequest.getFile(iter.next());
					if (file != null) {
						// 取得当前上传文件的文件名称
						myFileName = file.getOriginalFilename();
						// 如果名称不为“”,说明该文件存在，否则说明该文件不存在
						if (myFileName.trim() != "") {
							System.out.println(myFileName);
							int lastIndexOf = myFileName.lastIndexOf(".");
							String type = myFileName.substring(lastIndexOf);
							// 重命名上传后的文件名
							long timeInMillis = Calendar.getInstance().getTimeInMillis();
							String fileName = timeInMillis + type;
							// 定义上传路径

							String path = DOCDIC + "/" + fileName;
							File localFile = new File(path);
							if (!localFile.exists()) {
								localFile.mkdirs();
							}

							String oldname = myFileName.substring(0,myFileName.lastIndexOf("."));
							String newname=myFileName.replace(oldname, timeInMillis+"");
							try {
								boolean flag = FtpUtil.uploadFile(newname, file.getInputStream());
								if(flag){
									logger.info("上传成功");
								}else{
									logger.info("上传失败");
								}
							} catch (Exception e) {
								log.error("异常信息：", e);
							}
							file.transferTo(localFile);
							resultMap.put("attname", URLEncoder.encode(file.getOriginalFilename(), "UTF-8"));
							resultMap.put("attsize", file.getSize()/1000);
							resultMap.put("attpath", newname);
							resultMap.put("fileName", myFileName);
							resultMap.put("id", request.getParameter("id"));
						}
					}
					Enumeration<String> enu = request.getParameterNames();
					while (enu.hasMoreElements()) {
						String paraName = enu.nextElement();
						if (!paraName.equals("url")) {
							resultMap.put(paraName, request.getParameter(paraName));
						}
						// System.out.println(paraName+": "+request.getParameter(paraName));
					}
					// 记录上传该文件后的时间
					int finaltime = (int) System.currentTimeMillis();
					System.out.println(finaltime - pre);
				}

			}
		} catch (IOException e) {
			log.error("异常信息：", e);
		}
		// return new RedirectView(url,true,false,true);
		return "redirect:" + filecollbackurl;
	}


	@RequestMapping(value = "/sjzb/xmzlzb_fj", produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation("上传附件-返回attid")
	public @ResponseBody String xmzlzb_fj(HttpServletRequest request,
										  HttpServletResponse response,TblAttachment att,
		@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		//TblStaff user = (TblStaff) request.getSession().getAttribute("longUser");
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		response.reset();
		try {
			att.setAttname(URLDecoder.decode(att.getAttname(), "UTF-8"));
		} catch (UnsupportedEncodingException e) {
			log.error("异常信息：", e);
		}
		att.setUploader(staff.getRealname());
		att.setUploadtime(new Date());
		tblAttachmentService.add(att);//原
		TblCyhwBasicuninspection tcp = new TblCyhwBasicuninspection();
		tcp.setFilename(att.getFileName());
		tblCyhwBasicuninspectionService.insertBybudget(tcp);
		return JSONObject.toJSONString(att);
	}


	@RequestMapping(value = "/deleteFileRelation",produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation("删除附件")
	public @ResponseBody String deleteFileRelation(HttpServletRequest request,
						@RequestParam(value = "attid",required = false)String attid){

			tblLegalDisputregistrationService.deleteRelation(attid);
			//TblAttachment att = tblAttachmentService.findById(attid);
			tblAttachmentService.delete(attid);
			return JsonBean.success();
	}
	@Value("${file.path}")
	private String fileUrl;



	@RequestMapping(value = "/downloadFile",produces = "application/json; charset=utf-8")
	@ApiOperation("附件下载")
	public ResponseEntity<byte[]> download(BigDecimal id, HttpServletResponse response) throws IOException {
		TblAttachment tblAttachment = this.tblAttachmentService.get(id);
		//String path = DOCDIC +separator+ tblAttachment.getAttpath();
		String path = fileUrl + tblAttachment.getAttpath();
		File file = new File(path);
		HttpHeaders headers = new HttpHeaders();
		String fileName = processFileName(request, tblAttachment.getAttname());// 为了解决中文名称乱码问题
		headers.setContentDispositionFormData("attachment", fileName);
		headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
		headers.set("Content-disposition", "attachment;filename=" + fileName);
		headers.set("Content-Length", String.valueOf(file.length()));
		return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(file), headers, HttpStatus.OK);
	}

	public static String processFileName(HttpServletRequest request, String fileNames) {
		String codedfilename = null;
		try {
			Integer index = fileNames.indexOf(".");
			if (index < 0) {
				fileNames = fileNames + ".xls";
			}
			String agent = request.getHeader("USER-AGENT");
			if (null != agent && -1 != agent.indexOf("MSIE") || null != agent && -1 != agent.indexOf("Trident")) {// ie

				String name = java.net.URLEncoder.encode(fileNames, "UTF8");

				codedfilename = name;
			} else if (null != agent && -1 != agent.indexOf("Mozilla")) {// 火狐,chrome等

				codedfilename = new String(fileNames.getBytes("UTF-8"), "iso-8859-1");
			}
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return codedfilename;
	}

	@RequestMapping(value = "/legal/caseInformationRemove",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("法务管理-纠纷登记-删除")
	public String legal_caseInformationRemove(HttpServletRequest request,
											  @RequestParam(value="disputeId",required=false)Integer disputeId) {


		try {
			TblLegalDisputregistration oldDispute = this.tblLegalDisputregistrationService.findByDidputeid(disputeId);
			//TblCyhwUnit oldunit = oldDispute.getContract();
			if (oldDispute != null) {
				TblCyhwUnit oldunit = this.tblLegalArbitratsettlementService.findContractByDisputeId(new BigDecimal(disputeId));
				if (oldunit!=null) {
					this.tblCyhwUnitService.modifyContractStatus(new BigDecimal(oldunit.getContractstatus()),oldunit.getHistoryStatus(),oldDispute.getContractinfo());
				}
			}
			//是否删除具体文件？

			//删除附件关联表
			this.tblLegalDisputregistrationService.deleteAttacheMents(1, new BigDecimal(disputeId));
			this.tblLegalDisputregistrationService.removecaseInformation(disputeId);
			return JsonBean.success("删除成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return JsonBean.error("删除失败");
	}


	@RequestMapping(value = "/legal/negotiatedSettlementInfoList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-协商过程-列表")
	public String legal_negotiatedSettlementInfoList(HttpServletRequest request,
													 TblLegalNegotiatedsettlemen negotia,
											@RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
											@RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
											@RequestParam(value="flowid",required=false)String flowid,
											 @RequestParam(value = "disputeid",required = false)Integer disputeid,
											 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		//TblOrganization orgInfo = (TblOrganization) request.getSession().getAttribute("hbOrgEntity");
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal pid = staff.getCurrentOrg().getOrgid();
		TblFlow flow = tblFlowService.findById(flowid);
		if (pageNumber == null) {
			pageNumber = 1;
		}
		PageInfo<TblLegalNegotiatedsettlemen> pageInfo = new PageInfo<TblLegalNegotiatedsettlemen>();
		//negotia.setLinkorg(pid);
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		//pageInfo.setCondition(negotia);
		this.tblLegalNegotiatedsettlemenService.findListByPage(pageInfo,negotia,pid,disputeid);
		request.getSession().setAttribute("flowid", flowid);
		resultMap.put("date", pageInfo);
		resultMap.put("flow", flow);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/negotiatedSettlementDetail",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-协商过程-详情")
	public String  legal_negotiatedSettlementDetail(HttpServletRequest request,
														 @RequestParam(value="negotiaId",required=true)Integer negotiaId) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblLegalNegotiatedsettlemen negotiated = this.tblLegalNegotiatedsettlemenService.findById(negotiaId);
			resultMap.put("negotiated", negotiated);
		} catch (Exception e) {
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/negotiatedSettlemenSave",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("法务管理-协商过程-新增/修改")
	public String legal_negotiatedSettlemenSave(HttpServletRequest request,
												 TblLegalNegotiatedsettlemen negotiated,
												 @RequestParam(value="disputeId",required=false)Integer disputeId,
												 @RequestParam(value="attids",required=false)String attids,
												  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
												 @ApiParam(name = "staffId",required = false)String staffId) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal orgid = staff.getCurrentOrg().getOrgid();
			BigDecimal staffid = staff.getStaffid();
			if(disputeId != null) {
				TblLegalDisputregistration dispute = this.tblLegalDisputregistrationService.findByDisputeId(disputeId);
//				TblCyhwUnit unit = dispute.getContract();
//				this.tblCyhwUnitService.modifyContractStatus(new BigDecimal(unit.getContractstatus()),13,unit.getContractid());
				negotiated.setDispuinfo(new BigDecimal(dispute.getDisputeid()));
			}
			//LocalDateTime dateTime = LocalDateTime.now();
			//negotiated.setCreatetime(dateTime);
			negotiated.setLinkorg(orgid);
			negotiated.setCreatestaff(staffid);
			negotiated.setCreatetime(new Date());
			negotiated.setSchemeStatus(new BigDecimal("0"));
			this.tblLegalNegotiatedsettlemenService.addDiputregistration(negotiated,attids);
			//return JsonBean.success("新增成功");
			resultMap.put("data", negotiated.getNegotiaid() );
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			//return JsonBean.error("失败");
			log.error("异常信息：", e);
		}
		return JsonBean.error("保存失败");

	}

	@RequestMapping(value = "/legal/negotiatedSettlemenModify",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("法务管理-协商过程-新建-协商过程基本信息-修改保存按钮")
	public String legal_negotiatedSettlemenModify(HttpServletRequest request,
												   TblLegalNegotiatedsettlemen negotiated,
												   @RequestParam(value="disputeId",required=false)Integer disputeId,
												   @RequestParam(value="attids",required=false)String attids) {
		try {
			TblLegalNegotiatedsettlemen oldNegotiated = this.tblLegalNegotiatedsettlemenService.findById(negotiated.getNegotiaid());
			if(disputeId != null) {
				TblLegalDisputregistration dispute = this.tblLegalDisputregistrationService.findById(disputeId);
//				TblCyhwUnit unit = dispute.getContract();
//				this.tblCyhwUnitService.modifyContractStatus(new BigDecimal(unit.getContractstatus()),14,unit.getContractid());
//				TblCyhwUnit oldunit = oldNegotiated.getDispu().getContract();
//				this.tblCyhwUnitService.modifyContract(oldunit.getHiscontractstatus(),oldunit.getHiscontractstatus(),oldunit.getContractid());
				oldNegotiated.setDispuinfo(new BigDecimal(dispute.getDisputeid()));
			}
			oldNegotiated.setCounterpart(negotiated.getCounterpart());
			oldNegotiated.setCounterpartphone(negotiated.getCounterpartphone());
			oldNegotiated.setIsaggree(negotiated.getIsaggree());
			oldNegotiated.setNegetiaresult(negotiated.getNegetiaresult());
			oldNegotiated.setJudicialsettlement(negotiated.getJudicialsettlement());
			oldNegotiated.setCourtname(negotiated.getCourtname());
			oldNegotiated.setSolutionmode(negotiated.getSolutionmode());
			if (StringUtils.isNotBlank(attids)){
				String[] ids = attids.split(",");
				for (int i = 0; i < ids.length; i++) {
					TblAttachment att = tblAttachmentService.findById(ids[i].trim());
					oldNegotiated.getAttList().add(att);
				}
			}
			this.tblLegalNegotiatedsettlemenService.modifyNegotiatedSettlement(oldNegotiated);
			return JsonBean.success("修改成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return negotiated.getNegotiaid().toString();
	}


	@RequestMapping(value = "/legal/legalNegotiatedSettlemenRemove",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation("法务管理-协商过程-删除")
	public String legal_legalNegotiatedSettlemenRemove(HttpServletRequest request,@RequestParam(value="negotiaId",required=false)Integer negotiaId) {
		try {
			TblLegalNegotiatedsettlemen negotiated = this.tblLegalNegotiatedsettlemenService.findById(negotiaId);
//			TblCyhwUnit unit = negotiated.getDispuIn().getContract();
//			this.tblCyhwUnitService.modifyContrac(unit.getHiscontractstatus(),new BigDecimal(12),unit.getContractid());
			if (negotiated != null && negotiated.getDisputeid() != null) {
				TblCyhwUnit oldunit = this.tblLegalArbitratsettlementService.findContractByDisputeId(new BigDecimal(negotiated.getDisputeid()));
				if (oldunit!=null) {
					this.tblCyhwUnitService.modifyContractStatus(oldunit.getHiscontractstatus(), 12, oldunit.getContractid());
				}
			}
			//删除过程阶段信息
			List<TblLegalNegotiaterecord> recordList = this.tblLegalNegotiateRecordService.findListBynegotiaId(negotiaId);
			if (recordList !=null) {
				recordList.forEach((record) -> {
					this.tblLegalNegotiateRecordService.removeNegitiateRecord(record.getRecordid());
				});
			}

			//删除附件关联表
			this.tblLegalDisputregistrationService.deleteAttacheMents(2, new BigDecimal(negotiaId));
			this.tblLegalNegotiatedsettlemenService.removeLegalNegotiatedSettlemen(negotiaId);
			return JsonBean.success("删除成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return JsonBean.error("删除失败");
	}


	@RequestMapping(value = "/legal/findcaseInformationInfo",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-协商过程基本信息-新增-隶属纠纷选择接口")
	public String legal_findcaseInformationInfo(HttpServletRequest request,TblLegalDisputregistration dispute,
													  @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
													  @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
												@RequestParam(value="choiceType",required=false)Integer choiceType,
												@RequestParam(value="oid",required=false)BigDecimal oid,
													   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal pid = staff.getCurrentOrg().getOrgid();

		PageInfo<TblLegalDisputregistration> pageInfo = new PageInfo<TblLegalDisputregistration>();
		//dispute.setLinkorg(pid);
		dispute.setUniqueResult(1);
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		//pageInfo.setCondition(dispute);
		this.tblLegalDisputregistrationService.findListByPageInfoDispute(pageInfo,dispute,pid,choiceType,pid);
		resultMap.put("date", pageInfo);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/removeNegotiatedRecord",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation("法务管理-协商过程-新建-协商过程信息-删除")
	public void legal_removeNegotiatedRecord(HttpServletRequest request,
											 @RequestParam(value="recordId",required=false)Integer recordId) {
		//TblLegalNegotiatedsettlemen record = this.tblLegalNegotiatedsettlemenService.findById(recordId);
		this.tblLegalNegotiateRecordService.removeNegitiateRecord(recordId);
	}


	@RequestMapping(value = "/legal/negotiatedSettlemenToModify",method = {RequestMethod.POST})
	@ApiOperation("法务管理-协商过程基本信息-新增回显")
	public String legal_negotiatedSettlemenToModify(HttpServletRequest request,
														  @RequestParam(value="negotiaId",required=true)Integer negotiaId,@RequestParam(value="choiceSearch",required=false)String choiceSearch,
														  @RequestParam(value="flowid",required=true)String flowid) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblFlow flow = tblFlowService.findById(flowid);
		TblLegalNegotiatedsettlemen negotiated = this.tblLegalNegotiatedsettlemenService.findById(negotiaId);
		List<TblLegalNegotiaterecord> recordList = this.tblLegalNegotiateRecordService.findListBynegotiaId(negotiaId);
		resultMap.put("recordList", recordList);
		resultMap.put("flow", flow);
		resultMap.put("negotiated", negotiated);
		resultMap.put("choiceSearch", choiceSearch);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}



	@RequestMapping(value = "/legal/LegalnegotiatedRecordSave",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation("法务管理-协商过程-新建-协商过程信息-新建按钮（协商过程-新建-协商阶段-保存）")
	public Integer legal_negotiatedRecordSave(HttpServletRequest request,
											  TblLegalNegotiaterecord record,
											  @RequestParam(value="negotiaid",required=false)Integer negotiaid,
											  @RequestParam(value="zxstaffid",required=false)String zxstaffid,//我方谈判人
											   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
											  @ApiParam(name = "staffId",required = false)String staffId) {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal orgid = staff.getCurrentOrg().getOrgid();
			BigDecimal staffid = staff.getStaffid();//录入人
			record.setLinkorg(orgid);
			record.setCreatestaff(staffid);
			record.setOurnegotiator(new BigDecimal(zxstaffid));
			record.setNegotiateinfo(new BigDecimal(negotiaid));
			this.tblLegalNegotiateRecordService.saveNegotiateRecord(record);
//			TblStaff ts = new TblStaff();
//			ts.setUsername(record.getZxstaffname());//我方谈判人
//			ts.setRealname(record.getCreatename());//录入人
//			this.tblStaffService.saveTs(ts);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return record.getRecordid();
	}


	@RequestMapping(value = "/legal/negotiateRecordToModify",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("协商过程-新建-协商阶段-保存-回显")
	public String legal_negotiateRecordToModify(HttpServletRequest request,
													  @RequestParam(value="recordId",required=true)Integer recordId,
													  @RequestParam(value="flowid",required=true)Integer flowid,
												@RequestParam(value="negotiaId",required=true)Integer negotiaId) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
//			if (negotiaId != null){
//				TblLegalNegotiatedsettlemen tll =  tblLegalNegotiatedsettlemenService.findNegotiaId(negotiaId);
//				resultMap.put("tll",tll);
//			}else {
//
//			}
			TblLegalNegotiaterecord record = this.tblLegalNegotiateRecordService.findById(recordId);
			resultMap.put("record", record);
			resultMap.put("flowid", flowid);
		} catch (Exception e) {
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/negotiateRecord",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("协商过程-新建-协商阶段-列表")
	public String legal_negotiateRecordToModify(HttpServletRequest request,
												@RequestParam(value="flowid",required=true)Integer flowid,

												@ApiParam(name="pageNumber",value="当前页",required=false,defaultValue = "1")Integer pageNumber,
												@ApiParam(name="pageSize",value="每页数量",required=false,defaultValue = "10")Integer pageSize,
												@RequestParam(value="negotiaId",required=true)Integer negotiaId) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		PageInfo<TblLegalNegotiaterecord> pageInfo = new PageInfo<TblLegalNegotiaterecord>();
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);

		this.tblLegalNegotiateRecordService.findByNegotiaId(pageInfo,negotiaId);
		resultMap.put("date", pageInfo);
		resultMap.put("flowid", flowid);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}




	@RequestMapping(value = "/legal/negotiatedRecordModify",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation("法务管理-协商过程-新建-协商过程信息-协商阶段-修改保存按钮")
	public Integer legal_negotiatedRecordModify(HttpServletRequest request,
												TblLegalNegotiaterecord record,
												@RequestParam(value="zxstaffid",required=false)String zxstaffid) {
		try {
			TblLegalNegotiaterecord oldRecord = this.tblLegalNegotiateRecordService.findById(record.getRecordid());
			//SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//			if(enddate1 != null && !"".equals(enddate1)) {
//				oldRecord.setNegotiationtime(enddate1);
//			}
			if(zxstaffid != null) {
				TblStaff staff = this.tblStaffService.findById(zxstaffid.toString());
				oldRecord.setOurnegotiator(staff.getStaffid());
			}
			oldRecord.setRecordcounterpart(record.getRecordcounterpart());
			oldRecord.setNegotiationmode(record.getNegotiationmode());
			oldRecord.setNegotiationrecord(record.getNegotiationrecord());
			oldRecord.setNegetiationmemoe(record.getNegetiationmemoe());
			oldRecord.setCourtname(record.getCourtname());
			oldRecord.setCourtparter(record.getCourtparter());
			oldRecord.setCourtlink(record.getCourtlink());
			this.tblLegalNegotiateRecordService.modifyNegotiateRecord(oldRecord);
			TblStaff ts = new TblStaff();
			ts.setUsername(oldRecord.getZxstaffname());//我方谈判人
			ts.setRealname(oldRecord.getCreatename());//录入人
			this.tblStaffService.updateTs(ts);
		} catch (Exception e) {
		}
		return record.getRecordid();
	}





	@RequestMapping(value = "/deleteFileRelationAtt",produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation("协商过程-新建-协商过程信息-最下面删除附件")
	public @ResponseBody String deleteFileRelationAtt(HttpServletRequest request,
												   @RequestParam(value = "attid",required = false)String attid){

		tblLegalNegotiateeAttService.deleteRelation(attid);
		//TblAttachment att = tblAttachmentService.findById(attid);
		tblAttachmentService.delete(attid);
		return JsonBean.success();
	}


	@RequestMapping(value = "/legal/litigationSettlement",method = {RequestMethod.POST},produces = {"application/json;charset=UTF-8"})
	@ApiOperation("法务管理-诉讼过程列表")
	public String legal_litigationSettlementList(HttpServletRequest request,
													   TblLegalLitigationsettlement litigation,
													   @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
													   @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
													   @RequestParam(value="flowid",required=false)String flowid,
												  @RequestParam(value = "disputeid",required = false) Integer disputeid,
												  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal pid = staff.getCurrentOrg().getOrgid();

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblFlow flow = tblFlowService.findById(flowid);

			PageInfo<TblLegalLitigationsettlement> pageInfo = new PageInfo<TblLegalLitigationsettlement>();
			litigation.setLinkorg(pid);
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			this.tblLegalLitigationsettlementService.findListByPageInfo(pageInfo,litigation,disputeid);
			request.getSession().setAttribute("flowid", flowid);
			resultMap.put("data", pageInfo);
			resultMap.put("flow", flow);
		} catch (Exception e) {
			log.error("异常信息：",e);
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/litigationSettlementDetail",method = {RequestMethod.POST},produces = {"application/json;charset=UTF-8"})
	@ApiOperation("法务管理-诉讼过程-查看详情")
	public String legal_litigationSettlementDetail(HttpServletRequest request,
														 @RequestParam(value="litigationId",required=true)Integer litigationId) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);

		TblLegalLitigationsettlement litigation = this.tblLegalLitigationsettlementService.findById(litigationId);
		if (litigation != null && litigation.getDisputeid() !=null){
			//因为sql关联表的问题 只能单独查数据
			TblLegalDisputregistration disputregistration = tblLegalDisputregistrationService.findById(litigation.getDisputeid());
			litigation.setDisputeidname(disputregistration.getDisputeno());
		}
		if (litigation != null && litigation.getDisputeunder() !=null){
			TblStaff byId = tblStaffService.findById(litigation.getDisputeunder().toString());
			litigation.setDisputeundername(byId.getRealname());
		}
		resultMap.put("litigation", litigation);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/litigationSettlementSave",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("法务管理-诉讼过程-新建接口")
	public String legal_litigationSettlementSave(HttpServletRequest request,
												  TblLegalLitigationsettlement litigation,
												  @RequestParam(value="disputeId",required=false)Integer disputeId,
											//	  @RequestParam(value="attids",required=false)String attids,
//												  @RequestParam(value="shouliDate",required=false)String shouliDate,
//												  @RequestParam(value="jieanDate",required=false)String jieanDate
//													,@RequestParam(value="openDate",required=false)String openDate,
												   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			String orgid = staff.getCurrentOrg().getOrgid().toString();
			BigDecimal staffid = staff.getStaffid();
			if(disputeId != null) {
				TblLegalDisputregistration dispute = this.tblLegalDisputregistrationService.findById(disputeId);
//				TblCyhwUnit unit = dispute.getContract();
//				this.tblCyhwUnitService.modifyContractStatus(new BigDecimal(unit.getContractstatus()),14,unit.getContractid());
				litigation.setDisputeinfo(new BigDecimal(dispute.getDisputeid()));
			}
			//SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd");
			LocalDateTime dateTime = LocalDateTime.now();

			litigation.setLinkorg(new BigDecimal(orgid));
			litigation.setCreatestaff(staffid);
			litigation.setCreatetime(dateTime);
			this.tblLegalLitigationsettlementService.addLitigationSettlement(litigation);
			resultMap.put("data", litigation.getLitigationid() );
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
			//return JsonBean.success("新增成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return litigation.getLitigationid().toString();
	}



	@RequestMapping(value = "/legal/proceedingsRecordSave",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("法务管理-诉讼过程-新建-诉讼过程记录-新建按钮")
	public String legal_proceedingsRecordSave(HttpServletRequest request, TblLegalProceedingsrecord proceed,
											   @RequestParam(value="litigationId",required=false)Integer litigationId,
											    @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
											    @ApiParam(name="attids",value="上传附件的ID",required=false) @RequestParam(value="attids", required = false) String attids,
											   @ApiParam(name = "staffId",required = false)String staffId) {
		try {
			String result = null;
			Map<String, Object> resultMap = new HashMap<String, Object>(0);
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//			TblOrganization attribute = (TblOrganization) request.getSession().getAttribute("hbOrgEntity");
//			TblStaff user = (TblStaff) request.getSession().getAttribute("longUser");
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal orgid = staff.getCurrentOrg().getOrgid();
			BigDecimal staffid = staff.getStaffid();
			TblLegalLitigationsettlement litigation = this.tblLegalLitigationsettlementService.findById(litigationId);
			proceed.setLinkorg(orgid);
			proceed.setCreatestaff(staffid);
			proceed.setState(0);//新增为0状态为未审批
			proceed.setLitigationinfo(new BigDecimal(litigation.getLitigationid()));
			this.tblLegalProceedingsrecordService.saveProceedingRecord(proceed,attids);
//			TblStaff ts = new TblStaff();
//			ts.setUsername(proceed.getUsername());
//			tblStaffService.saveTs(ts);
//			return JsonBean.success("新增成功");
			resultMap.put("data", proceed.getProceedid());
			resultMap.put("code", 1);
			resultMap.put("msg", "添加成功");
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}

		return proceed.getProceedid().toString();
	}


	@RequestMapping(value = "/legal/proceedingsRecord",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-诉讼过程-新建-诉讼过程记录-列表")
	public String legal_proceedingsRecord(HttpServletRequest request,
												@RequestParam(value="flowid",required=true)Integer flowid,
												@ApiParam(name="pageNumber",value="当前页",required=false,defaultValue = "1")Integer pageNumber,
												@ApiParam(name="pageSize",value="每页数量",required=false,defaultValue = "10")Integer pageSize,
												@RequestParam(value="litigationId",required=true)Integer litigationId) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		PageInfo<TblLegalProceedingsrecord> pageInfo = new PageInfo<TblLegalProceedingsrecord>();
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);

		this.tblLegalProceedingsrecordService.findByNegotiaId(pageInfo,litigationId);
		resultMap.put("date", pageInfo);
		resultMap.put("flowid", flowid);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}

	@GetMapping("/legal/proceedingsRecord/{id}")
	@ApiOperation("法务管理-诉讼过程-新建-诉讼过程记录-详情 id为列表的proceedid")
	public TblLegalProceedingsrecord legal_proceedingsRecordModify(@PathVariable("id") Integer id) {
		try {
			TblLegalProceedingsrecord proceedingsrecord = this.tblLegalProceedingsrecordService.findById(id);
			if (proceedingsrecord != null && proceedingsrecord.getCreatestaff() != null){
				TblStaff byId = tblStaffService.findById(proceedingsrecord.getCreatestaff().toString());
				proceedingsrecord.setUsername(byId.getRealname());
			}
			return proceedingsrecord;
		} catch (Exception e) {
			log.error("异常信息：", e);
			return null;
		}
	}

	@GetMapping("/legal/listatt")
	@ApiOperation("法务管理-诉讼过程-诉讼过程记录-查询附件")
	public JsonBean listatt(@RequestParam(value="litigationId",required=false)Integer litigationId,
			@ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) {
		JsonBean jsonBean = null;
		try {
			jsonBean = tblLegalProceedingsrecordService.getAttListBylitigationId(token, litigationId);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return jsonBean;
	}
	
	@RequestMapping(value = "/legal/delRecordAttInfo", produces = "application/json; charset=utf-8", method = {RequestMethod.POST})
	@ApiOperation(value = "法务管理-诉讼过程-诉讼过程记录-查询附件")
    public JsonBean delRecordAttInfo(HttpServletRequest request,
    		@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token")String token,
    		@ApiParam(name="attid",value="附件ID ",required=true)@RequestParam(value = "attid", required = true)  BigDecimal attid) {
    	JsonBean jsonBean = null;
		try {
			jsonBean = this.tblLegalProceedingsrecordService.delAttListBylitigationId(token,attid);
		} catch (Exception e) {
			e.printStackTrace();
			ResponseFormat.retParam(0,1000,e.getMessage());
		}
		return jsonBean;
    }
	
	@RequestMapping(value = "/legal/proceedingsRecordModify",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("法务管理-诉讼过程-新建-诉讼过程记录-修改")
	public String legal_proceedingsRecordModify(HttpServletRequest request,
												 TblLegalProceedingsrecord proceed,
												 @ApiParam(name="attids",value="上传附件的ID",required=false) @RequestParam(value="attids", required = false) String attids,
												 @RequestParam(value="litigationId",required=false)Integer litigationId) {
		try {
			TblLegalProceedingsrecord oldproceed = this.tblLegalProceedingsrecordService.findById(proceed.getProceedid());
			oldproceed.setProceedno(proceed.getProceedno());
			oldproceed.setPorceedstage(proceed.getPorceedstage());
			oldproceed.setCourt(proceed.getCourt());
			oldproceed.setCourtlink(proceed.getCourtlink());
			oldproceed.setCourtcontact(proceed.getCourtcontact());
			oldproceed.setIsexternallawyer(proceed.getIsexternallawyer());
			oldproceed.setLawyearword(proceed.getLawyearword());
			oldproceed.setLawyearname(proceed.getLawyearname());
			oldproceed.setLawyearlink(proceed.getLawyearlink());
			oldproceed.setNegotiator(proceed.getNegotiator());
			oldproceed.setNegotiatorlink(proceed.getNegotiatorlink());
			oldproceed.setPresedingjudge(proceed.getPresedingjudge());
			oldproceed.setCasepromotion(proceed.getCasepromotion());
			oldproceed.setExistingdifficulties(proceed.getExistingdifficulties());
			oldproceed.setMeasurespromote(proceed.getMeasurespromote());
			oldproceed.setFilingtime(proceed.getFilingtime());
			oldproceed.setPaymentremindtime(proceed.getPaymentremindtime());
			oldproceed.setOpeningtime(proceed.getOpeningtime());
			oldproceed.setJudgetiem(proceed.getJudgetiem());
			System.out.println(oldproceed);
			this.tblLegalProceedingsrecordService.modifyNegotiateRecord(oldproceed,attids);
			return JsonBean.success("修改成功");
		} catch (Exception e) {
		}
		return proceed.getProceedid().toString();
	}


	@RequestMapping(value = "/legal/removeLegalProceedingsRecord",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation("法务管理-诉讼过程-新建-诉讼过程记录-删除")
	public void legalremoveLegalProceedingsRecord(HttpServletRequest request,
												  @RequestParam(value="proceedId",required=false)Integer proceedId) {

		//TblLegalProceedingsrecord oldproceed = this.tblLegalProceedingsRecordService.findById(proceedId);
		tblLegalProceedingsrecordService.removeNegitiateRecord(proceedId);
	}


	@RequestMapping(value = "/ssgcjlfj/deleteFileRelation",produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation("法务管理-诉讼过程-新建-诉讼过程记录-附近-删除")
	public @ResponseBody String ssgcjlfjDeleteFileRelation(HttpServletRequest request,
												   @RequestParam(value = "attid",required = false)String attid){
		tblLegalLsettlementAttService.deleteRelation(attid);
		TblAttachment att = tblAttachmentService.findById(attid);
		tblAttachmentService.deleteAttid(att);
		return JsonBean.success();
	}


	@RequestMapping(value = "/legal/litigationSettlementModify",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation("法务管理-诉讼过程-修改保存按钮")
	public String legal_litigationSettlementModify(HttpServletRequest request,
													TblLegalLitigationsettlement litigation,
													@RequestParam(value="disputeId",required=false)Integer disputeId,
													@RequestParam(value="attids",required=false)String attids
													//,@RequestParam(value="shouliDate",required=false)String shouliDate,
												//	@RequestParam(value="jieanDate",required=false)String jieanDate
			//,@RequestParam(value="openDate",required=false)String openDate
	) {
		try {
//			TblLegalLitigationsettlement oldLitigation = this.tblLegalLitigationsettlementService.findByLitigationid(litigation.getLitigationid());
//			SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd");
//			if(disputeId != null) {
//				//TblCyhwUnit oldunit  = oldLitigation.getDispute().getContract();
//				//this.tblCyhwUnitService.modifyContractStatus(oldunit.getHiscontractstatus(),12,oldunit.getContractid());
//				TblLegalDisputregistration dispute = this.tblLegalDisputregistrationService.findById(disputeId);
//				//TblCyhwUnit unit =dispute.getContract();
//				//this.tblCyhwUnitService.modifyContractStatus(new BigDecimal(unit.getContractstatus()),14,unit.getContractid());
//				oldLitigation.setDispute(dispute);
//			}
//
//			oldLitigation.setFirstcourt(litigation.getFirstcourt());
//			oldLitigation.setPresidingjudge(litigation.getPresidingjudge());
//			oldLitigation.setCollegialpanel(litigation.getCollegialpanel());
//			oldLitigation.setLitigationamount(litigation.getLitigationamount());
//			oldLitigation.setLitigationresult(litigation.getLitigationresult());
//			oldLitigation.setIseffect(litigation.getIseffect());
//			oldLitigation.setJudgemoney(litigation.getJudgemoney());
//			oldLitigation.setActionobject(litigation.getActionobject());
			
			
			if (StringUtils.isNotBlank(attids)){
				String[] ids = attids.split(",");
				for (int i = 0; i < ids.length; i++) {
					this.tblLegalLsettlementAttService.insertAttRelation(litigation.getLitigationid(),ids[i]);
					return JsonBean.success("新增成功");
				}
			}
			this.tblLegalLitigationsettlementService.updateModifyLitigationSettlement(litigation);
			return JsonBean.success("修改成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return litigation.getLitigationid().toString();
	}



	@RequestMapping(value = "/legal/litigationSettlementRemove",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation("法务管理-诉讼过程-删除")
	//和老代码一模一样，但是老代码不能删除，单删本表时提示有外键约束，不知道怎么解决老代码的删除逻辑，所以只能完全照搬
	public String legal_litigationSettlementRemove(HttpServletRequest request,@RequestParam(value="litigationId",required=false)Integer litigationId) {
		try {
			TblLegalLitigationsettlement oldLitigation = this.tblLegalLitigationsettlementService.findById(litigationId);
			if (oldLitigation != null && oldLitigation.getDisputeid() != null) {
				TblCyhwUnit oldunit = this.tblLegalArbitratsettlementService.findContractByDisputeId(new BigDecimal(oldLitigation.getDisputeid()));
				if (oldunit!=null) {
					this.tblCyhwUnitService.modifyContractStatus(oldunit.getHiscontractstatus(), 12, oldunit.getContractid());
				}
			}

			//删除过程记录
			List<TblLegalProceedingsrecord> recordList = this.tblLegalProceedingsrecordService.findListByLitigationid(litigationId);
			recordList.forEach((record) -> {
				this.tblLegalProceedingsrecordService.removeNegitiateRecord(record.getProceedid());
			});

			//删除附件关联表
			this.tblLegalDisputregistrationService.deleteAttacheMents(3, new BigDecimal(litigationId));
			this.tblLegalLitigationsettlementService.removeLitigationSettlement(oldLitigation.getLitigationid());
			return JsonBean.success("删除成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return JsonBean.error("删除失败");
	}


	@RequestMapping(value = "/legal/ArbitratSettlementDetail",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-仲裁过程-查看详情")
	public String ArbitratSettlementDetail(HttpServletRequest request,
												 @RequestParam(value="arbitraId",required=true)Integer arbitraId) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblLegalArbitratsettlement arbitrat = this.tblLegalArbitratsettlementService.findById(arbitraId);
			resultMap.put("arbitrat", arbitrat);
		} catch (Exception e) {
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/ArbitratSettlementInfoList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-仲裁过程-列表")
	public String ArbitratSettlementInfoList(HttpServletRequest request,
												   TblLegalArbitratsettlement negotia,
												   @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
												   @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
												   @RequestParam(value="flowid",required=false)String flowid,
												  @RequestParam(value = "disputeid",required = false)Integer disputeid,
											  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);

		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		if(staff == null) {
			resultMap.put("code", "0");
			resultMap.put("msg", "用户已失效！");
			return String.valueOf(resultMap);
		}
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();
		TblFlow flow = tblFlowService.findById(flowid);

		PageInfo<TblLegalArbitratsettlement> pageInfo = new PageInfo<TblLegalArbitratsettlement>();
		negotia.setLinkorg(orgid);
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		//pageInfo.setCondition(negotia);
		this.tblLegalArbitratsettlementService.findListByPageInfo(pageInfo,negotia,disputeid);
		request.getSession().setAttribute("flowid", flowid);
		resultMap.put("date", pageInfo);
		resultMap.put("flow", flow);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}

	@RequestMapping(value = "/legal/ArbitratSettlementSave",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-仲裁过程-新建按钮/保存按钮")
	@ResponseBody
	public String ArbitratSettlementSave(
										  TblLegalArbitratsettlement arbitrat,
										  @RequestParam(value="negotiaId",required=false)Integer negotiaId,
										   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token
//										  @RequestParam(value="asDealDateST",required=false) String asdealdate,
//										  @RequestParam(value="asFirsthearingDateST",required=false) String asfirsthearingdate,
//										  @RequestParam(value="arbitratiionEndDateST",required=false) String arbitrationenddate
										  ) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal orgid = staff.getCurrentOrg().getOrgid();
			BigDecimal staffId = staff.getStaffid();
			if(negotiaId != null) {
				TblLegalNegotiatedsettlemen negotiateInfo = this.tblLegalNegotiatedsettlemenService.findById(negotiaId);
//				TblCyhwUnit unit = new TblCyhwUnit();
//				this.tblCyhwUnitService.modifyContractStatus(new BigDecimal(unit.getContractstatus()),15,unit.getContractid());
				arbitrat.setNegotiateinfo(negotiateInfo.getNegotiaid());
			}

			arbitrat.setLinkorg(orgid);
			arbitrat.setCreatestaff(staffId);
			arbitrat.setCreatetime(new Date());
			this.tblLegalArbitratsettlementService.addDiputregistration(arbitrat);
			resultMap.put("data", arbitrat.getArbitraid() );
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
			//return JsonBean.success("新增成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return arbitrat.getArbitraid().toString();
	}


	@RequestMapping(value = "/legal/findNegotiatedSettlemenA",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-仲裁过程-新建-仲裁基本信息-协商信息选择接口")
	public String findNegotiatedSettlemenA(HttpServletRequest request,
												 TblLegalNegotiatedsettlemen negotiate,
												 @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
												 @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
										    @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);

		//TblOrganization orgInfo = (TblOrganization) request.getSession().getAttribute("hbOrgEntity");
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();
		PageInfo<TblLegalNegotiatedsettlemen> pageInfo = new PageInfo<TblLegalNegotiatedsettlemen>();
		negotiate.setLinkorg(orgid);
		negotiate.setJudicialsettlement(new BigDecimal(2));
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		//pageInfo.setCondition(negotiate);
		this.tblLegalNegotiatedsettlemenService.findListForLitiationList(pageInfo,negotiate);
		resultMap.put("date", pageInfo);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}

	@RequestMapping(value = "/legal/ArbitratSettlementModify",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("法务管理-仲裁过程-修改按钮")
	public String legal_negotiatedSettlemenModify(HttpServletRequest request,
												   TblLegalArbitratsettlement arbitrat,
												   @RequestParam(value="negotiaId",required=false)Integer negotiaId,
												   @RequestParam(value="attids",required=false)String attids) {
		//SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		try {
//			TblLegalArbitratsettlement oldArbitrat = this.tblLegalArbitratsettlementService.findById(arbitrat.getArbitraid());
//			if(negotiaId != null) {
//				TblLegalNegotiatedsettlemen negotiateInfo = this.tblLegalNegotiatedsettlemenService.findById(negotiaId);
//				oldArbitrat.setNegotiateinfo(negotiateInfo.getNegotiaid());
//			}
//			oldArbitrat.setCourtfirst(arbitrat.getCourtfirst());
//			oldArbitrat.setArbitrationamount(arbitrat.getArbitrationamount());
//			oldArbitrat.setArbitrationresult(arbitrat.getArbitrationresult());

			if (StringUtils.isNotBlank(attids)){
				String[] ids = attids.split(",");
				for (int i = 0; i < ids.length; i++) {
					TblAttachment att = tblAttachmentService.findById(ids[i].trim());
					arbitrat.getAttList().add(att);
				}
			}
			this.tblLegalArbitratsettlementService.modifyNegotiatedSettlement(arbitrat);
			return JsonBean.success("修改成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return arbitrat.getArbitraid().toString();
	}


	@RequestMapping(value = "/legal/ArbitratSettlementToModify",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-仲裁过程-新建按钮/保存按钮/修改回显")
	public String ArbitratSettlementToModify(HttpServletRequest request,
												   @RequestParam(value="arbitraId",required=true)Integer arbitraId,
												   @RequestParam(value="choiceSearch",required=false)String choiceSearch,
												   @RequestParam(value="flowid",required=true)String flowid) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblFlow flow = tblFlowService.findById(flowid);
			TblLegalArbitratsettlement arbitrat = this.tblLegalArbitratsettlementService.findById(arbitraId);
			List<TblLegalArbitrationrecord> recordList = this.tblLegalArbitrationrecordService.findListBynegotiaId(arbitraId);
			resultMap.put("recordList", recordList);
			resultMap.put("flow", flow);
			resultMap.put("arbitrat", arbitrat);
			resultMap.put("choiceSearch", choiceSearch);
		} catch (Exception e) {
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/ArbitrationRecordSave",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("法务管理-仲裁过程-新建-仲裁过程信息-新增")
	public String ArbitrationRecordSave(HttpServletRequest request,
										 TblLegalArbitrationrecord record,
										 @RequestParam(value="arbitraId",required=false)Integer arbitraId,
										 @RequestParam(value="zxstaffid",required=false)String zxstaffid,
										    @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			TblStaffUtil st = DealUserToken.parseUserToken(token);
			BigDecimal orgid = st.getCurrentOrg().getOrgid();
			BigDecimal stId = st.getStaffid();
			//TblLegalArbitratsettlement arbitrat = this.tblLegalArbitratsettlementService.findByarbitraid(arbitraId);

			if(zxstaffid != null) {
				TblStaff staff = this.tblStaffService.findById(zxstaffid);
				record.setCreatestaff(staff.getStaffid());
			}
			record.setLinkorg(orgid);
			record.setCreatestaff(stId);
			//record.setCreatetime(new Date());
			record.setArbitrationinfo(arbitraId);
			this.tblLegalArbitrationrecordService.saveNegotiateRecord(record);
			return JsonBean.success("新增成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return record.getArrecordid().toString();
	}


	@RequestMapping(value = "/legal/ArbitrationRecordToModify",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-仲裁过程-新建-仲裁过程信息-新增保存回显")
	public String ArbitrationRecordToModify(HttpServletRequest request,
												  @RequestParam(value="arrecordId",required=true)Integer arrecordId,
											@RequestParam(value="flowid",required=true)Integer flowid) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblLegalArbitrationrecord record = this.tblLegalArbitrationrecordService.findById(arrecordId);
		resultMap.put("record", record);
		resultMap.put("flowid", flowid);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@SneakyThrows
	@RequestMapping(value = "/legal/ArbitrationRecordModify",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("法务管理-仲裁过程-新建-仲裁过程信息-修改保存")
	public String ArbitrationRecordModify(HttpServletRequest request,
										   TblLegalArbitrationrecord record,
										   //@RequestParam(value="dealDateST",required=false)String dealDateST,
										   @RequestParam(value="zxstaffid",required=false)String zxstaffid) {
//		TblLegalArbitrationrecord oldRecord = this.tblLegalArbitrationrecordService.findById(record.getArrecordid());
//		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//		if(record.getDealdate() != null && !"".equals(record.getDealdate())) {
//			oldRecord.setDealdate(sdf.parse(record.getDealdate().toString()));
//		}
//		oldRecord.setArstage(record.getArstage());
//		oldRecord.setArcontactperson(record.getArcontactperson());
//		oldRecord.setOurcontractperson(record.getOurcontractperson());
//		oldRecord.setArrecordmode(record.getArrecordmode());
//		oldRecord.setArrecordmemo(record.getArrecordmemo());
		this.tblLegalArbitrationrecordService.modifyNegotiateRecord(record);
		return JsonBean.success("修改成功");
		//return record.getArrecordid();
	}


	@RequestMapping(value = "/legal/ArbitrationRecord",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-仲裁过程-新增-仲裁过程信息-列表")
	public String ArbitrationRecord(HttpServletRequest request,
											 @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
											 @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
											 @RequestParam(value="flowid",required=false)String flowid,
									@RequestParam(value="arrecordid",required=false) Integer arrecordid){

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);


		PageInfo<TblLegalArbitrationrecord> pageInfo = new PageInfo<TblLegalArbitrationrecord>();
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		this.tblLegalArbitrationrecordService.findListByPageInfo(pageInfo,arrecordid);
		request.getSession().setAttribute("flowid", flowid);
		resultMap.put("data", pageInfo);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}

	@RequestMapping(value = "/zcgcxjfjsc/deleteFileRelation",produces = "application/json; charset=utf-8",method = {RequestMethod.POST})
	@ApiOperation("法务管理-仲裁过程-新建-仲裁过程信息-附件-删除")
	public @ResponseBody String zcgcxjfjscDeleteFileRelation(HttpServletRequest request,
														   @RequestParam(value = "attid",required = false)String attid){
		tblLegalArbitrationAttService.deleteRelation(attid);
		TblAttachment att = tblAttachmentService.findById(attid);
		tblAttachmentService.deleteAttid(att);
		return JsonBean.success("删除成功");
	}


	@RequestMapping(value = "/legal/removeArbitrationRecord",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("法务管理-仲裁过程-新建-仲裁过程信息-删除")
	public String removeArbitrationRecord(HttpServletRequest request,
										@RequestParam(value="arrecordId",required=false)Integer arrecordId) {
		try {
			//TblLegalArbitrationrecord record = this.tblLegalArbitrationrecordService.findById(arrecordId);
			this.tblLegalArbitrationrecordService.removeNegitiateRecord(arrecordId);
			return JsonBean.success("删除成功");
		} catch (Exception e) {
		}
		return JsonBean.error("删除失败");
	}


	@RequestMapping(value = "/legal/ArbitratSettlementRemove",method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation("法务管理-仲裁过程-删除")
	public String ArbitratSettlementRemove(HttpServletRequest request,@RequestParam(value="arbitraId",required=false)Integer arbitraId) {
		try {
			TblLegalArbitratsettlement arbitrat = this.tblLegalArbitratsettlementService.findById(arbitraId);
			if (arbitrat != null && arbitrat.getDisputeid() != null) {
				//TblCyhwUnit oldunit =arbitrat.getNegotiate().getDispu().getContract();
//				TblCyhwUnit oldunit = new TblCyhwUnit();
				TblCyhwUnit oldunit = this.tblLegalArbitratsettlementService.findContractByDisputeId(arbitrat.getDisputeid());
				if (oldunit!=null) {
					this.tblCyhwUnitService.modifyContractStatus(oldunit.getHiscontractstatus(), 13, oldunit.getContractid());
				}
			}
			List<TblLegalArbitrationrecord> recordList = this.tblLegalArbitrationrecordService.findListBynegotiaId(arbitraId);
			recordList.forEach((record) -> {
				this.tblLegalArbitrationrecordService.removeNegitiateRecord(record.getArrecordid());
			});
			//删除附件关联表
			this.tblLegalDisputregistrationService.deleteAttacheMents(4, new BigDecimal(arbitraId));
			this.tblLegalArbitratsettlementService.removeLegalNegotiatedSettlemen(arbitrat.getArbitraid());
			return JsonBean.success("删除成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return JsonBean.error("删除失败");
	}


	@RequestMapping(value = "/legal/disputeSettlementList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-纠纷结案列表")
	public String legal_disputeSettlementList(HttpServletRequest request,
											  TblLegalCloseinformation closeInfo,
											  @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
											  @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
											  @RequestParam(value="flowid",required=false)String flowid,
											   @RequestParam(value ="disputeid",required = false )Integer disputeid,
											   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblFlow flow = tblFlowService.findById(flowid);
		PageInfo<TblLegalCloseinformation> pageInfo = new PageInfo<TblLegalCloseinformation>();
		closeInfo.setLinkorg(orgid);
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		//pageInfo.setCondition(closeInfo);
		this.tblLegalCloseinformationService.findListByPageInfo(pageInfo,closeInfo,disputeid);
		resultMap.put("date", pageInfo);
		resultMap.put("flow", flow);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/disputeSettlementDetail",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-纠纷结案-查看详情")
	public String legal_disputeSettlementDetail(HttpServletRequest request,
													  @RequestParam(value="closeId",required=true)Integer closeId) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblLegalCloseinformation oldCloseInfo = this.tblLegalCloseinformationService.findById(closeId);
			resultMap.put("closeInfo", oldCloseInfo);
		} catch (Exception e) {
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/disputeSettlementSave",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-纠纷结案-新增")
	@ResponseBody
	public String legal_disputeSettlementSave(HttpServletRequest request,
											   TblLegalCloseinformation closeInfo,
											   @RequestParam(value="disputeId",required=false)Integer disputeId,
//											  @RequestParam(value="jieanDate",required=false)String jieanDate,
											    @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
											   @RequestParam(value="flowid",required=true)String flowid) {
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal orgid = staff.getCurrentOrg().getOrgid();
			BigDecimal staffid = staff.getStaffid();
			if(disputeId != null) {
				TblLegalDisputregistration dispute = this.tblLegalDisputregistrationService.findById(disputeId);
				if(dispute.getContractinfo() != null) {
					TblCyhwUnit unit =  this.tblCyhwUnitService.getEntity(dispute.getContractinfo());
					this.tblCyhwUnitService.modifyContractStatus(new BigDecimal(unit.getContractstatus()),16,unit.getContractid());
				}
//				TblCyhwUnit unit = dispute.getContractinfo();
//				this.tblCyhwUnitService.modifyContractStatus(new BigDecimal(unit.getContractstatus()),16,unit.getContractid());
				closeInfo.setDisputinfo(new BigDecimal(dispute.getDisputeid()));
			}
			closeInfo.setLinkorg(orgid);
			closeInfo.setCreatestaff(staffid);
			closeInfo.setCreatetime(new Date());
			this.tblLegalCloseinformationService.addDisputeSettlement(closeInfo);
			return JsonBean.success("新增成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
//		String result = null;
//		Map<String,Object> resultMap = new HashMap<String, Object>(0);
//		Integer closeid = closeInfo.getCloseid();
//		TblFlow flow = tblFlowService.findById(flowid);
//		TblLegalCloseinformation mation = this.tblLegalCloseinformationService.findById(closeid);
//		resultMap.put("flow", flow);
//		resultMap.put("mation", mation);
//		JSONObject jsonObjectMV = new JSONObject(resultMap);
//		result = jsonObjectMV.toString();
		return closeInfo.getCloseid().toString();
	}
	
	@RequestMapping(value = "/legal/disputeSettlementToModify",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-纠纷结案-保存回显、修改回显")
	public String  legal_disputeSettlementToModify(HttpServletRequest request,
														@RequestParam(value="closeId",required=true)Integer closeId,
														@RequestParam(value="flowid",required=true)String flowid) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblFlow flow = tblFlowService.findById(flowid);
			TblLegalCloseinformation closeInfo = this.tblLegalCloseinformationService.findById(closeId);
			resultMap.put("flow", flow);
			resultMap.put("closeInfo", closeInfo);
		} catch (Exception e) {
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/disputeSettlementModify",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-纠纷结案-修改")
	@ResponseBody
	public String legal_disputeSettlementModify(HttpServletRequest request,
												 TblLegalCloseinformation closeInfo,
												 @RequestParam(value="disputeid",required=false)Integer disputeid){
		try {
			TblLegalCloseinformation oldCloseInfo = this.tblLegalCloseinformationService.findById(closeInfo.getCloseid());
			TblLegalDisputregistration predispute = this.tblLegalDisputregistrationService.findById(oldCloseInfo.getDisputinfo().intValue());
			if(predispute != null && predispute.getContractinfo() != null) {
				TblCyhwUnit preunit =  this.tblCyhwUnitService.getEntity(predispute.getContractinfo());
				this.tblCyhwUnitService.modifyContractStatus(preunit.getHiscontractstatus(),preunit.getHiscontractstatus().intValue(),preunit.getContractid());
			}
			if(disputeid != null) {
				//TblCyhwUnit unit = dispute.getContract();
//				this.tblCyhwUnitService.modifyContractStatus(new BigDecimal(unit.getContractstatus()),16,unit.getContractid());
//				TblCyhwUnit oldunit = oldCloseInfo.getDispute().getContract();
				//this.tblCyhwUnitService.modifyContractStatus(oldunit.getHiscontractstatus(),new Integer(String.valueOf(oldunit.getHiscontractstatus())),oldunit.getContractid());
				
				TblLegalDisputregistration dispute = this.tblLegalDisputregistrationService.findById(disputeid);
				if(dispute.getContractinfo() != null) {
					TblCyhwUnit unit =  this.tblCyhwUnitService.getEntity(dispute.getContractinfo());
					this.tblCyhwUnitService.modifyContractStatus(new BigDecimal(unit.getContractstatus()),16,unit.getContractid());
				}
				oldCloseInfo.setDisputinfo(new BigDecimal(dispute.getDisputeid()));
			}
			oldCloseInfo.setJudgementamount(closeInfo.getJudgementamount());
			oldCloseInfo.setCloseresult(closeInfo.getCloseresult());
			oldCloseInfo.setManagerecommond(closeInfo.getManagerecommond());
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//			if(closeInfo.getClosedate() != null) {
//				oldCloseInfo.setClosedate(sdf.parse(closeInfo.getClosedate().toString()));
//			}
			oldCloseInfo.setClosedate(closeInfo.getClosedate());

			this.tblLegalCloseinformationService.updateModifyDisputeSettlementModify(oldCloseInfo);
			return JsonBean.success("修改成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return closeInfo.getCloseid().toString();
	}


	@RequestMapping(value = "/legal/disputeSettlementRemove",method = {RequestMethod.POST})
	@ApiOperation("法务管理-纠纷结案-删除")
	@ResponseBody
	public String legal_disputeSettlementRemove(HttpServletRequest request,
											  @RequestParam(value="closeId",required=false)Integer closeId) {
		TblLegalCloseinformation oldCloseInfo = this.tblLegalCloseinformationService.findById(closeId);
		TblLegalDisputregistration predispute = this.tblLegalDisputregistrationService.findById(oldCloseInfo.getDisputinfo().intValue());
		if(predispute != null && predispute.getContractinfo() != null) {
			TblCyhwUnit preunit =  this.tblCyhwUnitService.getEntity(predispute.getContractinfo());
			this.tblCyhwUnitService.modifyContractStatus(preunit.getHiscontractstatus(),preunit.getHiscontractstatus().intValue(),preunit.getContractid());
		}
		this.tblLegalCloseinformationService.removeDisputeSettlementRemove(closeId);
		return JsonBean.success("删除成功");
	}


	@RequestMapping(value = "/legal/qualificationList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-资质保全-列表")
	public String legal_qualificationList(HttpServletRequest request,
										  TblLegalQualification qualification,
										  @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
										  @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
												@RequestParam(value="flowid",required=false)String flowid,
										   @RequestParam(value = "disputeid",required = false)Integer disputeid,
										   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblFlow flow = tblFlowService.findById(flowid);
		PageInfo<TblLegalQualification> pageInfo = new PageInfo<TblLegalQualification>();
		qualification.setLinkorg(orgid);
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		//pageInfo.setCondition(qualification);
		this.tblLegalQualificationService.findListByPageInfo(pageInfo,qualification,disputeid);
		request.getSession().setAttribute("flowid", flowid);
		resultMap.put("date", pageInfo);
		resultMap.put("flow", flow);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/qualificationDetail",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-资质保全-查看详情")
	public String legal_qualificationDetail(HttpServletRequest request,
												  @RequestParam(value="qualId",required=true)Integer qualId) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblLegalQualification qua = this.tblLegalQualificationService.findById(qualId);
			resultMap.put("qualification", qua);
		} catch (Exception e) {
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/qualificationSave",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("资质保全-新建")
	public String qualificationSave(HttpServletRequest request,
									 TblLegalQualification qualification,
									 @RequestParam(value="disputeId",required=false)Integer disputeId,
									  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token
	) throws Exception {
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();
		BigDecimal staffId = staff.getStaffid();
		if(disputeId != null) {
			TblLegalDisputregistration dispute = this.tblLegalDisputregistrationService.findDisputeId(disputeId);
			qualification.setDisputeinfo(new BigDecimal(dispute.getDisputeid()));
		}
		qualification.setLinkorg(orgid);
		qualification.setCreatestaff(staffId);
		this.tblLegalQualificationService.saveQualification(qualification);
//		TblLegalDisputregistration tration = new TblLegalDisputregistration();
//		tration.setDisputeitem(qualification.getDisputeitem());
//		tration.setWhethersued(qualification.getWhethersued());
//		this.tblLegalDisputregistrationService.saveDiputregistration(tration);
		return JsonBean.success("新增成功");
		//return qualification.getQualid();
	}


	@RequestMapping(value = "/legal/qualificationToModify",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("资质保全-新建、修改回显")
	public String legal_qualificationToModify(HttpServletRequest request,
													@RequestParam(value="qualId",required=true)Integer qualId,
													@RequestParam(value="flowid",required=true)String flowid) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblFlow flow = tblFlowService.findByFlowid(flowid);
			TblLegalQualification qualification = this.tblLegalQualificationService.findById(qualId);
			resultMap.put("flow", flow);
			resultMap.put("qualification", qualification);
		} catch (Exception e) {
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/qualificationModify",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("资质保全-修改")
	@ResponseBody
	public String legal_qualificationModify(HttpServletRequest request,
											 TblLegalQualification qualification,
											 @RequestParam(value="disputeid",required=false)Integer disputeid
	) {
		try {
			TblLegalQualification old = this.tblLegalQualificationService.findById(qualification.getQualid());
			if(disputeid != null) {
				TblLegalDisputregistration dispute = this.tblLegalDisputregistrationService.findById(disputeid);
				old.setDisputeinfo(new BigDecimal(dispute.getDisputeid()));
			}
			old.setApplypreservation(qualification.getApplypreservation());
			old.setPreservedamount(qualification.getPreservedamount());
			old.setPreservednature(qualification.getPreservednature());
			old.setIsperformed(qualification.getIsperformed());
			old.setExceteamount(qualification.getExceteamount());
			old.setIscancel(qualification.getIscancel());
			old.setStillfrozen(qualification.getStillfrozen());
			old.setPreservation(qualification.getPreservation());
			this.tblLegalQualificationService.updateModifyQualification(old);
			return JsonBean.success("修改成功");
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return qualification.getQualid().toString();
	}


	@RequestMapping(value = "/legal/qualificationRemove",method = {RequestMethod.POST})
	@ApiOperation("资质保全-删除")
	@ResponseBody
	public String legal_qualificationRemove(HttpServletRequest request,
											@RequestParam(value="qualId",required=false)Integer qualId) {
		this.tblLegalQualificationService.removeQualification(qualId);
		return JsonBean.success("删除成功");
	}


	@RequestMapping(value = "/legal/frozenInformationList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-账户冻结-列表")
	public String legal_frozenInformationList(HttpServletRequest request,
													TblLegalFrozenaccount frozenAccount,
											  @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
											  @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
													@RequestParam(value="flowid",required=false)String flowid,
											   @RequestParam(value = "disputeid",required = false)Integer disputeid,
											   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		if(staff == null) {
			resultMap.put("code", "0");
			resultMap.put("msg", "用户已失效！");
			return String.valueOf(resultMap);
		}
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();

		TblFlow flow = tblFlowService.findById(flowid);
		if (pageNumber == null) {
			pageNumber = 1;
		}
		PageInfo<TblLegalFrozenaccount> pageInfo = new PageInfo<TblLegalFrozenaccount>();
		frozenAccount.setLinkorg(orgid);
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		//pageInfo.setCondition(frozenAccount);
		this.tblLegalFrozenaccountService.findListByPageInfo(pageInfo,frozenAccount,disputeid);
		request.getSession().setAttribute("flowid", flowid);
		resultMap.put("date", pageInfo);
		resultMap.put("flow", flow);

		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/frozenAccountDetail",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-账户冻结-查看详情")
	public String legal_frozenAccountDetail(HttpServletRequest request,
												  @RequestParam(value="inforId",required=true)Integer inforId) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblLegalFrozenaccount frozen = this.tblLegalFrozenaccountService.findInforid(inforId);
		resultMap.put("frozen", frozen);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}



	@RequestMapping(value = "/legal/frozenAccountAdd",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-账户冻结-新增回显")
	public String legal_frozenAccountAdd(HttpServletRequest request,
											   @RequestParam(value="flowid",required=false)String flowid) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblFlow flow = tblFlowService.findBy(flowid);
			resultMap.put("flow", flow);
		} catch (Exception e) {
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/frozenAccountSave",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-账户冻结-新增")
	@ResponseBody
	public String legal_frozenAccountSave(HttpServletRequest request,
										   TblLegalFrozenaccount frozen,
										   TblLegalProceedingsrecord srecord,
										   @RequestParam(value="proceedId",required=false)Integer proceedId,
										   @RequestParam(value="startDateOpen",required=false)String startDateOpen,
										   @RequestParam(value="endDateOpen",required=false)String endDateOpen,
										    @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//			TblOrganization attribute = (TblOrganization) request.getSession().getAttribute("hbOrgEntity");
//			TblStaff user = (TblStaff) request.getSession().getAttribute("longUser");
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			BigDecimal orgid = staff.getCurrentOrg().getOrgid();
			BigDecimal staffid = staff.getStaffid();
//			if(proceedId != null) {
//				TblLegalProceedingsrecord dispute = this.tblLegalProceedingsrecordService.findById(proceedId);
//
//			}
			frozen.setProceedinfo(proceedId);
			if(startDateOpen != null && !"".equals(startDateOpen)) {
				frozen.setStartdate(sdf.parse(startDateOpen));
			}
			if(endDateOpen != null && !"".equals(endDateOpen)) {
				frozen.setEnddate(sdf.parse(endDateOpen));
			}
			TblLegalProceedingsrecord legal = new TblLegalProceedingsrecord();
			frozen.setLinkorg(orgid);
			frozen.setCreatestaff(staffid);
			this.tblLegalFrozenaccountService.saveFrozenAccount(frozen);
//			legal.setPorceedstage(frozen.getPorceedstage());
//			legal.setProceedno(frozen.getProceedno());
//			legal.setCourt(frozen.getCourt());
//			//legal.setDisputeitem(frozen.getDisputeitem());
//			this.tblLegalProceedingsrecordService.saveProceedingRecord(legal);
//			TblLegalDisputregistration stration = new TblLegalDisputregistration();
//			stration.setDisputeitem(frozen.getDisputeitem());
//			stration.setPlaintiff(frozen.getPlaintiff());
//			stration.setDefendant(frozen.getDefendant());
//			this.tblLegalDisputregistrationService.saveDiputregistration(stration);
			return JsonBean.success("新增成功");
		} catch (Exception e) {
			//return Integer.valueOf("新增失败");
		}
		return JsonBean.success("新增成功");
	}


	@RequestMapping(value = "/legal/findProceedingsRecordInfo",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-账户冻结-新增-诉讼阶段选择接口")
	public String legal_findProceedingsRecordInfo(HttpServletRequest request,
														TblLegalProceedingsrecord record,
														@RequestParam(value="idname",required=true)String idname,
														@RequestParam(value="textname",required=true)String textname,
														@RequestParam(value="othername",required=false)String othername,
														@RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
														@RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
												   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();
		try {

			PageInfo<TblLegalProceedingsrecord> pageInfo = new PageInfo<TblLegalProceedingsrecord>();
			record.setLinkorg(orgid);
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			//pageInfo.setCondition(record);
			this.tblLegalProceedingsrecordService.findListByPageInfo(pageInfo,record);
			resultMap.put("date", pageInfo);
			resultMap.put("textname", textname);
			resultMap.put("idname", idname);
			resultMap.put("othername", othername);
		} catch (Exception e) {
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/frozenAccountToModify",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-账户冻结-新增保存、修改保存回显")
	public String legal_frozenAccountToModify(HttpServletRequest request,
													@RequestParam(value="inforId",required=true)Integer inforId,
													@RequestParam(value="flowid",required=true)String flowid) {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblFlow flow = tblFlowService.findById(flowid);
			TblLegalFrozenaccount frozen = this.tblLegalFrozenaccountService.findById(inforId);
			resultMap.put("flow", flow);
			resultMap.put("frozen", frozen);
		} catch (Exception e) {
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/frozenAccountModify",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-账户冻结-修改")
	@ResponseBody
	public String legal_frozenAccountModify(HttpServletRequest request,
											 TblLegalFrozenaccount frozen,
											 @RequestParam(value="proceedid",required=false)Integer proceedid) {
		try {
		//	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			//TblLegalFrozenaccount oldFrozen = this.tblLegalFrozenaccountService.findById(frozen.getInforid());
			if(proceedid != null) {
				TblLegalProceedingsrecord dispute = this.tblLegalProceedingsrecordService.findById(proceedid);
				frozen.setProceedinfo(dispute.getProceedid());
			}
//			if(startDateOpen != null) {
//				oldFrozen.setStartdate(sdf.parse(startDateOpen));
//			}
//			if(endDateOpen != null) {
//				oldFrozen.setEnddate(sdf.parse(endDateOpen));
//			}
//			TblLegalProceedingsrecord legal = new TblLegalProceedingsrecord();
//			oldFrozen.setFrozenblank(frozen.getFrozenblank());
//			oldFrozen.setFrozenaccount(frozen.getFrozenaccount());
//			oldFrozen.setApplyamount(frozen.getApplyamount());
//			oldFrozen.setFrozenamount(frozen.getFrozenamount());
//			oldFrozen.setKouhuaamount(frozen.getKouhuaamount());
//			oldFrozen.setAccountnature(frozen.getAccountnature());
//			oldFrozen.setFrozenmemo(frozen.getFrozenmemo());
			this.tblLegalFrozenaccountService.updateModifyFrozenAccount(frozen);
//			legal.setProceedid(oldFrozen.getProceedinfo());
//			legal.setPorceedstage(frozen.getPorceedstage());
//			this.tblLegalProceedingsrecordService.modifyNegotiateRecord(legal);
			TblLegalProceedingsrecord legal = new TblLegalProceedingsrecord();
			legal.setPorceedstage(frozen.getPorceedstage());
			legal.setProceedno(frozen.getProceedno());
			legal.setCourt(frozen.getCourt());
			legal.setProceedid(proceedid);
			//legal.setDisputeitem(frozen.getDisputeitem());
			this.tblLegalProceedingsrecordService.modifyNegotiateRecord(legal,null);
			return JsonBean.success("修改成功");
//			TblLegalDisputregistration stration = new TblLegalDisputregistration();
//			stration.setDisputeitem(frozen.getDisputeitem());
//			stration.setPlaintiff(frozen.getPlaintiff());
//			stration.setDefendant(frozen.getDefendant());
//			this.tblLegalDisputregistrationService.updateDiputregistration(stration);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return frozen.getInforid().toString();
	}


	@RequestMapping(value = "/legal/frozenAccountRemove",method = {RequestMethod.POST})
	@ApiOperation("法务管理-账户冻结-删除")
	@ResponseBody
	public String legal_frozenAccountRemove(HttpServletRequest request,
										  @RequestParam(value="inforId",required=false)Integer inforId) {
		this.tblLegalFrozenaccountService.removeFrozenAccount(inforId);
		return JsonBean.success("修改成功");
	}


	@RequestMapping(value = "/legal/legalAccountDetail",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-法务台账-查看详情")
	public String legal_legalAccountDetail(HttpServletRequest request,
												 @RequestParam(value="disputeId",required=true)Integer disputeId,
										   @RequestParam(value="flowid",required=false)String flowid,
										   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {

		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		if(staff == null) {
			resultMap.put("code", "0");
			resultMap.put("msg", "用户已失效！");
			return String.valueOf(resultMap);
		}
		try {
			BigDecimal orgid = staff.getCurrentOrg().getOrgid();
			TblFlow flow = tblFlowService.findById(flowid);
			TblLegalDisputregistration dispute = this.tblLegalDisputregistrationService.findByOrgid(disputeId,orgid);
			resultMap.put("dispute", dispute);
			resultMap.put("flow", flow);
		} catch (Exception e) {
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}


	@RequestMapping(value = "/legal/legalAccountList", method = {RequestMethod.POST}, produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-法务台账-列表")
	public String legal_legalAccountList(HttpServletRequest request, TblLegalDisputregistration dispute,
			@ApiParam(name = "companyId", value = "公司id", required = false) @RequestParam(value = "companyId", required = false) String companyId,
			@RequestParam(value = "pageNumber", required = false, defaultValue = "1") Integer pageNumber,
			@RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
			@ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token,
			@RequestParam(value = "flowid", required = false) String flowid) throws Exception {
		boolean flags = false;
		String result = null;
		Map<String, Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		if (staff == null) {
			resultMap.put("code", "0");
			resultMap.put("msg", "用户已失效！");
			return String.valueOf(resultMap);
		}
		String staffid = staff.getStaffid().toString();
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();
		String linkOrgorgid = staff.getLinkOrg().getOrgid().toString();
		String orgname = staff.getLinkOrg().getOrgname();
		if (StringUtils.equals(orgname, belongGroupName)) {
			StaffResult userInfoExam = tblStaffService.getUserInfoExam(Integer.valueOf(staffid), Integer.valueOf(linkOrgorgid), legalPersonnel);
			if (userInfoExam != null) {
				flags = true;
			}
		}
		try {
			TblFlow flow = tblFlowService.findById(flowid);
			PageInfo<TblLegalDisputregistration> pageInfo = new PageInfo<TblLegalDisputregistration>();
			//dispute.setLinkorg(orgid);
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			//pageInfo.setCondition(dispute);
			this.tblLegalDisputregistrationService.findListByPageInfo(companyId,pageInfo,dispute,orgid);
			if (CollectionUtil.isNotEmpty(pageInfo.getTlist())){
				boolean finalFlags = flags;
				pageInfo.getTlist().forEach(x->x.setIsDeleteDisplay(finalFlags));
			}
			request.getSession().setAttribute("flowid", flowid);
			resultMap.put("date", pageInfo);
			resultMap.put("flow", flow);

		} catch (Exception e) {
			log.error("异常信息：",e);
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}
	
	
	@RequestMapping(value = "/legal/legalAlList", method = {RequestMethod.POST}, produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-典型案例库列表")
	public String legal_legalAlList(HttpServletRequest request, TblLegalDisputregistration dispute,
			@ApiParam(name = "companyId", value = "公司id", required = false) @RequestParam(value = "companyId", required = false) String companyId,
			@RequestParam(value = "pageNumber", required = false, defaultValue = "1") Integer pageNumber,
			@RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
			@ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token,
			@RequestParam(value = "flowid", required = false) String flowid) throws Exception {
		boolean flags = false;
		String result = null;
		Map<String, Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		if (staff == null) {
			resultMap.put("code", "0");
			resultMap.put("msg", "用户已失效！");
			return String.valueOf(resultMap);
		}
		String staffid = staff.getStaffid().toString();
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();
		String linkOrgorgid = staff.getLinkOrg().getOrgid().toString();
		String orgname = staff.getLinkOrg().getOrgname();
		if (StringUtils.equals(orgname, belongGroupName)) {
			StaffResult userInfoExam = tblStaffService.getUserInfoExam(Integer.valueOf(staffid), Integer.valueOf(linkOrgorgid), legalPersonnel);
			if (userInfoExam != null) {
				flags = true;
			}
		}
		try {
			TblFlow flow = tblFlowService.findById(flowid);
			PageInfo<TblLegalDisputregistration> pageInfo = new PageInfo<TblLegalDisputregistration>();
			//dispute.setLinkorg(orgid);
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			//pageInfo.setCondition(dispute);
			dispute.setIsclassiccase(new BigDecimal("1"));
			this.tblLegalDisputregistrationService.findListByPageInfo(companyId,pageInfo,dispute,orgid);
			if (CollectionUtil.isNotEmpty(pageInfo.getTlist())){
				boolean finalFlags = flags;
				pageInfo.getTlist().forEach(x->x.setIsDeleteDisplay(finalFlags));
			}
			request.getSession().setAttribute("flowid", flowid);
			resultMap.put("date", pageInfo);
			resultMap.put("flow", flow);

		} catch (Exception e) {
			log.error("异常信息：",e);
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}
	
	

	@RequestMapping(value = "/legal/dellegalAlList", method = {RequestMethod.DELETE}, produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-典型案例库-列表-删除")
	public String legal_dellegalAlList(
			@ApiParam(name = "disputeId", value = "公司id", required = true) @RequestParam(value = "disputeId") Integer disputeId,
			@ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {
		String result = null;
		Map<String, Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		if (staff == null) {
			resultMap.put("code", "0");
			resultMap.put("msg", "用户已失效！");
			return String.valueOf(resultMap);
		}
		try {
			this.tblLegalDisputregistrationService.delClassicCase(disputeId);
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return JsonBean.success("删除成功");
	}
	
	/**
	 * 资产保全
	 */
	@RequestMapping(value = "/legal/assetporotectList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("资产保全-列表")
	public String legal_assetporotectList(HttpServletRequest request,
											@RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
											@RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
											@ApiParam(name="litigationid",value="诉讼过程ID",required=false) @RequestParam(value="litigationid",required=false)Integer litigationid,
											@ApiParam(name="arbitraid",value="仲裁过程ID",required=false) @RequestParam(value = "arbitraid",required = false)Integer arbitraid,
											 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		BigDecimal pid = staff.getCurrentOrg().getOrgid();
		if (pageNumber == null) {
			pageNumber = 1;
		}
		PageInfo<TblLegalAssetporotect> pageInfo = new PageInfo<TblLegalAssetporotect>();
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		this.tblLegalAssetporotectService.findListByPage(pageInfo,litigationid,arbitraid);
		resultMap.put("date", pageInfo);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}
	
	@RequestMapping(value = "/legal/assetporotectSave",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("资产保全-新增,修改")
	public String legal_assetporotectSave(HttpServletRequest request,
											TblLegalAssetporotect tla,
												   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			String orgid = staff.getCurrentOrg().getOrgid().toString();
			BigDecimal staffid = staff.getStaffid();

			tla.setCreatestaffid(staffid.intValue());
			tla.setCreatetime(new Date());
			this.tblLegalAssetporotectService.addLegalAssetporotectService(tla);
			resultMap.put("code",1);
			resultMap.put("date", tla);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return tla.getLitigationid().toString();
	}
	
	@RequestMapping(value = "/legal/assetporotectDetail",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("资产保全-查詢明細")
	public String legal_assetporotectDetail(HttpServletRequest request,
					@RequestParam(value="id",required=false)Integer id,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);

			TblLegalAssetporotect tla = this.tblLegalAssetporotectService.getLegalAssetporotectById(id);
			resultMap.put("data",tla);
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return null;
	}
	
	@RequestMapping(value = "/legal/assetporotectDelete",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("资产保全-刪除")
	public String legal_assetporotectDelete(HttpServletRequest request,
					@RequestParam(value="id",required=false)Integer id,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);

			this.tblLegalAssetporotectService.deleteLegalAssetporotectById(id);
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return null;
	}
	
	
	//======================================================================================
	/**
	 * 执行管理
	 */
	@RequestMapping(value = "/legal/legalExecumgrList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("执行管理-列表")
	public String legal_legalExecumgrList(HttpServletRequest request,TblLegalExecumgr tla,
											@RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
											@RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
											@ApiParam(name="litigationid",value="诉讼过程ID",required=false) @RequestParam(value="litigationid",required=false)Integer litigationid,
											@ApiParam(name="arbitraid",value="仲裁过程ID",required=false) @RequestParam(value = "arbitraid",required = false)Integer arbitraid,
											 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		if (pageNumber == null) {
			pageNumber = 1;
		}
		tla.setOrgid(staff.getCurrentOrg().getOrgid());
		PageInfo<TblLegalExecumgr> pageInfo = new PageInfo<TblLegalExecumgr>();
		pageInfo.setCurrentPage(pageNumber);
		pageInfo.setPageSize(pageSize);
		this.tblLegalExecumgrService.findListByPage(pageInfo,litigationid,arbitraid,tla);
		resultMap.put("date", pageInfo);
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}
	
	
	
	/**
	 * 纠纷台账-执行管理列表数据
	 */
	@RequestMapping(value = "/legal/legalExecumgrbydisidList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("纠纷台账-执行管理列表数据")
	public JsonBean legalExecumgrbydisidList(HttpServletRequest request,
											@ApiParam(name="disputeid",value="纠纷ID",required=false) @RequestParam(value="disputeid",required=false)Integer disputeid,
											 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) throws Exception {
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		if(staff == null) {
			return ResponseFormat.retParam(0,20006,null);
		}
		this.tblLegalExecumgrService.findListBydisputeid(disputeid,resultMap);
		return ResponseFormat.retParam(1,200,resultMap);
	}
	
	@RequestMapping(value = "/legal/legalExecumgrSave",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("执行管理-新增,修改")
	public String legal_legalExecumgrSave(HttpServletRequest request,
											TblLegalExecumgr tla,
												   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			String orgid = staff.getCurrentOrg().getOrgid().toString();
			BigDecimal staffid = staff.getStaffid();
			tla.setOrgid(staff.getCurrentOrg().getOrgid());
			tla.setCreatestaffid(staffid.intValue());
			tla.setCreatetime(new Date());
			tla.setStatus(0);//初始状态为0，未审批
			this.tblLegalExecumgrService.addLegalExecumgr(tla);
			resultMap.put("code",1);
			resultMap.put("date", tla);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return tla.getLitigationid().toString();
	}
	
	@RequestMapping(value = "/legal/legalExecumgrDetail",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("执行管理-查詢明細")
	public String legal_legalExecumgrDetail(HttpServletRequest request,
					@RequestParam(value="id",required=false)Integer id,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);

			TblLegalExecumgr tla = this.tblLegalExecumgrService.getLegalExecumgrById(id);
			resultMap.put("data",tla);
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return null;
	}
	
	@RequestMapping(value = "/legal/legalExecumgrDelete",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("执行管理-刪除")
	public String legal_legalExecumgrDelete(HttpServletRequest request,
					@RequestParam(value="id",required=false)Integer id,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);

			this.tblLegalExecumgrService.deleteLegalExecumgrById(id);
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return null;
	}
	
	
	
	//======================================================================
	/**
	 * 结案总结
	 */
	@RequestMapping(value = "/legal/legalCloseSumSave",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("结案总结-新增,修改")
	public String legal_legalCloseSumSave(HttpServletRequest request,
											TblLegalCloseSum tla,
												   @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			String orgid = staff.getCurrentOrg().getOrgid().toString();
			BigDecimal staffid = staff.getStaffid();

			tla.setCreatestaffid(staffid.intValue());
			this.tblLegalCloseSumService.addLegalCloseSum(tla);
			resultMap.put("code",1);
			resultMap.put("date", tla);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return tla.getLitigationid().toString();
	}
	
	@RequestMapping(value = "/legal/legalCloseSumDetail",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("结案总结-查詢明細")
	public String legal_legalCloseSumDetail(HttpServletRequest request,
					@RequestParam(value="id",required=false)Integer id,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);

			TblLegalCloseSum tla = this.tblLegalCloseSumService.getLegalCloseSumById(id);
			resultMap.put("data",tla);
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return null;
	}
	
	@RequestMapping(value = "/legal/legalCloseSumDetailBySSZC",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("结案总结-通过诉讼过程、仲裁过程查询")
	public String legal_legalCloseSumDetail(HttpServletRequest request,
					@ApiParam(name="litigationid",value="诉讼过程ID",required=false) @RequestParam(value="litigationid",required=false)Integer litigationid,
					@ApiParam(name="arbitraid",value="仲裁过程ID",required=false) @RequestParam(value = "arbitraid",required = false)Integer arbitraid,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);

			TblLegalCloseSum tla = this.tblLegalCloseSumService.getLegalCloseSumBySSZCId(litigationid,arbitraid);
			resultMap.put("data",tla);
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return null;
	}
	
	@RequestMapping(value = "/legal/legalCloseSumDelete",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("结案总结-刪除")
	public String legal_legalCloseSumDelete(HttpServletRequest request,
					@RequestParam(value="id",required=false)Integer id,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);

			this.tblLegalCloseSumService.deleteLegalCloseSumById(id);
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return null;
	}
	
	
	//=====================================================================================
	/**
	 * 代理律师 TBL_LEGAL_ATTORNEY
	 */
	@RequestMapping(value = "/legal/legalAttorney",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("代理律师-列表（不分页）")
	public String legal_legalAttorneyList(HttpServletRequest request,
					@RequestParam(value="disputeid",required=false)Integer disputeid,
					@RequestParam(value="negotiationid",required=false)Integer negotiationid,
					@RequestParam(value="lawsuitid",required=false)Integer lawsuitid,
					@RequestParam(value="arbitrationid",required=false)Integer arbitrationid,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			if(disputeid !=null){
				List<TbllegalAttorney> list = this.tbllegalAttorneyService.getlegalAttorneyByDisputeid(disputeid);
				resultMap.put("data",list);
				resultMap.put("code",1);
				JSONObject jsonObjectMV = new JSONObject(resultMap);
				result = jsonObjectMV.toString();
				return result;
			}
			if(negotiationid !=null){
				List<TbllegalAttorney> list = this.tbllegalAttorneyService.getlegalAttorneyByNegotiationid(negotiationid);
				resultMap.put("data",list);
				resultMap.put("code",1);
				JSONObject jsonObjectMV = new JSONObject(resultMap);
				result = jsonObjectMV.toString();
				return result;
			}
			if(lawsuitid !=null){
				List<TbllegalAttorney> list = this.tbllegalAttorneyService.getlegalAttorneyByLawsuitid(lawsuitid);
				resultMap.put("data",list);
				resultMap.put("code",1);
				JSONObject jsonObjectMV = new JSONObject(resultMap);
				result = jsonObjectMV.toString();
				return result;
			}
			if(arbitrationid !=null){
				List<TbllegalAttorney> list = this.tbllegalAttorneyService.getlegalAttorneyByArbitrationid(arbitrationid);
				resultMap.put("data",list);
				resultMap.put("code",1);
				JSONObject jsonObjectMV = new JSONObject(resultMap);
				result = jsonObjectMV.toString();
				return result;
			}
		} catch (Exception e) {
			log.error("异常信息：",e);
		}
		return null;
	}

	/**
	 * 代理律师
	 */
	@RequestMapping(value = "/legal/legalAttorneySave", method = {RequestMethod.POST}, produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("代理律师-新增,修改")
	public String legal_legalAttorneySave(HttpServletRequest request, TbllegalAttorney tla,
			@ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) {
		String result = null;
		Map<String, Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);
			String orgid = staff.getCurrentOrg().getOrgid().toString();
			BigDecimal staffid = staff.getStaffid();

			tla.setCreatestaffid(staffid.intValue());
			tla.setCreatetime(new Date());
			this.tbllegalAttorneyService.addlegalAttorney(tla);
			resultMap.put("code", 1);
			resultMap.put("date", tla);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常处理：", e);
		}
		return null;
	}
	
	@RequestMapping(value = "/legal/legalAttorneyDetail",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("代理律师-查詢明細")
	public String legal_legalAttorneyDetail(HttpServletRequest request,
					@RequestParam(value="id",required=false)Integer id,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);

			TbllegalAttorney tla = this.tbllegalAttorneyService.getlegalAttorneyById(id);
			resultMap.put("data",tla);
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return null;
	}
	
	@RequestMapping(value = "/legal/legalAttorneyDelete",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("代理律师-刪除")
	public String legal_legalAttorneyDelete(HttpServletRequest request,
					@RequestParam(value="id",required=false)Integer id,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);

			this.tbllegalAttorneyService.deletelegalAttorneyById(id);
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return null;
	}
	
	
	@RequestMapping(value ="/contract/contractAnalysis",method = {RequestMethod.GET})
	@ApiOperation(value="交付分析--年度合同分析")
	public @ResponseBody String ContractAnalysis(HttpServletRequest request,@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,@RequestParam(value="year",required=false)Integer year
			) throws Exception{
		String result = null;
  		try {
  			Map<String,Object>  resultMap = this.tblCyhwUnitService.getContractAnalysis(token,year);
			resultMap.put("year", year);
  			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
  		} catch (Exception e) {
  			ResponseFormat.retParam(0,1000,e.getMessage());
  		}
  		return result;
	}
	
	@RequestMapping(value ="/contract/ContractCollection",method = {RequestMethod.GET})
	@ApiOperation(value="交付分析--各部门合同收款汇总表")
	public @ResponseBody String ContractCollection(HttpServletRequest request,@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,@RequestParam(value="year",required=false)Integer year
			) throws Exception{
		String result = null;
  		try {
  			Map<String,Object>  resultMap = this.tblCyhwUnitService.getContractCollection(token,year);
			resultMap.put("year", year);
  			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
  		} catch (Exception e) {
  			ResponseFormat.retParam(0,1000,e.getMessage());
  		}
  		return result;
	}
	

	@RequestMapping(value ="/contract/orgContractPlan",method = {RequestMethod.GET})
	@ApiOperation(value="交付分析--各部门交付合同计划情况")
	public @ResponseBody String orgContractPlan(HttpServletRequest request,@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
			@RequestParam(value="year",required=false)Integer year,@RequestParam(value="quarter",required=false)Integer quarter
    ) throws Exception{
		String result = null;
  		try {
  			Map<String,Object>  resultMap = this.tblCyhwUnitService.orgContractPlan(token,year,quarter);
			resultMap.put("year", year);
  			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
  		} catch (Exception e) {
  			ResponseFormat.retParam(0,1000,e.getMessage());
  		}
  		return result;
	}
	
	
	@RequestMapping(value ="/contract/orgPayContract",method = {RequestMethod.GET})
	@ApiOperation(value="交付分析--各部门合同付款明细表")
	public @ResponseBody String orgPayContract(HttpServletRequest request,@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
			@RequestParam(value="year",required=false)Integer year,@RequestParam(value="quarter",required=false)Integer quarter
    ) throws Exception{
		String result = null;
  		try {
  			Map<String,Object>  resultMap = this.tblCyhwUnitService.orgPayContract(token,year,quarter);
			resultMap.put("year", year);
  			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
  		} catch (Exception e) {
  			ResponseFormat.retParam(0,1000,e.getMessage());
  		}
  		return result;
	}
	
	
	@RequestMapping(value ="/contract/contractLegalAprStat",method = {RequestMethod.GET})
	@ApiOperation(value="本年合同法律合规审查数量、金额")
	public @ResponseBody String contractLegalAprStat(HttpServletRequest request,@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
			@RequestParam(value="year",required=false)Integer year,
			@RequestParam(value="quarter",required=false)Integer quarter
    ) throws Exception{
		String result = null;
  		try {
  			Map<String,Object>  resultMap = this.tblCyhwUnitService.contractLegalAprStat(token,year,quarter);
			resultMap.put("year", year);
  			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
  		} catch (Exception e) {
  			ResponseFormat.retParam(0,1000,e.getMessage());
  		}
  		return result;
	}
	
	
	@RequestMapping(value ="/contract/legalLitigationCaseStat",method = {RequestMethod.GET})
	@ApiOperation(value="在手法律诉讼案件数量，涉诉金额")
	public @ResponseBody String legalLitigationCaseStat(HttpServletRequest request,@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
			@RequestParam(value="year",required=false)Integer year,
			@RequestParam(value="quarter",required=false)Integer quarter
    ) throws Exception{
		String result = null;
  		try {
  			Map<String,Object>  resultMap = this.tblCyhwUnitService.legalLitigationCaseStat(token,year,quarter);
			resultMap.put("year", year);
  			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
  		} catch (Exception e) {
  			ResponseFormat.retParam(0,1000,e.getMessage());
  		}
  		return result;
	}
	
	
	@RequestMapping(value = "/legal/addClassicCase",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("添加典型案例库")
	public String legal_addClassicCase(HttpServletRequest request,
					@RequestParam(value="disputeid",required=false)Integer disputeid,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);

			this.tblLegalDisputregistrationService.addClassicCase(disputeid);
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return null;
	}
	
	@RequestMapping(value = "/legal/classicCaseList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ApiOperation("法务管理-典型案例库-列表")
	public String legal_classicCaseList(HttpServletRequest request,TblLegalDisputregistration dispute,
										 @ApiParam(name="companyId",value="公司id",required=false)
										 @RequestParam(value = "companyId")String companyId,
										 @RequestParam(value = "pageNumber",required = false,defaultValue = "1")Integer pageNumber,
										 @RequestParam(value = "pageSize",required = false,defaultValue = "10")Integer pageSize,
										  @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token,
											   @RequestParam(value="flowid",required=false)String flowid) throws Exception {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		TblStaffUtil staff = DealUserToken.parseUserToken(token);
		if(staff == null) {
			resultMap.put("code", "0");
			resultMap.put("msg", "用户已失效！");
			return String.valueOf(resultMap);
		}
		BigDecimal orgid = staff.getCurrentOrg().getOrgid();
		try {
			TblFlow flow = tblFlowService.findById(flowid);
			PageInfo<TblLegalDisputregistration> pageInfo = new PageInfo<TblLegalDisputregistration>();
			//dispute.setLinkorg(orgid);
			pageInfo.setCurrentPage(pageNumber);
			pageInfo.setPageSize(pageSize);
			//pageInfo.setCondition(dispute);
			this.tblLegalDisputregistrationService.findClassicCaseListByPageInfo(companyId,pageInfo,dispute,orgid);
			request.getSession().setAttribute("flowid", flowid);
			resultMap.put("date", pageInfo);
			resultMap.put("flow", flow);

		} catch (Exception e) {
		}
		JSONObject jsonObjectMV = new JSONObject(resultMap);
		result = jsonObjectMV.toString();
		return result;
	}
	
	
	@RequestMapping(value = "/legal/caseChronicle",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("案件大事记")
	public String legal_caseChronicle(HttpServletRequest request,
					@RequestParam(value="disputeid",required=false)Integer disputeid,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		Map<String,Object> resultMap = new HashMap<String, Object>(0);
		try {
			TblStaffUtil staff = DealUserToken.parseUserToken(token);

			this.tblLegalDisputregistrationService.addClassicCase(disputeid);
			resultMap.put("code",1);
			JSONObject jsonObjectMV = new JSONObject(resultMap);
			result = jsonObjectMV.toString();
			return result;
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return null;
	}
	
	
	
	@RequestMapping(value = "/legal/disputeStatistics",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("纠纷统计分析")
	public String disputeStatistics(HttpServletRequest request,
			         @ApiParam(name="year",value="年度",required=false)@RequestParam(value="year",required=false)Integer year,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		try {
			log.info("纠纷统计分析");
  			Map<String,Object>  resultMap = this.tblLegalDisputregistrationService.setDisputeStatistics(year,token);
  			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}
	
	
	 
	@RequestMapping(value = "/legal/proceedTimeAxis",method = {RequestMethod.POST},produces = {"application/json;charset=UTF-8"})
	@ApiOperation("法务管理-诉讼过程-查看诉讼记录时间轴")
	public String proceedTimeAxis(HttpServletRequest request,
			@RequestParam(value="litigationId",required=true)@ApiParam(name="litigationId",value="litigationId")String litigationId) {
		String result = null;
		try {
  			Map<String,Object>  resultMap = this.tblLegalProceedingsrecordService.getproceedTimeAxis(litigationId);
  			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		 return result;
	}
	
	

	@RequestMapping(value = "/legal/disputeMoneyList",method = {RequestMethod.POST},produces = "application/json; charset=utf-8")
	@ResponseBody
	@ApiOperation("法律纠纷案件数量、涉及金额分析")
	public String disputeMoneyList(HttpServletRequest request,
			         @ApiParam(name="year",value="年度",required=false)@RequestParam(value="year",required=false)Integer year,
					@ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token") String token) {
		String result = null;
		try {
			log.info("法律纠纷案件数量、涉及金额分析");
  			Map<String,Object>  resultMap = this.tblLegalDisputregistrationService.setDisputeMoneyList(year,token);
  			JSONObject jsonObj = new JSONObject(resultMap);
			result = jsonObj.toString();
		} catch (Exception e) {
			log.error("异常信息：", e);
		}
		return result;
	}
	
}
