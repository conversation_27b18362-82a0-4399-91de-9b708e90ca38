package com.huabo.finance.entity.caiji;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 会计科目体系
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-28
 */
@Data
  @EqualsAndHashCode(callSuper = false)
    @TableName("BD_ACCSYSTEM")
@ApiModel(value="BdAccsystem对象", description="会计科目体系")
public class BdAccsystem implements Serializable {

    private static final long serialVersionUID = 1L;

      @ApiModelProperty(value = "主键")
      @TableField("PK_ACCSYSTEM")
    private String pkAccsystem;

      @ApiModelProperty(value = "科目体系编码")
      @TableField("CODE")
    private String code;

      @ApiModelProperty(value = "科目体系名称")
      @TableField("NAME")
    private String name;

      @ApiModelProperty(value = "科目编码规则")
      @TableField("ACCCODERULE")
    private String acccoderule;

      @ApiModelProperty(value = "备注")
      @TableField("MEMO")
    private String memo;

      @ApiModelProperty(value = "科目类型")
      @TableField("ACCTYPES")
    private String acctypes;

      @ApiModelProperty(value = "创建组织")
      @TableField("PK_ORG")
    private String pkOrg;

      @ApiModelProperty(value = "创建集团")
      @TableField("PK_GROUP")
    private String pkGroup;

      @ApiModelProperty(value = "数据来源 	0=本级产生;1=上级下发;2=下级上报;3=本级产生已上报下发;-1=系统预置;")
      @TableField("DATAORIGINFLAG")
    private String dataoriginflag;

      @ApiModelProperty(value = "创建人")
      @TableField("CREATOR")
    private String creator;

      @ApiModelProperty(value = "最后修改人")
      @TableField("MODIFIER")
    private String modifier;

      @ApiModelProperty(value = "创建时间")
      @TableField("CREATIONTIME")
    private Date creationtime;

      @ApiModelProperty(value = "最后修改时间")
      @TableField("MODIFIEDTIME")
    private Date modifiedtime;


}
