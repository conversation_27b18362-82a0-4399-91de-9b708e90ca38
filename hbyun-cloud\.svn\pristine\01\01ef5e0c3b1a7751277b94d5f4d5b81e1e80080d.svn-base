package com.huabo.audit.oracle.mapper;

import com.hbfk.util.PageInfo;
import com.huabo.audit.oracle.entity.LeaveAudit2LEntity;
import com.hbfk.util.StringUtil;

/**
 * <AUTHOR>
 * @ClassName LeaveAudit2LMapperSqlConfig
 * @Description
 * @DATE 2023/9/14
 */
public class LeaveAudit2LMapperSqlConfig {

    public String selectByPageInfo(PageInfo pageInfo , LeaveAudit2LEntity leaveAudit2LEntity){
        StringBuffer sb = new StringBuffer();
        sb.append("SELECT * FROM (SELECT T1.* , ROWNUM RN FROM (");
        sb.append("SELECT * FROM TBL_YQNS_LEAVE_AUDIT_2L RS LEFT JOIN TBL_STAFF TS ON RS.CREATE_USER = TS.STAFFID WHERE 1=1 ");

        if(leaveAudit2LEntity.getAuditOrg() != null && com.hbfk.util.StringUtil.isNotEmpty(leaveAudit2LEntity.getAuditOrg().getOrgname())){
            sb.append("AND RS.AUDIT_ORG IN ( SELECT ORGID FROM TBL_ORGANIZATION WHERE ORGNAME LIKE '%"+leaveAudit2LEntity.getAuditOrg().getOrgname()+"%') ");
        }


        if(StringUtil.isNotEmpty(leaveAudit2LEntity.getProjectName())){
            sb.append("AND RS.PROJECT_NAME LIKE '%"+leaveAudit2LEntity.getProjectName()+"%'");
        }
        sb.append(") T1 WHERE ROWNUM <= "+(pageInfo.getCurrentRecord() + pageInfo.getPageSize() )+" ) T2 WHERE T2.RN > "+ pageInfo.getCurrentRecord());
        return sb.toString();
    }

    public String selectCountByEntity(LeaveAudit2LEntity leaveAudit2LEntity){
        StringBuffer sb = new StringBuffer();
        sb.append("SELECT COUNT(0) FROM (");
        sb.append("SELECT * FROM TBL_YQNS_LEAVE_AUDIT_2L RS LEFT JOIN TBL_STAFF TS ON RS.CREATE_USER = TS.STAFFID WHERE 1=1 ");

        if(leaveAudit2LEntity.getAuditOrg() != null && com.hbfk.util.StringUtil.isNotEmpty(leaveAudit2LEntity.getAuditOrg().getOrgname())){
            sb.append("AND RS.AUDIT_ORG IN ( SELECT ORGID FROM TBL_ORGANIZATION WHERE ORGNAME LIKE '%"+leaveAudit2LEntity.getAuditOrg().getOrgname()+"%') ");
        }

        if(StringUtil.isNotEmpty(leaveAudit2LEntity.getProjectName())){
            sb.append("AND RS.PROJECT_NAME LIKE '%"+leaveAudit2LEntity.getProjectName()+"%'");
        }

        sb.append(")");
        return sb.toString();
    }

    public String updateEntity(LeaveAudit2LEntity leaveAudit2LEntity){
        StringBuffer sb = new StringBuffer();
        sb.append("UPDATE TBL_YQNS_LEAVE_AUDIT_2L SET ");
        sb.append("PROJECT_NAME = '"+leaveAudit2LEntity.getProjectName()+"'");

        if(StringUtil.isNotEmpty(  leaveAudit2LEntity.getAuditOrgId() )){
            sb.append(", AUDIT_ORG = '"+leaveAudit2LEntity.getAuditOrgId()+"'");
        }

        if(StringUtil.isNotEmpty(leaveAudit2LEntity.getEntrustNo())){
            sb.append(", ENTRUST_NO = '"+leaveAudit2LEntity.getEntrustNo()+"'");
        }


        if(leaveAudit2LEntity.getEntrustTime() != null){
            sb.append(", ENTRUST_TIME = TO_DATE('"+leaveAudit2LEntity.getEntrustTime()+"','yyyy-mm-dd hh24:mi:ss')");
        }

        if(leaveAudit2LEntity.getAuditStartTime() != null){
            sb.append(", AUDIT_START_TIME = TO_DATE('"+leaveAudit2LEntity.getAuditStartTime()+"','yyyy-mm-dd hh24:mi:ss')");
        }

        if(leaveAudit2LEntity.getAuditEndTime() != null){
            sb.append(", AUDIT_END_TIME = TO_DATE('"+leaveAudit2LEntity.getAuditEndTime()+"','yyyy-mm-dd hh24:mi:ss')");
        }


        sb.append(" WHERE ID = '"+leaveAudit2LEntity.getId()+"'");

        return sb.toString();
    }

    public String insertEntity(LeaveAudit2LEntity leaveAudit2LEntity){
        StringBuffer colSb = new StringBuffer();
        colSb.append("INSERT INTO TBL_YQNS_LEAVE_AUDIT_2L (ID");

        StringBuffer valSb = new StringBuffer();
        valSb.append(" VALUES (HIBERNATE_SEQUENCE.nextval");

        if(StringUtil.isNotEmpty(leaveAudit2LEntity.getProjectName())){
            colSb.append(", PROJECT_NAME");
            valSb.append(", '"+ leaveAudit2LEntity.getProjectName()+"'");
        }

        if(StringUtil.isNotEmpty(  leaveAudit2LEntity.getAuditOrgId() )){
            colSb.append(", AUDIT_ORG");
            valSb.append(", '"+ leaveAudit2LEntity.getAuditOrgId()+"'");
        }

        if(StringUtil.isNotEmpty(leaveAudit2LEntity.getEntrustNo())){
            colSb.append(", ENTRUST_NO");
            valSb.append(", '"+ leaveAudit2LEntity.getEntrustNo()+"'");
        }


        if(leaveAudit2LEntity.getEntrustTime() != null){
            colSb.append(", ENTRUST_TIME");
            valSb.append(", TO_DATE('" + leaveAudit2LEntity.getEntrustTime() + "','yyyy-mm-dd hh24:mi:ss')" );
        }

        if(leaveAudit2LEntity.getAuditStartTime() != null){
            colSb.append(", AUDIT_START_TIME");
            valSb.append(", TO_DATE('" + leaveAudit2LEntity.getAuditStartTime() + "','yyyy-mm-dd hh24:mi:ss')" );
        }

        if(leaveAudit2LEntity.getAuditEndTime() != null){
            colSb.append(", AUDIT_END_TIME");
            valSb.append(", TO_DATE('" + leaveAudit2LEntity.getAuditEndTime() + "','yyyy-mm-dd hh24:mi:ss')" );
        }


        colSb.append(", CREATE_USER");
        valSb.append(", '" + leaveAudit2LEntity.getCreateUser().getStaffid() + "'");

        colSb.append(", CREATE_TIME)");
        valSb.append(", (SELECT SYSDATE FROM DUAL))");

        colSb.append(valSb);
        return colSb.toString();
    }

    public String deleteByIds(String ids){
        StringBuffer sb = new StringBuffer();
        sb.append("DELETE FROM TBL_YQNS_LEAVE_AUDIT_2L WHERE ID IN (" + ids+")");
        return sb.toString();
    }
}
