package com.huabo.monitor.vo.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ControlTestImplSaveParam {

	@ApiModelProperty("testtaskid")
	private String testtaskid;

	@ApiModelProperty("备注")
	private String memo;

	@ApiModelProperty("测试程序")
	private String procedures;

	@ApiModelProperty("测试结果")
	private String testresult;

	@ApiModelProperty("测试有效性value=1or2or3")
	private String testpointvalidity;

	@ApiModelProperty("planid")
	private BigDecimal planid;

	@ApiModelProperty("oneprocess")
	private String oneprocess;

	@ApiModelProperty("twoprocess")
	private String twoprocess;

	@ApiModelProperty("threeprocess")
	private String threeprocess;

	@ApiModelProperty("risktype")
	private String risktype;

	@ApiModelProperty("evidence")
	private String evidence;

	@ApiModelProperty("dutyorg")
	private String dutyorg;

	@ApiModelProperty("dutystation")
	private String dutystation;

	@ApiModelProperty("insystemname")
	private String insystemname;

	@ApiModelProperty("evaluationpoint")
	private String evaluationpoint;

	@ApiModelProperty("evaluationpro")
	private String evaluationpro;

	@ApiModelProperty("evaluationnode")
	private String evaluationnode;

	@ApiModelProperty("quabasis")
	private String quabasis;

	@ApiModelProperty("defecttype")
	private String defecttype;

	@ApiModelProperty("defectlevel")
	private String defectlevel;

	@ApiModelProperty("defectmemo")
	private String defectmemo;

	@ApiModelProperty("defectdetail")
	private String defectdetail;
}
