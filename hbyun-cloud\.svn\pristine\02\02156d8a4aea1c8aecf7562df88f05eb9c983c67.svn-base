package com.huabo.compliance.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huabo.compliance.util.IgnoreSwaggerParameter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Set;

/**
 * 描述: 底稿
 * author: ziyao
 * date: 2022-04-20
 */
@TableName("TBL_NBSJ_TEMPLETE")
@Data
@ApiModel("审计模板实体类")
@Accessors(chain = true)
public class TblNbsjTempleteEntity {
	public static final Integer NORMAL = 1;  //启用
	public static final Integer UNNORMAL=-1; //禁用
	
	public static final Integer MB_TYPE = 0;   //审计模板
	public static final Integer ZY_TYPE = 1;  // 指引模板
	public static final Integer COPY_TYPE=2; // 审计经验库

    @TableId(value = "templeteid", type= IdType.AUTO)
    @ApiModelProperty(value="模板id")
    private Integer templeteId;

    @TableField(value = "templetecode")
    @ApiModelProperty(value="模板编号")
    private String templeteCode;

    @TableField(value = "templetename")
    @ApiModelProperty(value="模板名称")
    private String templeteName;

    @TableField(value = "templetetype")
    @ApiModelProperty(value="审计类型",required = true)
    private String templeteType;

    @TableField(value = "templetedesc")
    @ApiModelProperty(value="模板说明")
    private String templeteDesc;

    @TableField(value = "staffid")
    @ApiModelProperty(value="创建人id",hidden=true)
    private Integer staffId;

    @TableField(value = "createdate")
    @ApiModelProperty(value="创建日期",hidden=true)
    private Date createDate;

    @TableField(value = "updatedate")
    @ApiModelProperty(value="修改日期",hidden=true)
    private Date updateDate;

    @TableField(value = "updatestaffid")
    @ApiModelProperty(value="修改人id",hidden=true)
    private Integer updateStaffId;

    @TableField(value = "status")
    @ApiModelProperty(value="状态：启用：1、禁用“-1",hidden=true)
    private Integer status;

    @TableField(value = "temptype")
    @ApiModelProperty(value="模板类型：审计模板：0、指引模板：1、审计经验库：2",required = true)
    private String tempType;

    @TableField(value = "orgid")
    @ApiModelProperty(value="模板所属公司id",hidden=true)
    private Integer orgId;
    
    @ApiModelProperty(value = "涉及单位", hidden = true)
    @IgnoreSwaggerParameter
    private String temorgname;
    
    @ApiModelProperty(value = "涉及单位的id", hidden = true)
    @IgnoreSwaggerParameter
    private String temorgids;
    
    @IgnoreSwaggerParameter
    @ApiModelProperty(value = "创建人", hidden = true)
    private String createstaffname;
    
    @ApiModelProperty(value="")
    @IgnoreSwaggerParameter
    private Set<TblOrganization> organizations;
    
}
