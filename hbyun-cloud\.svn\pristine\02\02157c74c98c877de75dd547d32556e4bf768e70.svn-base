package com.huabo.audit.oracle.mapper;

import com.hbfk.util.PageInfo;
import com.hbfk.util.StringUtil;
import com.huabo.audit.oracle.entity.QualityEvaluateEntity;

import java.math.BigDecimal;
import java.util.Date;
import cn.hutool.core.date.DateUtil;
/**
 * <AUTHOR>
 * @ClassName QualityEvaluateMapperSqlConfig
 * @Description
 * @DATE 2023/10/9
 */
public class QualityEvaluateMapperSqlConfig {

    public String selectByEntity( QualityEvaluateEntity qualityEvaluateEntity){
        StringBuffer sb = new StringBuffer();
        //	sb.append("SELECT * FROM (SELECT T1.* , ROWNUM RN FROM (");
        sb.append("SELECT * FROM TBL_YQNS_QUALITY_EVALUATE RS WHERE 1=1 ");

        if(StringUtil.isNotEmpty(qualityEvaluateEntity.getName())){
            sb.append("AND RS.TYPE LIKE '%"+qualityEvaluateEntity.getName()+"%'");
        }

        if(qualityEvaluateEntity.getStatus() != null){
            sb.append("AND RS.STATUS = '"+qualityEvaluateEntity.getStatus()+"'");
        }

//        sb.append(" ORDER BY RS.STATUS ASC ) T1 WHERE ROWNUM <= "+(pageInfo.getCurrentRecord() + pageInfo.getPageSize() )+" ) T2 WHERE T2.RN > "+ pageInfo.getCurrentRecord());
        sb.append(" ORDER BY RS.STATUS ASC");
        return sb.toString();
    }

    public String selectCountByEntity(QualityEvaluateEntity qualityEvaluateEntity){
        StringBuffer sb = new StringBuffer();
        sb.append("SELECT COUNT(0) FROM (");
        sb.append("SELECT * FROM TBL_YQNS_QUALITY_EVALUATE RS  WHERE 1=1 ");


        if(StringUtil.isNotEmpty(qualityEvaluateEntity.getName())){
            sb.append("AND RS.TYPE LIKE '%"+qualityEvaluateEntity.getName()+"%'");
        }

        if(qualityEvaluateEntity.getStatus() != null){
            sb.append("AND RS.STATUS = '"+qualityEvaluateEntity.getStatus()+"'");
        }

        sb.append(")");
        return sb.toString();
    }

    public String updateEntity(QualityEvaluateEntity qualityEvaluateEntity){
        StringBuffer sb = new StringBuffer();
        sb.append("UPDATE TBL_YQNS_QUALITY_EVALUATE SET ");
        sb.append("NAME = '"+qualityEvaluateEntity.getName()+"'");

        if(qualityEvaluateEntity.getStatus() != null){
            sb.append(", STATUS = '"+qualityEvaluateEntity.getStatus()+"'");
        }


        sb.append(" WHERE ID = '"+qualityEvaluateEntity.getId()+"'");

        return sb.toString();
    }

    public String updateEntityStatus(QualityEvaluateEntity qualityEvaluateEntity){
        StringBuffer sb = new StringBuffer();
        sb.append("UPDATE TBL_YQNS_QUALITY_EVALUATE SET ");
        sb.append("STATUS = '"+qualityEvaluateEntity.getStatus()+"'");


        sb.append(" WHERE ID = '"+qualityEvaluateEntity.getId()+"'");

        return sb.toString();
    }

    public String insertEntity(QualityEvaluateEntity qualityEvaluateEntity){
        StringBuffer colSb = new StringBuffer();
        colSb.append("INSERT INTO TBL_YQNS_QUALITY_EVALUATE (ID");

        StringBuffer valSb = new StringBuffer();
        valSb.append(" VALUES (HIBERNATE_SEQUENCE.nextval");

        if(StringUtil.isNotEmpty(qualityEvaluateEntity.getName())){
            colSb.append(", NAME");
            valSb.append(", '" + qualityEvaluateEntity.getName() + "'");
        }

        if(qualityEvaluateEntity.getStatus() != null){
            colSb.append(", STATUS");
            valSb.append(", '" + qualityEvaluateEntity.getStatus() + "'");
        }

        if(qualityEvaluateEntity.getCreateUser() != null){
            colSb.append(", CREATE_USER");
            valSb.append(", '" + qualityEvaluateEntity.getCreateUser().getStaffid() + "'");
        }

        colSb.append(", CREATE_TIME)");
        valSb.append(", '"+ DateUtil.format(new Date(),"yyyy-MM-dd")+"')");

        colSb.append(valSb);
        return colSb.toString();
    }

    public String deleteByIds(String ids){
        StringBuffer sb = new StringBuffer();
        sb.append("DELETE FROM TBL_YQNS_QUALITY_EVALUATE WHERE ID IN (" + ids+")");
        return sb.toString();
    }

    public String insertQualityEvaluateItem(BigDecimal qeId, BigDecimal smId ){
        StringBuffer sb = new StringBuffer();
        sb.append("INSERT INTO TBL_YQNS_QUALITY_EVALUATE_ITEM(QEID,SMID) VALUES ("+ qeId + ","+ smId +")");
        return sb.toString();
    }

    public String deleteQualityEvaluateItemsById(String ids){
        StringBuffer sb = new StringBuffer("DELETE FROM TBL_YQNS_QUALITY_EVALUATE_ITEM WHERE QEID IN ("+ids+")");
        return sb.toString();
    }
}
