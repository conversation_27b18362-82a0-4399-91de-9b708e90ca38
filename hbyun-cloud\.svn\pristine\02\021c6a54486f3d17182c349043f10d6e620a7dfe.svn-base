package com.huabo.system.oracle.mapper;

import com.hbfk.util.PageInfo;
import com.huabo.system.oracle.entity.*;
import com.huabo.system.service.UserService;
import com.huabo.system.vo.result.StaffResult;
import io.lettuce.core.dynamic.annotation.Param;
import net.sf.json.JSONObject;
import org.apache.ibatis.annotations.*;
import tk.mybatis.mapper.common.BaseMapper;

import java.math.BigDecimal;
import java.util.List;

public interface TblStaffMapper extends BaseMapper<TblStaff> {

    @Select("SELECT STAFFID,REALNAME,ORGID,USERNAME FROM TBL_STAFF WHERE STAFFID = #{staffId}")
    @Results({
            @Result(column = "STAFFID", property = "staffid"),
            @Result(column = "REALNAME", property = "realname"),
            @Result(column = "ORGID", property = "orgid"),
            @Result(column = "USERNAME", property = "username"),
    })
    TblStaff selectStaff(@Param("staffId") String staffId) throws Exception;

    @Select("SELECT TS.STAFFID,TS.REALNAME,TS.USERNAME,TS.ADDRESS,TS.PKYMSTAFFID,TS.MIBLEPHONE,TS.MEMO,TS.STATUS,TS.CREATETIME,TS.JOBID,TS.OUTSIDEID,TS.OUTSIDEOPENDID,TS.EMAIL,TS.FIXEDPHONE,TR.RID,TR.RNAME,ORG.ORGID,ORG.ORGNAME,TS.ROLEIDSTRS,(SELECT WM_CONCAT(RNAME) FROM TBL_ROLE WHERE INSTR(','||TS.ROLEIDSTRS||',',','||RID||',')>0) RNAMES,TS.HISTORYCODE FROM TBL_STAFF TS LEFT JOIN TBL_ROLE TR ON TS.ROLEID = TR.RID LEFT JOIN TBL_ORGANIZATION ORG ON TS.ORGID = ORG.ORGID WHERE TS.USERNAME = #{userName} AND TS.PASSWORD = #{password} AND TS.STATUS = 1")
    @Results({
            @Result(column = "STAFFID", property = "staffid"),
            @Result(column = "REALNAME", property = "realname"),
            @Result(column = "USERNAME", property = "username"),
            @Result(column = "ADDRESS", property = "address"),
            @Result(column = "EMAIL", property = "email"),
            @Result(column = "PKYMSTAFFID", property = "pkYmStaffId"),
            @Result(column = "MIBLEPHONE", property = "miblephone"),
            @Result(column = "MEMO", property = "memo"),
            @Result(column = "STATUS", property = "status"),
            @Result(column = "CREATETIME", property = "createDate"),
            @Result(column = "JOBID", property = "jobid"),
            @Result(column = "OUTSIDEID", property = "outSideId"),
            @Result(column = "OUTSIDEOPENDID", property = "outSideOpenId"),
            @Result(column = "FIXEDPHONE", property = "fixedphone"),
            @Result(column = "RID", property = "trole.rid", id = true),
            @Result(column = "RNAME", property = "trole.rname"),
            @Result(column = "ORGID", property = "linkDetp.orgid", id = true),
            @Result(column = "ORGNAME", property = "linkDetp.orgname"),
            @Result(column = "ROLEIDSTRS", property = "roleIdStrs"),
            @Result(column = "RNAMES", property = "roleNames"),
            @Result(column = "HISTORYCODE", property = "historycode"),
    })
    TblStaff selectUniqueStaffInfo(@Param("userName") String userName, @Param("password") String password) throws Exception;

    @Select("SELECT TS.STAFFID,TS.REALNAME,TS.USERNAME,TS.ADDRESS,TS.PKYMSTAFFID,TS.MIBLEPHONE,TS.MEMO,TS.STATUS,TS.CREATETIME,TS.JOBID,TS.OUTSIDEID,TS.OUTSIDEOPENDID,TS.EMAIL,TS.FIXEDPHONE,TR.RID,TR.RNAME,ORG.ORGID,ORG.ORGNAME,TS.ROLEIDSTRS,(SELECT WM_CONCAT(RNAME) FROM TBL_ROLE WHERE INSTR(','||TS.ROLEIDSTRS||',',','||RID||',')>0) RNAMES,TS.HISTORYCODE FROM TBL_STAFF TS LEFT JOIN TBL_ROLE TR ON TS.ROLEID = TR.RID LEFT JOIN TBL_ORGANIZATION ORG ON TS.ORGID = ORG.ORGID WHERE TS.USERNAME = #{userName}")
    @Results({
            @Result(column = "STAFFID", property = "staffid"),
            @Result(column = "REALNAME", property = "realname"),
            @Result(column = "USERNAME", property = "username"),
            @Result(column = "ADDRESS", property = "address"),
            @Result(column = "EMAIL", property = "email"),
            @Result(column = "PKYMSTAFFID", property = "pkYmStaffId"),
            @Result(column = "MIBLEPHONE", property = "miblephone"),
            @Result(column = "MEMO", property = "memo"),
            @Result(column = "STATUS", property = "status"),
            @Result(column = "CREATETIME", property = "createDate"),
            @Result(column = "JOBID", property = "jobid"),
            @Result(column = "OUTSIDEID", property = "outSideId"),
            @Result(column = "OUTSIDEOPENDID", property = "outSideOpenId"),
            @Result(column = "FIXEDPHONE", property = "fixedphone"),
            @Result(column = "RID", property = "trole.rid", id = true),
            @Result(column = "RNAME", property = "trole.rname"),
            @Result(column = "ORGID", property = "linkDetp.orgid", id = true),
            @Result(column = "ORGNAME", property = "linkDetp.orgname"),
            @Result(column = "ROLEIDSTRS", property = "roleIdStrs"),
            @Result(column = "RNAMES", property = "roleNames"),
            @Result(column = "HISTORYCODE", property = "historycode"),
    })
    TblStaff selectUniqueStaffInfoByname(@Param("userName") String userName) throws Exception;

    @Select("select count(*) from TBL_STAFF where ORGID = #{ORGID} and STATUS = 1")
    boolean selectFindChildrenByOrgid(List<TblOrganization> findChildrenByOrgid);


    @Select("select S.*,O.ORGNAME from TBL_STAFF S LEFT JOIN TBL_ORGANIZATION O ON S.ORGID = O.ORGID WHERE S.STAFFID = #{id}")
    TblStaff findById(BigDecimal id);

    @Update("UPDATE TBL_STAFF SET PASSWORD = #{password} WHERE USERNAME =#{username}")
    void attachDirty(TblStaff username);

    @Select("SELECT s.STAFFID,s.REALNAME,o.ORGNAME FROM TBL_STAFF s LEFT JOIN TBL_ORGANIZATION o ON s.ORGID = o.ORGID WHERE s.STAFFID IN (select STAFFID from TBL_MANAGE_USER_BOOK WHERE BOOKID IN (%s)) AND s.ORGID IN (SELECT ORGID FROM TBL_ORGANIZATION where ORGTYPE = 0 start with ORGID = %s connect by prior ORGID = FATHERORGID)")
    List<TblStaff> selectDan();

    @Select("SELECT count(*) FROM TBL_STAFF s LEFT JOIN TBL_ORGANIZATION o ON s.ORGID = o.ORGID WHERE s.STAFFID IN (select STAFFID from TBL_MANAGE_USER_BOOK WHERE BOOKID IN (%s)) AND s.ORGID IN (SELECT ORGID FROM TBL_ORGANIZATION where ORGTYPE = 0 start with ORGID = %s connect by prior ORGID = FATHERORGID)")
    List<TblStaff> selectCountt();

    @Select("SELECT * FROM TBL_STAFF WHERE STAFFID =#{pid}")
    TblStaff selectUserId(BigDecimal pid);

    @SelectProvider(method = "selectListByPageInfoo", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> selectListByPageInfoo(PageInfo<TblStaff> pageInfo, Find find, BigDecimal orgid);

    @SelectProvider(method = "selectListByPageInfo", type = TblStaffMapperSqlConifg.class)
    List<UserService> selectListByPageInfo(PageInfo<UserService> pageInfo);

    @SelectProvider(method = "selectListByPageIn", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> selectListByPageIn(PageInfo<TblStaff> pageInfo, BigDecimal pid, Find find);

    @SelectProvider(method = "selectListByPageInfoOrgid", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> selectListByPageInfoOrgid(PageInfo<TblStaff> pageInfo, String orgIdStrs, Find find);


    @Select("select * from TBL_STAFF WHERE USERNAME = #{username}")
    TblStaff selectUserName(String username);

    //	@Insert("INSERT INTO TBL_STAFF (REALNAME,STATUS,FIXEDPHONE,MIBLEPHONE,ADDRESS,EMAIL,MEMO,USERNAME,PASSWORD) " +
//			"VALUES(#{realname},#{status},#{fixedphone},#{miblephone},#{address},#{email},#{memo},#{username},#{password})")
    @InsertProvider(method = "insertUser", type = TblStaffMapperSqlConifg.class)
    @Options(useGeneratedKeys = true, keyProperty = "staffid", keyColumn = "STAFFID")
    void insertUser(TblStaff user);

    @Select("select * from TBL_STAFF WHERE EMAIL = #{email}")
    List<TblStaff> selectEmail(String email);

    @Update("UPDATE TBL_STAFF SET PASSWORD =#{password} WHERE STAFFID = #{staffid}")
        //@UpdateProvider(type=TblStaffMapperSqlConifg.class,method="updateUser")
    void updateUser(String password, BigDecimal staffid);

    @Select("SELECT TS.STAFFID,TS.REALNAME,TS.ADDRESS,TS.EMAIL,TS.MIBLEPHONE,TS.MEMO,TS.USERNAME,TS.PASSWORD,TS.STATUS,TS.CREATETIME,TS.JOBID,TS.ROLEIDSTRS,(SELECT WM_CONCAT(RNAME) FROM TBL_ROLE WHERE INSTR(TS.ROLEIDSTRS,RID)>0) RNAMES,TOR.ORGID,TOR.ORGNAME FROM TBL_STAFF TS LEFT JOIN TBL_ORGANIZATION TOR ON TS.ORGID = TOR.ORGID WHERE TS.STAFFID = #{id}")
    @Results({
            @Result(column = "STAFFID", property = "staffid"),
            @Result(column = "REALNAME", property = "realname"),
            @Result(column = "ADDRESS", property = "address"),
            @Result(column = "EMAIL", property = "email"),
            @Result(column = "MIBLEPHONE", property = "miblephone"),
            @Result(column = "MEMO", property = "memo"),
            @Result(column = "ORGID", property = "orgid"),
            @Result(column = "USERNAME", property = "username"),
            @Result(column = "PASSWORD", property = "password"),
            @Result(column = "STATUS", property = "status"),
            @Result(column = "CREATETIME", property = "createDate"),
            @Result(column = "JOBID", property = "jobid"),
            @Result(column = "ROLEIDSTRS", property = "roleIdStrs"),
            @Result(column = "RNAMES", property = "roleNames"),
            @Result(column = "ORGNAME", property = "orgname"),
    })
    TblStaff selectPid(String id);


    //	@SelectProvider(method = "selectCountFind",type =TblStaffMapperSqlConifg.class )
    @Select("select count(*) from tbl_staff where orgid= #{orgid} order by STATUS desc,STAFFID desc")
    Integer selectCountFind(PageInfo<TblStaff> pageInfo, BigDecimal orgid);


    //	@SelectProvider(method = "selectListByPageInfoCount",type =TblStaffMapperSqlConifg.class )
    @SelectProvider(method = "selectListByPageInfoCount", type = TblStaffMapperSqlConifg.class)
    Integer selectListByPageInfoCount(String orgIdStrs, Find find);

    @SelectProvider(method = "selectListByPageInfoCountOrgid",type = TblStaffMapperSqlConifg.class)
    Integer selectListByPageInfoCountOrgid(Find find);

    //	@SelectProvider(method = "selectListByPageInCount",type = TblStaffMapperSqlConifg.class)
    @Select("select COUNT(*) from tbl_staff where ORGID in (select ORGID from TBL_ORGANIZATION org where FATHERORGID= #{pid} AND ORGTYPE=0 )")
    Integer selectListByPageInCount(PageInfo<TblStaff> pageInfo, BigDecimal pid);

    @SelectProvider(method = "selectListByPageInfoFind", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> selectListByPageInfoFind(PageInfo<TblStaff> pageInfo, Find find);

    @Select("select * from TBL_STAFF where ORGID = #{orgid}")
    String selectFindOrgid(TblOrganization orgid);

    @Delete("DELETE FROM TBL_STAFF WHERE ORGID = #{orgid}")
    void deleteOrgid(TblOrganization orgid);

    @Select("SELECT staff.*,TOR.ORGNAME FROM TBL_STAFF staff"
            + " LEFT JOIN TBL_ORGANIZATION TOR ON staff.ORGID = TOR.ORGID "
            + "WHERE STAFFID = #{staffid}")
    TblStaff findByStaffid(BigDecimal staffid);

    @UpdateProvider(type = TblStaffMapperSqlConifg.class, method = "updateStaff")
    void updateStaff(TblStaff user);

    @UpdateProvider(type = TblStaffMapperSqlConifg.class, method = "updateStaffByUsername")
    void updateStaffByUsername(TblStaff user);

    @Select("SELECT * FROM TBL_STAFF WHERE STAFFID = #{userid}")
    TblStaff selectByUserId(String userid);

    @SelectProvider(method = "selectListByPage", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> selectListByPage(PageInfo<TblStaff> pageInfo, String pid);

    @SelectProvider(method = "selectListByPageCount", type = TblStaffMapperSqlConifg.class)
    Integer selectListByPageCount(PageInfo<TblStaff> pageInfo, String pid);

    @SelectProvider(method = "selectListBy", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> selectListBy(PageInfo<TblStaff> pageInfo, BigDecimal orgid);

    @SelectProvider(method = "selectListByOrgid", type = TblStaffMapperSqlConifg.class)
    Integer selectListByOrgid(PageInfo<TblStaff> pageInfo, BigDecimal orgid);

    @SelectProvider(method = "findPageListBySql", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> findPageListBySql(PageInfo<TblStaff> pageInfo);

    @SelectProvider(method = "findPageCountBySql", type = TblStaffMapperSqlConifg.class)
    Integer findPageCountBySql(PageInfo<TblStaff> pageInfo);

    @Select("SELECT * FROM TBL_STAFF where STAFFID = #{staffid}")
    TblStaff getExpert(BigDecimal staffid);

    @SelectProvider(method = "findAllPageInfoByacctid", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> findAllPageInfoByacctid(PageInfo<TblStaff> pageInfo, String bookid);

    @SelectProvider(method = "findAllPageInfoByacctidcCount", type = TblStaffMapperSqlConifg.class)
    Integer findAllPageInfoByacctidcCount(String bookid);

    @SelectProvider(method = "findByAll", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> findByAll(String pid, PageInfo<TblStaff> pageInfo);

    @Select(" select COUNT(*) from TBL_STAFF TS LEFT JOIN TBL_ORGANIZATION TOR ON TS.ORGID = TOR.ORGID WHERE TS.ORGID = #{pid} AND (TS.STATUS is NULL or TS.STATUS != 0)")
    Integer findByAllCount(String pid);

    @SelectProvider(method = "findByAllORGID", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> findByAllORGID(BigDecimal orgid, PageInfo<TblStaff> pageInfo);

    @Select("SELECT COUNT(*) FROM TBL_STAFF TS LEFT JOIN TBL_ORGANIZATION TOR ON TS.ORGID = TOR.ORGID " +
            "WHERE TOR.FATHERORGID = #{orgid} AND (TS.STATUS IS NULL OR TS.STATUS != 0)")
    Integer findByAllORGIDCount(BigDecimal orgid);

    @SelectProvider(method = "findAllPageBeanPid", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> findAllPageBeanPid(PageInfo<TblStaff> pageInfo, TblOrganization attribute);

    @SelectProvider(method = "findCountPageBeanPid", type = TblStaffMapperSqlConifg.class)
    Integer findCountPageBeanPid(PageInfo<TblStaff> pageInfo, TblOrganization attribute);

    @Select("select STA.STAFFID,ORA.ORGID,ORA.FATHERORGID,@ROW := @ROW + 1 AS SIGNED from(SELECT @ROW := 0) R,TBL_STAFF sta  INNER JOIN TBL_ORGANIZATION ora on STA.ORGID=ORA.ORGID  where  STA.STAFFID= #{cystaffid} HAVING SIGNED = 1")
    TblStaff findByOrag(String cystaffid);

    @Select("SELECT * FROM TBL_MANAGE_USER_RIGHT WHERE STAFFID = #{staffid}")
    List<TblManageRight> findMansgeUserRight(String staffid);

    @Select("SELECT * FROM TBL_STAFF WHERE JOBID = #{jobid}")
    List<TblStaff> findByJobid(BigDecimal jobid);

    @Select("SELECT PKYMSTAFFID FROM TBL_STAFF WHERE STAFFID = #{staffid}")
    String selectYmPkStaffIdByStaffId(BigDecimal staffid) throws Exception;

    @Results({
            @Result(column = "STAFFID", property = "staffid"),
            @Result(column = "USERNAME", property = "username"),
            @Result(column = "EMAIL", property = "email"),
            @Result(column = "MIBLEPHONE", property = "miblephone"),
            @Result(column = "PKYMSTAFFID", property = "pkYmStaffId"),
            @Result(column = "REALNAME", property = "realname"),
            @Result(column = "PKYMORGID", property = "orgname"),
            @Result(column = "ROLEIDS", property = "roleNames"),
            @Result(column = "PASSWORD", property = "password"),
    })
    @SelectProvider(method = "selectAllyinMaiStaffList", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> selectAllyinMaiStaffList(Integer orgId) throws Exception;

    @Update("UPDATE TBL_STAFF SET PKYMSTAFFID =#{pkYmStaffId} WHERE STAFFID = #{staffid}")
    void updatePkYmStaffIdByStaffId(String pkYmStaffId, BigDecimal staffid);

//	@SelectProvider(method = "selectTblExternal",type = TblStaffMapperSqlConifg.class)
//	List<TblExternalExpert> selectTblExternal(PageInfo<TblExternalExpert> pageInfo, Find find);


//	void insertTblStaff(TblStaff tblStaff);


//	@Select("SELECT * FROM TBL_STAFF WHERE STAFFID = #{userid}")
//	TblStaff selectStaffId(BigDecimal userid);

    @Update("UPDATE TBL_STAFF SET PASSWORD =#{password} WHERE STAFFID = #{staffId}")
    void updateStaffPassWord(Integer staffId, String password) throws Exception;

    @SelectProvider(method = "selectStaffListByPageInfo", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> selectStaffListByPageInfo(PageInfo<TblStaff> pageInfo, @Param("orgId") BigDecimal orgId, TblStaff staff)
            throws Exception;

    @SelectProvider(method = "selectStaffCountByPageInfo", type = TblStaffMapperSqlConifg.class)
    Integer selectStaffCountByPageInfo(BigDecimal orgId, TblStaff staff) throws Exception;

    //@Insert("INSERT INTO TBL_TRAININGSTAFF_ATT(STAFFID,ATTID) VALUES (${id},${attid})")
    //void insertAttInfoForStaff(@Param("id")BigDecimal id, @Param("attid")String attid) throws Exception;


    @InsertProvider(method = "insertAttInfoForStaff", type = TblStaffMapperSqlConifg.class)
    void insertAttInfoForStaff(BigDecimal id, String attid) throws Exception;

    @InsertProvider(method = "insertAttInfoForTrain", type = TblStaffMapperSqlConifg.class)
    void insertAttInfoForTrain(BigDecimal id, String attid) throws Exception;

    @Delete("DELETE FROM TBL_TRAININGSTAFF_ATT WHERE ATTID = #{attid}")
    void deleteStaffFileInfoByAttId(@Param("attid") BigDecimal attid) throws Exception;


    @Delete("DELETE FROM tbl_training_att WHERE ATTID = #{attid}")
    void deleteTrainFileInfoByAttId(@Param("attid") BigDecimal attid) throws Exception;

    @SelectProvider(method = "getAuditorInformationList", type = TblStaffMapperSqlConifg.class)
    JSONObject getAuditorInformationList(String orgid) throws Exception;

    @Select("select * from tbl_staff where historycode=#{historycode}")
    List<TblStaff> getStffListByHistorycode(String historycode);


    @Update("update tbl_staff s set s.orgid=(select orgid from tbl_organization where historycode=s.historydepartmentid ) where s.datasource='zz' ")
    void updateTblStaffSyncOrg();


    @Update("update tbl_staff s set s.jobid=(select jobid from tbl_job where historycode=s.jobid ) where s.datasource='zz'")
    void updateTblStaffSyncJob();

    @Update("update tbl_staff s set s.jobgradeid=(select gradeid from TBL_JOB_GRADE where historycode=s.jobgradeid ),s.title=(select aliasname from TBL_JOB_GRADE where historycode=s.jobgradeid) where s.datasource='zz'")
    void updateTblStaffSyncJobGrade();

    //==
    @SelectProvider(method = "selectListOrgInId", type = TblStaffMapperSqlConifg.class)
    List<TblOrganization> selectListOrgInId(@Param("sysorgid") String sysorgid) throws Exception;

    //==
    @SelectProvider(method = "selectListRoleInId", type = TblStaffMapperSqlConifg.class)
    List<TblRole> selectListRoleInId(@Param("roleids") String roleids) throws Exception;


    @Select("SELECT PKYMSTAFFID FROM TBL_STAFF WHERE STAFFID = (SELECT PRINCIPALSTAFFID FROM TBL_ORGANIZATION WHERE ORGID = #{orgid} )")
    String selectDeptManagerId(@Param("orgid")BigDecimal orgid) throws Exception;

    @Select("SELECT PKYMSTAFFID FROM TBL_STAFF WHERE ','||FGORGS||',' LIKE #{orgid} AND ROWNUM = 1")
    String selectbmfzrByOrgId(String orgid) throws Exception;

    @Results({
            @Result(column = "STAFFID", property = "staffid"),
            @Result(column = "USERNAME", property = "username"),
            @Result(column = "EMAIL", property = "email"),
            @Result(column = "MIBLEPHONE", property = "miblephone"),
            @Result(column = "PKYMSTAFFID", property = "pkYmStaffId"),
            @Result(column = "REALNAME", property = "realname"),
            @Result(column = "PKYMORGID", property = "orgname"),
            @Result(column = "ROLEIDSTRS", property = "roleIdStrs"),
            @Result(column = "PASSWORD", property = "password"),
            @Result(column = "PKYMJOBID", property = "jobName"),
            @Result(column = "ORGID", property = "orgid"),
    })
    @SelectProvider(method = "selectAllyinMaiStaffInfo", type = TblStaffMapperSqlConifg.class)
    TblStaff selectAllyinMaiStaffInfo(BigDecimal staffId) throws Exception;


    @SelectProvider(method = "selectListBmfzrByPageInfo", type = TblStaffMapperSqlConifg.class)
    @Results({
            @Result(column = "ORGNAME", property = "orgname"),
    })
    List<TblStaff> selectListBmfzrByPageInfo(PageInfo<TblStaff> pageInfo, BigDecimal roleid);

    @Select("select COUNT(*) from TBL_STAFF TS WHERE TS.ROLEIDSTRS LIKE #{orgidStr} ")
    Integer selectListBmfzrByPageInfoCount(String roleStr);


    @Select("select * from TBL_ROLE TR WHERE TR.COMPANYID = #{orgid} AND TR.RNAME = '部门负责人' ")
    List<TblRole> selectRoleByBmfzr(BigDecimal orgid);

    @SelectProvider(method = "selectUniqueCountUserName", type = TblStaffMapperSqlConifg.class)
    Integer selectUniqueCountUserName(String username, BigDecimal staffid) throws Exception;

    @SelectProvider(method = "selectUniqueCountFgld", type = TblStaffMapperSqlConifg.class)
    Integer selectUniqueCountFgld(String orgId, BigDecimal staffid) throws Exception;

    @SelectProvider(method = "selectUniqueCountBmfzr", type = TblStaffMapperSqlConifg.class)
    Integer selectUniqueCountBmfzr(String orgId, BigDecimal staffid) throws Exception;

    //==
    @SelectProvider(method = "selectListFgldByPageInfo", type = TblStaffMapperSqlConifg.class)
    @Results({
            @Result(column = "ORGNAME", property = "orgname"),
    })
    List<TblStaff> selectListFgldByPageInfo(PageInfo<TblStaff> pageInfo, Integer orgid);

    @Select("select COUNT(*) from TBL_STAFF TS "
            + " LEFT JOIN TBL_ORGANIZATION TORG ON TORG.ORGID = TS.ORGID "
            + " WHERE TS.FGORGS IS NOT NULL AND TORG.FATHERORGID= #{orgid} ")
    Integer selectListFgldByPageInfoCount(String orgid);

    @SelectProvider(method = "findAllLeader", type = TblStaffMapperSqlConifg.class)
    List<TblStaff> findAllLeader(PageInfo<TblStaff> pageInfo, String orgid);

    @SelectProvider(method = "findAllLeaderCount", type = TblStaffMapperSqlConifg.class)
    Integer findAllLeaderCount(String orgid);

    @Select("SELECT DISTINCT TS.STAFFID,TS.REALNAME,TS.USERNAME,ORG.ORGNAME,TFK.NEXTROLE,TS.PKYMSTAFFID FROM TBL_STAFF TS LEFT JOIN TBL_ORGANIZATION ORG ON TS.ORGID = ORG.ORGID LEFT JOIN TBL_FLOW_TASKINFO TFK ON TFK.NEXTSTAFFID = TS.STAFFID WHERE TFK.PROCESSID = #{processId} AND TFK.NEXTSTAFFID IS NOT NULL AND TFK.NEXTROLE != '提交人'")
    @Results({
            @Result(column = "STAFFID", property = "staffid"),
            @Result(column = "REALNAME", property = "realname"),
            @Result(column = "USERNAME", property = "username"),
            @Result(column = "ORGNAME", property = "orgname"),
            @Result(column = "NEXTROLE", property = "jobName"),
            @Result(column = "PKYMSTAFFID", property = "pkYmStaffId"),
    })
    List<TblStaff> selectCopyStaffList(String processId) throws Exception;

    @Select("SELECT * FROM TBL_STAFF WHERE STAFFID IN (${formStaffId})")
    List<TblStaff> selectListInfoByIds(@Param("formStaffId") String formStaffId) throws Exception;

    @Select("SELECT * FROM TBL_STAFF WHERE ORGID = #{orgid}")
	List<TblStaff> selectAllListByOrgid(TblOrganization orgid);
    @Select("SELECT RID from TBL_ROLE where rname = #{type} and COMPANYID =#{orgid}")
    Integer findLikeUser(@org.apache.ibatis.annotations.Param("orgid") Integer orgid, @org.apache.ibatis.annotations.Param("type") String type);

    @Select("select * from TBL_STAFF where ROLEIDSTRS like '%${rid}%' and STAFFID=#{staffId}")
    StaffResult findUserInfoExam(@org.apache.ibatis.annotations.Param("staffId") Integer staffId, @org.apache.ibatis.annotations.Param("rid") Integer rid);
    
    @Update("UPDATE TBL_STAFF SET ORGID = #{deptId} WHERE STAFFID = #{staffid}")
	void updateDeptIdByStaffId(BigDecimal staffid, BigDecimal deptId) throws Exception;

    @Select("SELECT PKYMSTAFFID FROM TBL_STAFF WHERE CHARGELEADERSTAFFID = #{staffId} ")
    String selectbmfzrByStaffId(BigDecimal staffId);

    @Select("SELECT DEPTIDSTRS FROM TBL_SYSTEM_DATA_RIGHT WHERE ORGID = #{orgid} AND ROLEID IN (${roleIdStrs})")
	List<String> selectDateDeptRelation(@Param("roleIdStrs")String roleIdStrs,@Param("orgid") BigDecimal orgid) throws Exception;

	/**
	 * 根据用户ID查找   用户所在的所有公司ID拼接的字符串  1,2,3
	 * @param staffid
	 * @return
	 * @throws Exception
	 */
	@Select("SELECT WM_CONCAT(ORGID) FROM TBL_USER_ORGRELATION WHERE STAFFID = #{staffid}")
	String selectOrgIdStrsByStaffId(BigDecimal staffid) throws Exception;

	 @Update("UPDATE TBL_STAFF SET ROLEIDSTRS =#{roleids} WHERE STAFFID = #{id}")
	 void updateuserRole(String  id,String roleids);

	@Select("SELECT PKYMSTAFFID FROM TBL_STAFF WHERE INSTR(','||ROLEIDSTRS||',',#{roleId}) > 0")
	List<String> selectYmPkStaffIdByRoleId(String roleId);

	@SelectProvider(method = "selectAllListByroleid", type = TblStaffMapperSqlConifg.class)
	@Results({
		@Result(column="STAFFID",property="staffid"),
		@Result(column="REALNAME",property="realname"),
		@Result(column="USERNAME",property="username"),
		@Result(column="MIBLEPHONE",property="miblephone"),
		@Result(column="EMAIL",property="email"),
		@Result(column="FIXEDPHONE",property="fixedphone"),
		@Result(column="ROLEIDSTRS",property="roleIdStrs"),
		@Result(column="ORGID",property="dataSource"),
		@Result(column="ORGNAME",property="orgname"),
	})
	List<TblStaff> selectAllListByroleid(String  roleid,String username,String realname,PageInfo<TblStaff> pageInfo);

	@SelectProvider(method = "selectAllListByroleidcount", type = TblStaffMapperSqlConifg.class)
	Integer  selectAllListByroleidcount(String  roleid,String username,String realname);

	@SelectProvider(method = "selectStaffListByRoleId", type = TblStaffMapperSqlConifg.class)
	@Results({
		@Result(column="STAFFID",property="staffid"),
		@Result(column="REALNAME",property="realname"),
		@Result(column="USERNAME",property="username"),
		@Result(column="MIBLEPHONE",property="miblephone"),
		@Result(column="EMAIL",property="email"),
		@Result(column="FIXEDPHONE",property="fixedphone"),
		@Result(column="ROLEIDSTRS",property="roleIdStrs"),
		@Result(column="ORGID",property="dataSource"),
		@Result(column="ORGNAME",property="orgname"),
	})
	List<TblStaff> selectStaffListByRoleId(PageInfo<TblStaff> pageInfo, String userName, String realName,
			Integer roleId,String deptName, String deptIds) throws Exception;

	@SelectProvider(method = "selectStaffCountByRoleId", type = TblStaffMapperSqlConifg.class)
	Integer selectStaffCountByRoleId(PageInfo<TblStaff> pageInfo, String userName, String realName, Integer roleId,
			String deptName, String deptIds) throws Exception;

	@SelectProvider(method = "selectStaffListToGrantRole", type = TblStaffMapperSqlConifg.class)
	@Results({
		@Result(column="STAFFID",property="staffid"),
		@Result(column="REALNAME",property="realname"),
		@Result(column="USERNAME",property="username"),
		@Result(column="MIBLEPHONE",property="miblephone"),
		@Result(column="EMAIL",property="email"),
		@Result(column="FIXEDPHONE",property="fixedphone"),
		@Result(column="ROLEIDSTRS",property="roleIdStrs"),
		@Result(column="ORGID",property="dataSource"),
		@Result(column="ORGNAME",property="orgname"),
		@Result(column="ISCHECKED",property="checked"),
	})
	List<TblStaff> selectStaffListToGrantRole(PageInfo<TblStaff> pageInfo, String userName, String realName,
			Integer roleId, String deptName, String deptIds, Integer isAll) throws Exception;

	@SelectProvider(method = "selectStaffCountToGrantRole", type = TblStaffMapperSqlConifg.class)
	Integer selectStaffCountToGrantRole(PageInfo<TblStaff> pageInfo, String userName, String realName, Integer roleId,
			String deptName, String deptIds, Integer isAll) throws Exception;

	@SelectProvider(method = "selectListByThemeHouse", type = TblStaffMapperSqlConifg.class)
	List<TblStaff> selectListByThemeHouse(PageInfo<TblStaff> pageInfo) throws Exception;

	@SelectProvider(method = "selectCountByThemeHouse", type = TblStaffMapperSqlConifg.class)
	Integer selectCountByThemeHouse(PageInfo<TblStaff> pageInfo) throws Exception;



}
