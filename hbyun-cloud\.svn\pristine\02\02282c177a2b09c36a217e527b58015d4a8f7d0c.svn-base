package com.huabo.audit;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.context.annotation.ComponentScan;


@SpringBootApplication(exclude = {org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration.class,
		org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration.class,
		DataSourceAutoConfiguration.class})
@EnableEurekaClient
@ComponentScan(basePackages = "com.huabo.audit")
@MapperScan(basePackages = {"com.huabo.audit.oracle.mapper"})
public class SpringbootHbyunAuditApplication {

    public static void main(String[] args) {
        SpringApplication.run(SpringbootHbyunAuditApplication.class, args);
        System.out.println("智能审计微服务启动成功！！");
    }
}
