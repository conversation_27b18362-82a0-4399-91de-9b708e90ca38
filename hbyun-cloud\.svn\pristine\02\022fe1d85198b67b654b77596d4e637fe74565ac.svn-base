<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huabo.system.mapper.TblSystemRefReminderAccessMapper">

    <select id="getList" resultType="com.huabo.system.entity.TblSystemRefReminderAccess">
        select t1.REMINDERSTAFFID,t2.CREATEDTIME,t2.REMINDERCONTENT
        from TBL_SYSTEM_REF_REMINDER_ACCESS t1
        inner join TBL_SYSTEM_REF_REMINDER t2 on t2.ID=t1.REMINDERID
        inner join TBL_SYSTEM_REFOPM_REMINDER t3 on t3.ID=t2.REFOPMID
        left join TBL_STAFF t4 on t4.STAFFID = t1.REMINDERSTAFFID
        where t3.CREATOR=#{param.creator} and t3.MODULEROUTE = #{param.moduleRoute}
        <if test="param.reminderStaffName != null and param.reminderStaffName != ''">
             and t4.REALNAME like concat('%',concat(#{param.reminderStaffName},'%'))
        </if>
        order by t2.id desc
    </select>

</mapper>