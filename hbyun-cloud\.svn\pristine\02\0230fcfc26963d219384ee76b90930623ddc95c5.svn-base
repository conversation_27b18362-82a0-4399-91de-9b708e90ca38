package com.huabo.monitor.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblOrganizationUtil;
import com.hbfk.util.JsonBean;
import com.hbfk.util.ResponseFormat;
import com.huabo.monitor.entity.TblBug;
import com.huabo.monitor.entity.TblBugCriterionEntity;
import com.huabo.monitor.entity.TblTestplan;
import com.huabo.monitor.service.ITblBugService;
import com.huabo.monitor.service.TblBugCriterionService;
import com.huabo.monitor.util.ConstClass;
import com.huabo.monitor.util.PageBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ICS: Internal Control Setting
 * defect standard controller
 */

@RestController
@Slf4j
@Api(value = "内控设置-缺陷标准", tags = {"内控设置-缺陷标准所有接口"})
@RequestMapping(value = "/nbkz")
public class ICSDefectStandardController {

    @Resource
    public PageBean pageBean;
    @Resource
    TblBugCriterionService tblBugCriterionService;

    @Resource
    ITblBugService tblBugService;

    @ApiOperation("	")
    @GetMapping(value = "/gzdg/def_quexian_list")
    public JsonBean qxbz(
            @ApiParam(name = "pageNumber", value = "分页当前页数", required = false) @RequestParam(value = "pageNumber", required = false, defaultValue = "1") Integer pageNumber,
            @ApiParam(name = "pageSize", value = "每页行数") @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @ApiParam(name = "orgid", value = "组织id") @RequestParam(value = "orgid") String orgId,
            @RequestHeader("token") String token
    ) {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }

//        TblOrganization attribute = (TblOrganization) request.getSession().getAttribute("hbOrgEntity");// 选则的机构
        IPage<TblBugCriterionEntity> iPage = tblBugCriterionService.findAll(pageNumber, orgId, pageSize);

        Map<String, Object> map = new HashMap<>();

        map.put("pageBean", iPage);
        return ResponseFormat.retParam(1, 200, map);
    }

    @ApiOperation("缺陷标准-保存")
    @PostMapping(value = "/gzdg/def_quexian_save")
    public JsonBean qxbz_add(
            @ApiParam(name = "con", value = "TblBugCriterionEntity实体类")@RequestBody  TblBugCriterionEntity con,
            @RequestHeader("token") String token) throws Exception {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }

        TblOrganizationUtil currentOrg = DealUserToken.parseUserToken(token).getCurrentOrg();
        BigDecimal orgid = currentOrg.getOrgid();
        if (con != null && con.getBugcriid() == null) {
            con.setVersion(1);
            con.setOrgid(Integer.parseInt(orgid.toString()));
            tblBugCriterionService.saveEntity(con);
        } else {
            con.setOrgid(Integer.parseInt(orgid.toString()));
            con.setVersion(con.getVersion() + 1);
            tblBugCriterionService.update(con);
        }
        return ResponseFormat.retParam(1, 200, "success");
    }


    @ApiOperation("缺陷标准-查看及修改时获取数据")
    @GetMapping(value = "/gzdg/def_quexian_detail")
    public JsonBean qxbz_detail(
            @ApiParam(name = "selectid", value = "selectid") @RequestParam(value = "selectid") String selectid,
            @RequestHeader("token") String token
    ) {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isNotBlank(selectid)) {
            TblBugCriterionEntity con = tblBugCriterionService.findByid(selectid);
            map.put("con", con);
        }

        return ResponseFormat.retParam(1, 200, map);
    }

    @ApiOperation("缺陷标准-删除")
    @DeleteMapping(value = "/gzdg/def_quexian_del")
    public JsonBean qxbz_del(
            @ApiParam(name = "ids", value = "ids") @RequestParam(value = "ids") String[] ids,
            @RequestHeader("token") String token
    ) {
        if (!ConstClass.checkToken(token)) {
            return ConstClass.tokenFailure();
        }

        String is = "ok";
        for (int i = 0; i < ids.length; i++) {
            TblBugCriterionEntity con = tblBugCriterionService.findByid(ids[i]);
            List<TblBug> findByCriterionId = tblBugService.findByCriterionId(con.getBugcriid());
            if (null != findByCriterionId && findByCriterionId.size() > 0) {
                return ResponseFormat.retParam(0, 50001, "error");

            }
            try {
                tblBugCriterionService.del(con.getBugcriid());
            } catch (Exception e) {
                is = con.getBugcrilevel();
            }
        }
        return ResponseFormat.retParam(1, 200, is);
    }

}