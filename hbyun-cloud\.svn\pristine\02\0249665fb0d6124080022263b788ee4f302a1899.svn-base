package com.huabo.fxgl.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblStaffUtil;
import com.hbfk.util.FtpUtil;
import com.hbfk.util.JsonBean;
import com.huabo.fxgl.entity.*;
import com.huabo.fxgl.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

import com.alibaba.fastjson.JSONObject;
import springfox.documentation.spring.web.json.Json;

import static org.springframework.web.bind.annotation.RequestMethod.GET;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 * @since 2022-08-05
 */
@RestController
@RequestMapping(value = "/attachment", method = {RequestMethod.GET, RequestMethod.POST})
@Api(tags = "附件API")
public class AttachmentController {

    @Autowired
    private IAttachmentService attachmentService;
    @Autowired
    private IRiskAssplanAttService riskAssplanAttService;
    @Autowired
    private IRiskeventAttService riskeventAttService;
    @Autowired
    private IRepAttService repAttService;
    @Autowired
    private IBugAttService bugAttService;


//    private final List<String> moduleList = Arrays.asList("PGJH,FXSJK,FXBG,ZDYBG,QXGL".split(","));

    @RequestMapping(value = "/delete", produces = "application/json; charset=utf-8", method = RequestMethod.POST)
    @ApiOperation(value = "删除附件接口")
    public JsonBean delete(HttpServletRequest request, HttpServletResponse response, Model map,
                                      @RequestParam(value = "type", required = false) Integer type,
                                      @ApiParam(name = "moduleType", value = "模块类型", example = "PGJH或FXSJK或FXBG或ZDYBG或QXGL", required = true) @RequestParam(name = "moduleType", required = false) String moduleType,
                                      @ApiParam(name = "moduleId", value = "模块ID", required = true) @RequestParam(name = "moduleId", required = true) String moduleId,
                                      @ApiParam(name = "attId", value = "附件ID", required = true) @RequestParam(name = "attId", required = true) String attId,
                                      @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {
        String result = null;
        BigDecimal aid = null;

        JsonBean jsonBean = new JsonBean();

//        TblStaffUtil staff = DealUserToken.parseUserToken(token);
        TblStaffUtil staffUtil = DealUserToken.parseUserToken(token);//得到了当前登录的用户信息
        if (staffUtil == null) {
            return new JsonBean(0, "用户已失效", null);
        }

        List<String> moduleList = Arrays.asList("PGJH,FXSJK,FXBG,ZDYBG,QXGL".split(","));
        if (StringUtils.isBlank(moduleType) || !moduleList.contains(moduleType)) {
            Map moduleMap = new HashMap();
            moduleMap.put("PGJH", "评估计划");
            moduleMap.put("FXSJK", "风险数据库");
            moduleMap.put("FXBG", "风险报告");
            moduleMap.put("ZDYBG", "自定义报告");
            moduleMap.put("QXGL", "缺陷管理");
            return new JsonBean(400, "参数错误：未提供正确的模块类别(moduleType)", moduleMap);
        }
        if (StringUtils.isBlank(attId)) {
            return new JsonBean(400, "参数错误：未提供正确的附件ID(attId)", null);
        }
        if (StringUtils.isBlank(moduleId)) {
            Map moduleMap = new HashMap();
            moduleMap.put("PGJH", "评估计划ID");
            moduleMap.put("FXSJK", "风险ID");
            moduleMap.put("FXBG", "风险报告ID");
            moduleMap.put("ZDYBG", "自定义报告ID");
            moduleMap.put("QXGL", "缺陷ID");
            return new JsonBean(400, "参数错误：未提供正确的模块ID(moduleId)", moduleMap);
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("ATTID", attId);
        try {
            switch (moduleType) {
                case "PGJH": {
                    //删除风险计划映射的附件
                    queryWrapper.eq("ASSPLANID", moduleId);
                    riskAssplanAttService.remove(queryWrapper);
                    break;
                }
                case "FXSJK": {
                    //删除风险事件映射的附件
                    queryWrapper.eq("RISEVEID", moduleId);
                    riskeventAttService.remove(queryWrapper);
                    break;
                }
                case "FXBG":
                case "ZDYBG": {
                    //删除风险报告映射的附件
                    queryWrapper.eq("REPORTID", moduleId);
                    repAttService.remove(queryWrapper);
                    break;
                }
                case "QXGL": {
                    //删除缺陷映射的附件
                    queryWrapper.eq("BUGID", moduleId);
                    bugAttService.remove(queryWrapper);
                    break;
                }
            }
            return new JsonBean(200, "删除附件成功！", null);
        } catch (Exception ex) {
            ex.printStackTrace();
            return new JsonBean(500, "删除附件时出错！", ex.getMessage());
        }
    }

    @RequestMapping(value = "/uploadFileAttInfo", produces = "application/json; charset=utf-8", method = RequestMethod.POST)
    @ApiOperation(value = "上传附件接口")
    public JsonBean uploadFileAttInfo(HttpServletRequest request, HttpServletResponse response, Model map, MultipartFile file,
                                      @RequestParam(value = "type", required = false) Integer type,
                                      @ApiParam(name = "moduleType", value = "模块类型", example = "PGJH,FXSJK,FXBG,ZDYBG,QXGL", required = true) @RequestParam(name = "moduleType", required = false) String moduleType,
                                      //@ApiParam(name = "moduleId", value = "模块ID", required = true) @RequestParam(name = "moduleId", required = true) String moduleId,
                                      @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {
        String result = null;
        BigDecimal aid = null;

        JsonBean jsonBean = new JsonBean();

//        TblStaffUtil staff = DealUserToken.parseUserToken(token);
        TblStaffUtil staffUtil = DealUserToken.parseUserToken(token);//得到了当前登录的用户信息
        if (staffUtil == null) {
            return new JsonBean(0, "用户已失效", null);
        }

        List<String> moduleList = Arrays.asList("PGJH,FXSJK,FXBG,ZDYBG,QXGL".split(","));
        if (StringUtils.isBlank(moduleType) || !moduleList.contains(moduleType)) {
            Map moduleMap = new HashMap();
            moduleMap.put("PGJH", "评估计划");
            moduleMap.put("FXSJK", "风险数据库");
            moduleMap.put("FXBG", "风险报告");
            moduleMap.put("ZDYBG", "自定义报告");
            moduleMap.put("QXGL", "缺陷管理");
            return new JsonBean(400, "参数错误：未提供正确的模块类别(moduleType)", moduleMap);
        }
        /*if (StringUtils.isBlank(moduleId)) {
            Map moduleMap = new HashMap();
            moduleMap.put("PGJH", "评估计划ID");
            moduleMap.put("FXSJK", "风险ID");
            moduleMap.put("FXBG", "风险报告ID");
            moduleMap.put("ZDYBG", "自定义报告ID");
            moduleMap.put("QXGL", "缺陷ID");
            return new JsonBean(400, "参数错误：未提供正确的模块ID(moduleId)", moduleMap);
        }*/

        // 创建一个通用的多部分解析器
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(request.getSession().getServletContext());
        // 判断 request 是否有文件上传,即多部分请求
        try {
            if (multipartResolver.isMultipart(request)) {
                // 转换成多部分request
                MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
                Map<String, Object> resultMap = attachmentService.uploadAttachment(multiRequest, staffUtil.getRealname().toString(), file);
                Attachment attachment = (Attachment) resultMap.get("attInfo");
                /*switch (moduleType) {
                    case "PGJH": {
                        //添加风险计划映射的附件
                        RiskAssplanAtt riskAssplanAtt = new RiskAssplanAtt(new BigDecimal(moduleId), attachment.getAttid());
                        riskAssplanAttService.save(riskAssplanAtt);
                        break;
                    }
                    case "FXSJK": {
                        //添加风险事件映射的附件
                        RiskeventAtt riskeventAtt = new RiskeventAtt(new BigDecimal(moduleId), attachment.getAttid());
                        riskeventAttService.save(riskeventAtt);`
                        break;
                    }
                    case "FXBG":
                    case "ZDYBG": {
                        //添加风险报告映射的附件
                        RepAtt repAtt = new RepAtt(new BigDecimal(moduleId), attachment.getAttid());
                        repAttService.save(repAtt);
                        break;
                    }
                    case "QXGL": {
                        //添加缺陷映射的附件
                        BugAtt bugAtt = new BugAtt(new BigDecimal(moduleId), attachment.getAttid());
                        bugAttService.save(bugAtt);
                        break;
                    }
                }*/
                return new JsonBean(200, "操作成功", resultMap);
            }

        } catch (Exception e) {
            e.printStackTrace();
            return new JsonBean(500, "操作异常", e.getMessage());
        }
        return new JsonBean(500, "上传失败", null);
    }

    /**
     * 文件下载
     */
    @ApiOperation(value = "附件文件下载", httpMethod = "POST", produces = "application/json")
    @RequestMapping(value = "/download", produces = "application/json; charset=utf-8",method = {GET})
    public void fileDownLoad(@RequestParam("id") String id, HttpServletResponse httpServletResponse) {
        Attachment tblAttachmentEntity = attachmentService.getById(id);
        FtpUtil.downUploadFile(tblAttachmentEntity.toTblAttachment(), httpServletResponse);
    }
}
