package com.huabo.audit.oracle.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Transient;

@Data
@ApiModel("工程项目造价表-内外部-统计表")
public class TblYqnsGcxmzjNwbStatisticalDto {
	


	/**
	 * 建设单位
	 */
	@ApiModelProperty(value = "建设单位")
	@Transient
	@TableField(value = "JSDW")
	private String jsdw;
	
	@ApiModelProperty(value="合计个数")
	@Transient
	@TableField(value = "HTBHCOUNT")
	private String htbhCount;


	@ApiModelProperty(value="合计金额合计")
	@Transient
	@TableField(value = "EDJESUM")
	private String edjeSum;



	@ApiModelProperty(value="抽审合计个数")
	@Transient
	@TableField(value = "ESSCJECOUNT")
	private String esscjeCount;


	@ApiModelProperty(value="抽审合计金额合计")
	@Transient
	@TableField(value = "ESSCJESUM")
	private String esscjeSum;

	@ApiModelProperty(value="外部单位合计个数")
	@Transient
	@TableField(value = "EXTERNALUNITCOUNT")
	private String externalunitCount;


	@ApiModelProperty(value="外部单位金额合计")
	@Transient
	@TableField(value = "EXTERNALUNITSUM")
	private String externalunitSum;


	@ApiModelProperty(value="内部单位合计个数")
	@Transient
	@TableField(value = "TENGFEIORINTERNALCOUNT")
	private String tengfeIorinternalCount;

	@ApiModelProperty(value="内部单位金额合计")
	@Transient
	@TableField(value = "TENGFEIORINTERNALSUM")
	private String tengfeIorinternalSum;


	@ApiModelProperty(value="内部多径单位个数")
	@Transient
	@TableField(value = "INTERNALMULTIPATHCOUNT")
	private String internalmultipathCount;


	@ApiModelProperty(value="内部多径单位合计")
	@Transient
	@TableField(value = "INTERNALMULTIPATHSUM")
	private String internalmultipathSum;


	@ApiModelProperty(value="内部腾飞单位个数")
	@Transient
	@TableField(value = "TENGFEICOUNT")
	private String tengfeiCount;


	@ApiModelProperty(value="内部腾飞金额合计")
	@Transient
	@TableField(value = "TENGFEISUM")
	private String tengfeiSum;


	@ApiModelProperty(value="工程建设公司个数")
	@Transient
	@TableField(value = "ENGINEERINGCONSTRUCTIONCOUNT")
	private String engineeringconstructionCount;


	@ApiModelProperty(value="工程建设公司金额合计")
	@Transient
	@TableField(value = "ENGINEERINGCONSTRUCTIONSUM")
	private String engineeringconstructionSum;
	
	@ApiModelProperty(value="筛选年度")
	@Transient
	@TableField(exist = false)
	private Integer queryYear;
}
