package com.huabo.audit.service.impl;

import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblStaffUtil;
import com.hbfk.util.*;
import com.huabo.audit.oracle.entity.ImplementPlanEntity;
import com.huabo.audit.oracle.entity.ImplementPlanTeamEntity;
import com.huabo.audit.oracle.mapper.ImplementPlanMapper;
import com.huabo.audit.service.ImplementPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName  ImplementPlanServiceImpl
 * @Description
 * @DATE 2023/10/27
 */

@Service
public class ImplementPlanServiceImpl implements  ImplementPlanService {

    @Autowired
    private  ImplementPlanMapper implementPlanMapper;

    @Override
    public JsonBean findAll(String token, Integer pageNumber, Integer pageSize, BigDecimal projectOrderId, String projectName, String planStarttime, String planEndtime) throws Exception {
        TblStaffUtil user = DealUserToken.parseUserToken(token);
        if(user == null) {
            return ResponseFormat.retParam(0,20006,null);
        }

         ImplementPlanEntity implementPlanEntity = new  ImplementPlanEntity();


        if(projectOrderId != null){
            implementPlanEntity.setProjectOrderId(projectOrderId);
        }

        if(StringUtil.isNotEmpty(projectName)){
            implementPlanEntity.setProjectName(projectName);
        }

        if(StringUtil.isNotEmpty(planStarttime)){
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date startTime = simpleDateFormat.parse(planStarttime);
            implementPlanEntity.setPlanStarttime(new java.sql.Date(startTime.getTime()));
        }

        if(StringUtil.isNotEmpty(planEndtime)){
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date endTime = simpleDateFormat.parse(planEndtime);
            implementPlanEntity.setPlanEndtime(new java.sql.Date(endTime.getTime()));
        }

        PageInfo< ImplementPlanEntity> pageInfo = new PageInfo< ImplementPlanEntity>();
        pageInfo.setPageSize(pageSize);
        pageInfo.setCurrentPage(pageNumber);

        List< ImplementPlanEntity> list = implementPlanMapper.selectByPageInfo(pageInfo,implementPlanEntity);
        Integer total = implementPlanMapper.selectCountByEntity(implementPlanEntity);

        pageInfo.setTlist(list);
        pageInfo.setTotalRecord(total);


        return ResponseFormat.retParam(1,200,pageInfo);
    }

    @Override
    public JsonBean findById(String id) throws Exception{

        ImplementPlanEntity implementPlanEntity = implementPlanMapper.selectById(id);
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("data", implementPlanEntity);
        return ResponseFormat.retParam(1,200,resultMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEntity( ImplementPlanEntity implementPlanEntity) throws Exception{
        implementPlanMapper.updateEntity(implementPlanEntity);
        implementPlanMapper.deleteImplementPlanTeamByIds(implementPlanEntity.getId()+"");
        implementPlanMapper.deleteAttachmentByIds(implementPlanEntity.getId()+"");
        //处理关联表关系
        if(implementPlanEntity.getTeams()!= null && implementPlanEntity.getTeams().size() > 0){
            for (ImplementPlanTeamEntity team : implementPlanEntity.getTeams()){
                implementPlanMapper.insertImplementPlanTeamWidthId(implementPlanEntity.getId(), team);
            }
        }
        //处理关联附件表关系
        if(StringUtil.isNotEmpty(implementPlanEntity.getAttIds())){
            String[] ids = implementPlanEntity.getAttIds().split(",");
            for (String attId : ids){
                implementPlanMapper.insertAttachmentsWidthId(implementPlanEntity.getId(),attId);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveEntity(String token,  ImplementPlanEntity implementPlanEntity) throws Exception{
        implementPlanMapper.insertEntity(implementPlanEntity);
        //处理关联表关系
        if(implementPlanEntity.getTeams()!= null && implementPlanEntity.getTeams().size() > 0){
            for (ImplementPlanTeamEntity team : implementPlanEntity.getTeams()){
                implementPlanMapper.insertImplementPlanTeamWidthId(implementPlanEntity.getId(), team);
            }
        }
        //处理关联附件表关系
        if(StringUtil.isNotEmpty(implementPlanEntity.getAttIds())){
            String[] ids = implementPlanEntity.getAttIds().split(",");
            for (String attId : ids){
                implementPlanMapper.insertAttachmentsWidthId(implementPlanEntity.getId(),attId);
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(String ids) throws Exception{

        implementPlanMapper.deleteEntity(ids);
        implementPlanMapper.deleteImplementPlanTeamByIds(ids);
        implementPlanMapper.deleteAttachmentByIds(ids);

    }


}
