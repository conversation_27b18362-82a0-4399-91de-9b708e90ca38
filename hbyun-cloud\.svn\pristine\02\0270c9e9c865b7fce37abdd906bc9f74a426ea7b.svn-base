package com.huabo.contract.oracle.mapper;

import com.hbfk.util.PageInfo;
import com.huabo.contract.oracle.entity.TblAttachment;
import com.huabo.contract.oracle.entity.TblCyhwUnit;
import com.huabo.contract.oracle.entity.TblDispuinfo;
import com.huabo.contract.oracle.entity.TblLegalDisputregistration;

import net.sf.json.JSONObject;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
public interface TblLegalDisputregistrationMapper extends BaseMapper<TblLegalDisputregistration> {

    @Select("SELECT * FROM TBL_LEGAL_DISPUTREGISTRATION tld " +
            "LEFT JOIN TBL_CYHW_UNIT tcu on tld.CONTRACTINFO = tcu.CONTRACTID " +
            "WHERE tld.DISPUTEID = #{disputeid}")
    TblLegalDisputregistration selectDisputeId(Integer disputeid);

    @Select("SELECT * FROM TBL_LEGAL_DISPUTREGISTRATION tld WHERE tld.DISPUTEID = (SELECT DISPUTEID FROM TBL_LEGAL_LITIGATIONSETTLEMENT WHERE LITIGATIONID = #{litigationid})")
    TblLegalDisputregistration findByIdByLitigation(Integer litigationid);
    
    @Select("SELECT * FROM TBL_LEGAL_DISPUTREGISTRATION tld WHERE tld.DISPUTEID = (SELECT DISPUTEID FROM TBL_LEGAL_ARBITRATSETTLEMENT WHERE ARBITRAID = #{arbitraid})")
    TblLegalDisputregistration findByIdByArbitra(Integer arbitraid);
    
    @SelectProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="findListByPageInfo")
    List<TblLegalDisputregistration> findListByPageInfo(String companyId,PageInfo<TblLegalDisputregistration> pageInfo, TblLegalDisputregistration dispute, BigDecimal pid);

    @Select("SELECT * FROM TBL_LEGAL_DISPUTREGISTRATION WHERE DISPUTEID = #{pid}")
    String findDisputeIdsForDisputeClose(BigDecimal pid);

    @SelectProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="findListByPageInfoCount")
    Integer findListByPageInfoCount(String companyId,TblLegalDisputregistration dispute, BigDecimal pid);
    
    
    
    @SelectProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="findListLSByPageInfo")
    List<TblLegalDisputregistration> findListLSByPageInfo(String companyId,PageInfo<TblLegalDisputregistration> pageInfo, TblLegalDisputregistration dispute, BigDecimal pid);

    @SelectProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="findListLSByPageInfoCount")
    Integer findListLSByPageInfoCount(String companyId,TblLegalDisputregistration dispute, BigDecimal pid);
    
    
    
    
    @SelectProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="findClassicCaseListByPageInfo")
    List<TblLegalDisputregistration> findClassicCaseListByPageInfo(String companyId,PageInfo<TblLegalDisputregistration> pageInfo, TblLegalDisputregistration dispute, BigDecimal pid);
    
    @SelectProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="findClassicCaseListByPageInfoCount")
    Integer findClassicCaseListByPageInfoCount(String companyId,TblLegalDisputregistration dispute, BigDecimal pid);
    
    
    

    @InsertProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="saveDiputregistration")
    @Options(useGeneratedKeys=true, keyProperty="disputeid", keyColumn="DISPUTEID")
    void saveDiputregistration(TblLegalDisputregistration dispute);

    @InsertProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="saveAttacheMent")
    void saveAttacheMent(Integer type, Integer bid, BigDecimal aid);

    @Delete("DELETE FROM TBL_LEGAL_DISPUTREGISTRATION WHERE DISPUTEID = #{attid}")
    void deleteRelation(String attid);

    @Delete("DELETE FROM TBL_LEGAL_DISPUTREGISTRATION WHERE DISPUTEID = #{disputeId}")
    void removecaseInformation(Integer disputeId);


    @Select("SELECT * FROM TBL_LEGAL_DISPUTREGISTRATION WHERE DISPUTEID = #{disputeId}")
    TblLegalDisputregistration findByDisputeId(Integer disputeId);

    @SelectProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="findListByPageInfoDispute")
    List<TblLegalDisputregistration> findListByPageInfoDispute(PageInfo<TblLegalDisputregistration> pageInfo, TblLegalDisputregistration dispute, BigDecimal pid,String disputeIds);

    @SelectProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="findListByPageInfoDisputeCount")
    Integer findListByPageInfoDisputeCount(TblLegalDisputregistration dispute, BigDecimal pid,String disputeIds);

    @SelectProvider(type = TblLegalDisputregistrationMapperSqlConfig.class,method = "findListByDispute")
    List<TblLegalDisputregistration> findListByDispute(PageInfo<TblLegalDisputregistration> pageInfo, TblLegalDisputregistration dispute);

    @SelectProvider(type = TblLegalDisputregistrationMapperSqlConfig.class,method = "findListByDisputeCount")
    Integer findListByDisputeCount(TblLegalDisputregistration dispute);

    @Select("SELECT * FROM TBL_LEGAL_DISPUTREGISTRATION tld " +
            "LEFT JOIN TBL_CYHW_UNIT tcu on tld.CONTRACTINFO = tcu.CONTRACTID WHERE tld.DISPUTEID = #{disputeId}")
    @Options(useGeneratedKeys=true, keyProperty="disputeId", keyColumn="DISPUTEID")
    TblLegalDisputregistration findDisputeId(Integer disputeId);

    @UpdateProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="updateDiputregistration")
    void updateDiputregistration(TblLegalDisputregistration stration);

    @Select("SELECT tst.REALNAME AS zxstaffname,tld.*, ts.REALNAME AS realname,tcu.*,org.* " +
            "FROM TBL_LEGAL_DISPUTREGISTRATION tld " +
            "LEFT JOIN TBL_STAFF tst on tld.DISPUTEUNDERTAKER = tst.STAFFID " +
            "LEFT JOIN TBL_CYHW_UNIT tcu ON tld.CONTRACTINFO = tcu.CONTRACTID " +
            "LEFT JOIN TBL_STAFF ts ON tld.CREATESTAFF = ts.STAFFID " +
            "INNER JOIN TBL_ORGANIZATION org ON ts.ORGID = org.ORGID " +
            " WHERE tld.DISPUTEID =  #{disputeId}")
    TblLegalDisputregistration findBydisputeId(Integer disputeId);

    @Select("SELECT * FROM TBL_LEGAL_DISPUTREGISTRATION tld\n" +
            "LEFT JOIN TBL_CYHW_UNIT tcu on tld.CONTRACTINFO = tcu.CONTRACTID\n" +
            "WHERE tld.DISPUTEID = #{disputeId} AND tld.LINKORG = #{orgid}")
    TblLegalDisputregistration findByOrgid(Integer disputeId, BigDecimal orgid);

    @SelectProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="findAttacheMentByBid")
    List<TblAttachment> findAttacheMentByBid(Integer type, Integer bid);

    @DeleteProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="deleteAttacheMentByBid")
    void deleteAttacheMentByBid(Integer type, Integer aid);

    @DeleteProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="deleteAttacheMents")
    void deleteAttacheMents(Integer type, BigDecimal bid);

    @SelectProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="findDisputeIdsForDispute")
    List<TblDispuinfo> findDisputeIdsForDispute(String colName, String tableName, String orgColName, String whSql, BigDecimal oid);


//    @Select("SELECT * FROM TBL_LEGAL_CLOSEINFORMATION tlc \n" +
//            "LEFT JOIN TBL_STAFF ts on tlc.CREATESTAFF = ts.STAFFID\n" +
//            "LEFT JOIN TBL_LEGAL_DISPUTREGISTRATION tld on tlc.DISPUTINFO = tld.DISPUTEID WHERE tlc.CLOSEID = #{closeId}")
//    TblLegalCloseinformation selectCloseid(Integer closeid);
    
    
    @Update("UPDATE TBL_LEGAL_DISPUTREGISTRATION SET ISCLASSICCASE = 1 WHERE disputeid = #{disputeid}")
	void addClassicCase(Integer disputeid);
    
    
    @Update("UPDATE TBL_LEGAL_DISPUTREGISTRATION SET ISCLASSICCASE = 2 WHERE disputeid = #{disputeid}")
   	void delClassicCase(Integer disputeid);

    @Select("SELECT MAX(to_number(SUBSTR(DISPUTENO,INSTR(DISPUTENO,'-',-1)+1,LENGTH(DISPUTENO)-INSTR(DISPUTENO,'-',-1)))) FROM TBL_LEGAL_DISPUTREGISTRATION WHERE DISPUTENO LIKE #{disputeno}")
	String selectCountByDisputreNo(String disputeno) throws Exception;
    
    @Select("select  Orgmeno as key,(select  nvl(sum(nvl(litigationamount,0)),0)  from TBL_LEGAL_DISPUTREGISTRATION where to_char(createtime,'yyyy')=#{year} and linkorg =orgid) as money from tbl_organization where (fatherorgid=#{orgid} or orgid=#{orgid}) and orgtype>0  and status=0 order by orgid ")
    List<JSONObject> selectMoneyValue(Integer year,BigDecimal orgid) throws Exception;
    
     @Select(" select  Orgmeno as key,"+
     " (select count(*) from TBL_LEGAL_DISPUTREGISTRATION where nvl(whethersued,0)=1 and   to_char(createtime,'yyyy')=#{year} "+
     " and linkorg=orgid) as yg,"+
     " (select count(*) from TBL_LEGAL_DISPUTREGISTRATION where nvl(whethersued,0)=2 and   to_char(createtime,'yyyy')=#{year} "+
     "  and linkorg=orgid) as bg,"+
     " (select count(*) from TBL_LEGAL_DISPUTREGISTRATION where nvl(whethersued,0)=3 and   to_char(createtime,'yyyy')=#{year} "+
     " and linkorg=orgid) as dsr"+
     " from tbl_organization where (fatherorgid=#{orgid} or orgid=#{orgid}) and orgtype>0  and status=0 order by orgid ")
     List<JSONObject>  selectSsTypeList(Integer year,BigDecimal orgid) throws Exception;
     
     @Select("select Orgmeno as key,"+
     " (select count(*) from TBL_LEGAL_DISPUTREGISTRATION where to_char(createtime,'yyyy')=#{year}  and linkorg=orgid ) xz,"+
     " (select count(*) from TBL_LEGAL_DISPUTREGISTRATION where to_char(createtime,'yyyy')=#{year}  and linkorg=orgid "+ 
     " and disputeid not in ( select distinct disputeid from  TBL_LEGAL_LITIGATIONSETTLEMENT where"+ 
     " litigationid in (select distinct litigationid from TBL_LEGAL_CLOSESUM where litigationid is not null) and disputeid is not null )) wj "+ 
     " from tbl_organization where (fatherorgid=#{orgid} or orgid=#{orgid}) and orgtype>0  and status=0 order by orgid")
     List<JSONObject>  selectJfslList(Integer year,BigDecimal orgid) throws Exception;

     @Select("select  Orgmeno as key,"+
    		 " (select count(*) from TBL_LEGAL_PROCEEDINGSRECORD where  to_char(createtime,'yyyy')=#{year}  "+
    		 " and linkorg=orgid  and PORCEEDSTAGE in ('一审审理中','一审已判决') and PORCEEDSTAGE is not null) ys,"+
    		 " (select count(*) from TBL_LEGAL_PROCEEDINGSRECORD where  to_char(createtime,'yyyy')=#{year}  "+
    		 "  and linkorg=orgid  and PORCEEDSTAGE in ('二审审理中','二审已判决') and PORCEEDSTAGE is not null) es,"+
    		 " (select count(*) from TBL_LEGAL_PROCEEDINGSRECORD where  to_char(createtime,'yyyy')=#{year}  "+
    		 " and linkorg=orgid  and PORCEEDSTAGE in ('再审审理中','再审已判决') and PORCEEDSTAGE is not null ) zs "+
    		 " from tbl_organization where (fatherorgid=#{orgid} or orgid=#{orgid}) and orgtype>0  and status=0 order by orgid")
     List<JSONObject>  selectSlztList(Integer year,BigDecimal orgid) throws Exception;

     

    @SelectProvider(type=TblLegalDisputregistrationMapperSqlConfig.class,method="selectDisputeMoneyList")
	List<JSONObject> selectDisputeMoneyList(Integer year, BigDecimal orgid) throws Exception;

}
