package com.huabo.audit.oracle.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;

/**
 * <AUTHOR>
 * @ClassName LeaveAudit2LEntity
 * @Description
 * @DATE 2023/9/14
 */
@Data
@TableName("TBL_YQNS_LEAVE_AUDIT_2L")
@ApiModel("二级单位及成员单位离任审计")
@Accessors(chain = true)
public class LeaveAudit2LEntity implements Serializable {

    @TableId(value="ID", type= IdType.AUTO)
    @ApiModelProperty("ID")
    private BigDecimal id;

    @TableField(value="PROJECT_NAME")
    @ApiModelProperty("审计项目名称")
    private String projectName;

    @TableField(value="AUDIT_ORG")
    @ApiModelProperty("被审计单位")
    private String auditOrgId;

	@TableField(exist = false)
	private TblOrganization auditOrg;

    @TableField(value="ENTRUST_NO")
    @ApiModelProperty("委托书编号")
    private String entrustNo;

    @TableField(value="ENTRUST_TIME")
    @ApiModelProperty("委托时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entrustTime;

    @TableField(value="AUDIT_START_TIME")
    @ApiModelProperty("审计任职起始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date auditStartTime;

    @TableField(value="AUDIT_END_TIME")
    @ApiModelProperty("审计任职结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date auditEndTime;

    @TableField(value="PERSON_IDS")
    @ApiModelProperty("下发的人员ID")
    private String personIds;

    @TableField(value="CREATE_USER")
    @ApiModelProperty("创建人")
    private TblStaff createUser;

    @TableField(value="CREATE_TIME")
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    @TableField(value = "STATUS")
    @ApiModelProperty("状态")
    private Integer status;
}
