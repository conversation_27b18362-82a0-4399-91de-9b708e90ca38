package com.huabo.contract.mysql.mapper;

import com.hbfk.util.DateUtil;
import com.huabo.contract.mysql.entity.TblOrganizationMySql;

public class TblOrganizationMySqlMapperSqlConfig {

    public String saveToz(TblOrganizationMySql toz) {
        StringBuffer column = new StringBuffer("INSERT INTO TBL_ORGANIZATION (ORGID");
        StringBuffer value = new StringBuffer(" VALUES (MYTASK_SEQUENCE.nextval");

        if(toz.getOrgname() != null && !"".equals(toz.getOrgname())){
            column.append(",ORGNAME");
            value.append(",'"+toz.getOrgname()+"'");
        }
        if(toz.getFatherorgid() != null && !"".equals(toz.getFatherorgid())){
            column.append(",FATHERORGID");
            value.append(",'"+toz.getFatherorgid()+"'");
        }
        if(toz.getOrgnumber() != null && !"".equals(toz.getOrgnumber())){
            column.append(",ORGNUMBER");
            value.append(",'"+toz.getOrgnumber()+"'");
        }
        if(toz.getOrgmeno() != null && !"".equals(toz.getOrgmeno())){
            column.append(",ORGMENO");
            value.append(",'"+toz.getOrgmeno()+"'");
        }
        if(toz.getMemo() != null && !"".equals(toz.getMemo())){
            column.append(",MEMO");
            value.append(",'"+toz.getMemo()+"'");
        }
        if(toz.getIcode() != null && !"".equals(toz.getIcode())){
            column.append(",ICODE");
            value.append(",'"+toz.getIcode()+"'");
        }
        if(toz.getOrgtype() != null && !"".equals(toz.getOrgtype())){
            column.append(",ORGTYPE");
            value.append(",'"+toz.getOrgtype()+"'");
        }
        if(toz.getAuditType() != null && !"".equals(toz.getAuditType())){
            column.append(",AUDITTYPE");
            value.append(",'"+toz.getAuditType()+"'");
        }
        if(toz.getStatus() != null && !"".equals(toz.getStatus())){
            column.append(",STATUS");
            value.append(",'"+toz.getStatus()+"'");
        }
        if(toz.getIszy() != null && !"".equals(toz.getIszy())){
            column.append(",ISZY");
            value.append(",'"+toz.getIszy()+"'");
        }
        if(toz.getHyzsktype() != null && !"".equals(toz.getHyzsktype())){
            column.append(",HYZSKTYPE");
            value.append(",'"+toz.getHyzsktype()+"'");
        }
        if(toz.getOrderid() != null && !"".equals(toz.getOrderid())){
            column.append(",ORDERID");
            value.append(",'"+toz.getOrderid()+"'");
        }
        if(toz.getOutsideid() != null && !"".equals(toz.getOutsideid())){
            column.append(",OUTSIDEID");
            value.append(",'"+toz.getOutsideid()+"'");
        }
        if(toz.getOutsideid() != null && !"".equals(toz.getOutsideid())){
            column.append(",OUTSIDEOPENDID");
            value.append(",'"+toz.getOutsideid()+"'");
        }
        if(toz.getIsautonumber() != null && !"".equals(toz.getIsautonumber())){
            column.append(",ISAUTONUMBER");
            value.append(",'"+toz.getIsautonumber()+"'");
        }
        if(toz.getOrgcreate() != null && !"".equals(toz.getOrgcreate())){
            column.append(",ORGCREATE");
            value.append(",TO_DATE('"+ DateUtil.parseDate(toz.getOrgcreate(),"yyyy-MM-dd HH:mm:ss") +"', 'YYYY-MM-DD HH24:MI:SS')");
        }
        if(toz.getDuties() != null && !"".equals(toz.getDuties())){
            column.append(",DUTIES");
            value.append(",'"+toz.getDuties()+"'");
        }
        if(toz.getIndustryid() != null && !"".equals(toz.getIndustryid())){
            column.append(",INDUSTRYID");
            value.append(",'"+toz.getIndustryid()+"'");
        }
        if(toz.getBywx() != null && !"".equals(toz.getBywx())){
            column.append(",BYWX");
            value.append(",'"+toz.getBywx()+"'");
        }
        if(toz.getDatasource() != null && !"".equals(toz.getDatasource())){
            column.append(",DATASOURCE");
            value.append(",'"+toz.getDatasource()+"'");
        }
        if(toz.getHistorycode() != null && !"".equals(toz.getHistorycode())){
            column.append(",HISTORYCODE");
            value.append(",'"+toz.getHistorycode()+"'");
        }
        if(toz.getHistorydepartmentid() != null && !"".equals(toz.getHistorydepartmentid())){
            column.append(",HISTORYDEPARTMENTID");
            value.append(",'"+toz.getHistorydepartmentid()+"'");
        }

        column.append(")");
        value.append(")");
        String sql = column.toString()+value.toString();
        return sql;
    }


    public String updateToz(TblOrganizationMySql toz) {
        StringBuffer sql = new StringBuffer("UPDATE TBL_ORGANIZATION SET ORGNAME = '"+toz.getOrgname()+"'");

        if(toz.getFatherorgid() != null && !"".equals(toz.getFatherorgid())) {
            sql.append(" , FATHERORGID = '"+toz.getFatherorgid() +"'");
        }
        if(toz.getOrgnumber() != null && !"".equals(toz.getOrgnumber())) {
            sql.append(" , ORGNUMBER = '"+toz.getOrgnumber()+"'");
        }
        if(toz.getOrgmeno() != null && !"".equals(toz.getOrgmeno())) {
            sql.append(" , ORGMENO = '"+toz.getOrgmeno()+"'");
        }
        if(toz.getMemo() != null && !"".equals(toz.getMemo())) {
            sql.append(" , MEMO = '"+toz.getMemo()+"'");
        }
        if(toz.getIcode() != null && !"".equals(toz.getIcode())) {
            sql.append(" , ICODE = '"+toz.getIcode()+"'");
        }
        if(toz.getOrgtype() != null && !"".equals(toz.getOrgtype())) {
            sql.append(" , ORGTYPE = '"+toz.getOrgtype()+"'");
        }
        if(toz.getAuditType() != null && !"".equals(toz.getAuditType())) {
            sql.append(" , AUDITTYPE = '"+toz.getAuditType()+"'");
        }
        if(toz.getStatus() != null && !"".equals(toz.getStatus())) {
            sql.append(" , STATUS = '"+toz.getStatus()+"'");
        }
        if(toz.getIszy() != null && !"".equals(toz.getIszy())) {
            sql.append(" , ISZY = '"+toz.getIszy() +"'");
        }
        if(toz.getHyzsktype() != null && !"".equals(toz.getHyzsktype())) {
            sql.append(" , HYZSKTYPE = '"+toz.getHyzsktype()+"'");
        }
        if(toz.getOrderid() != null && !"".equals(toz.getOrderid())) {
            sql.append(" , ORDERID = '"+toz.getOrderid()+"'");
        }
        if(toz.getOutsideid() != null && !"".equals(toz.getOutsideid())) {
            sql.append(" , OUTSIDEID = '"+toz.getOutsideid()+"'");
        }
        if(toz.getOutsideid() != null && !"".equals(toz.getOutsideid())) {
            sql.append(" , OUTSIDEOPENDID = '"+toz.getOutsideid()+"'");
        }
        if(toz.getIsautonumber() != null && !"".equals(toz.getIsautonumber())) {
            sql.append(" , ISAUTONUMBER = '"+toz.getIsautonumber()+"'");
        }
        if(toz.getOrgcreate() != null && !"".equals(toz.getOrgcreate())) {
            sql.append(" ,ORGCREATE = TO_DATE('"+DateUtil.parseDate(toz.getOrgcreate(),"yyyy-MM-dd HH:mm:ss") +"', 'YYYY-MM-DD HH24:MI:SS')");
        }
        if(toz.getIsinitialization() != null && !"".equals(toz.getIsinitialization())) {
            sql.append(" , ISINITIALIZATION = '"+toz.getIsinitialization()+"'");
        }
        if(toz.getDuties() != null && !"".equals(toz.getDuties() )) {
            sql.append(" , DUTIES = '"+toz.getDuties() +"'");
        }
        if(toz.getIndustryid() != null && !"".equals(toz.getIndustryid())) {
            sql.append(" , INDUSTRYID = '"+toz.getIndustryid()+"'");
        }
        if(toz.getBywx() != null && !"".equals(toz.getBywx())) {
            sql.append(" , BYWX = '"+toz.getBywx()+"'");
        }
        if(toz.getDatasource() != null && !"".equals(toz.getDatasource())) {
            sql.append(" , DATASOURCE = '"+toz.getDatasource()+"'");
        }
        if(toz.getHistorycode() != null && !"".equals(toz.getHistorycode())) {
            sql.append(" , HISTORYCODE = '"+toz.getHistorycode()+"'");
        }
        if(toz.getHistorydepartmentid() != null && !"".equals(toz.getHistorydepartmentid())) {
            sql.append(" , HISTORYDEPARTMENTID = '"+toz.getHistorydepartmentid()+"'");
        }

        sql.append(" WHERE ORGID = '"+toz.getOrgid()+"'");
        return sql.toString();
    }
}
