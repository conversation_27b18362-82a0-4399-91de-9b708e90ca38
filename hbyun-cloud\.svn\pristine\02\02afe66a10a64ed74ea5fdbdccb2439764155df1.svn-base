package com.huabo.contract.oracle.entity;

import java.math.BigDecimal;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
  @TableName("TBL_LEGAL_LITIGATIONSETTLEMENT")
@ApiModel(value="诉讼过程", description="")
public class TblLegalLitigationsettlement implements Serializable {
//法务管理-诉讼过程-新建接口
  private static final long serialVersionUID = 1L;

    @TableId("LITIGATIONID")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY,generator = "select HIBERNATE_SEQUENCE.nextval from dual")
    private Integer litigationid;

  @TableField("FIRSTCOURT")//案件名称。必填
  private String firstcourt;

  @TableField("PRESIDINGJUDGE")//审判长
  private String presidingjudge;

  @TableField("COLLEGIALPANEL")//合议庭组成
  private String collegialpanel;

  @TableField("LITIGATIONAMOUNT")//诉讼金额
  private BigDecimal litigationamount;

  @TableField("DEALDATE")//诉讼受理日期
  @JSONField(format = "yyyy-MM-dd")
  @DateTimeFormat(pattern="yyyy-MM-dd")
  private Date dealdate;

  @TableField("FIRSTHEARINGDATE")
  @JSONField(format = "yyyy-MM-dd")
  @DateTimeFormat(pattern="yyyy-MM-dd")
  private Date firsthearingdate;//首次开庭日期

  @TableField("LITIGATIONENDDATE")
  @JSONField(format = "yyyy-MM-dd")
  @DateTimeFormat(pattern="yyyy-MM-dd")
  private Date litigationenddate;//結案日期

  @TableField("LITIGATIONRESULT")//谈判结果
  private String litigationresult;

  @TableField("CREATESTAFF")
  private BigDecimal createstaff;

  @TableField("CREATETIME")
  private LocalDateTime createtime;

  @TableField("LINKORG")
  private BigDecimal linkorg;

  @TableField("LITISTATUS")
  private BigDecimal litistatus;

  @TableField("ACTIONOBJECT")//诉讼标的物
  private String actionobject;

  @TableField("ISEFFECT")//判决是否生效：1是，2否
  private BigDecimal iseffect;

  @TableField("JUDGEMONEY")//判决金额
  private BigDecimal judgemoney;

  @TableField("DISPUTEINFO")
  private BigDecimal disputeinfo;
  
  
  //
  @ApiModelProperty(value = "关联纠纷登记")
  @TableField("DISPUTEID")
  private Integer disputeid;

	@ApiModelProperty(value = "关联纠纷登记名称")
	private String disputeidname;
  
  @ApiModelProperty(value = "关联仲裁")
  @TableField("ARBITRAID")//
  private Integer arbitraid;
  
  @ApiModelProperty(value = "填报单位")
  @TableField("FILLUNIT")//
  private String fillunit;
  
  @ApiModelProperty(value = "所属集团")
  @TableField("BELONGGROUP")
  private String belonggroup;
  
  @ApiModelProperty(value = "案由")
  @TableField("CAUSECASE")
  private String causecase;
  
  @ApiModelProperty(value = "纠纷承办")
  @TableField("DISPUTEHAND")
  private String disputehand;
  
  @ApiModelProperty(value = "纠纷名称")
  @TableField("DISPUTENAME")
  private String disputename;
  
  @ApiModelProperty(value = "纠纷类型")
  @TableField("DISPUTETYPE")
  private String disputetype;
  
  @ApiModelProperty(value = "律师事务所")
  @TableField("LAWFIRM")
  private String lawfirm;
  
  @ApiModelProperty(value = "是否外聘律师")
  @TableField("ISATTORNEY")
  private BigDecimal isattorney;
  
  @ApiModelProperty(value = "律师名称")
  @TableField("LAWNAME")
  private String lawname;
  
  @ApiModelProperty(value = "外聘律师类型")
  @TableField("LAWTYPE")
  private String lawtype;
  
  @ApiModelProperty(value = "关联合同")
  @TableField("ISLINKCONTRACT")
  private BigDecimal islinkcontract;
  
  @ApiModelProperty(value = "纠纷承办人")
  @TableField("DISPUTEUNDER")
  private Integer disputeunder;

	@Transient
	@ApiModelProperty("纠纷承办人名称")
	private String disputeundername;

  @ApiModelProperty(value = "诉讼地位")
  @TableField("LITIGIOUSSTATUS")
  private String litigiousstatus;
  
  @ApiModelProperty(value = "我方涉诉企业")
  @TableField("OURDISPUTEORG")
  private String ourdisputeorg;
  
  @ApiModelProperty(value = "对方当事人")
  @TableField("OPPOSITEPARTY")
  private String oppositeparty;
  
  @ApiModelProperty(value = "标的额（万元）")
  @TableField("SUBJECTAMOUNT")
  private String subjectamount;
  
  @ApiModelProperty(value = "管辖机构")
  @TableField("JURISDICTION")
  private String jurisdiction;
  
  @ApiModelProperty(value = "纠纷状态")
  @TableField("DISPUTESTATUS")
  private BigDecimal disputestatus;
  
  @ApiModelProperty(value = "业务发生时间")
  @JSONField(format = "yyyy-MM-dd")
  @DateTimeFormat(pattern="yyyy-MM-dd")
  @TableField("BIZTIME")
  private Date biztime;
  
  @ApiModelProperty(value = "立案时间")
  @JSONField(format = "yyyy-MM-dd")
  @DateTimeFormat(pattern="yyyy-MM-dd")
  @TableField("CASETIME")
  private Date casetime;
  
  @ApiModelProperty(value = "是否涉及刑事案件")
  @TableField("ISCRIMINALCASES")
  private BigDecimal iscriminalcases;
  
  @ApiModelProperty(value = "对方当事人是否具备偿还能力")
  @TableField("ISREPAYMENT")
  private BigDecimal isrepayment;
  
  @ApiModelProperty(value = "是否有担保或保全措施")
  @TableField("ISPRESERVATION")
  private BigDecimal ispreservation;
  
  @ApiModelProperty(value = "执行状态")
  @TableField("EXECSTATUS")
  private BigDecimal execstatus;
  
  @ApiModelProperty(value = "是否为紧急事项")
  @TableField("ISURGENT")
  private BigDecimal isurgent;
  
  @ApiModelProperty(value = "紧急情况说明")
  @TableField("URGENTMEMO")
  private String urgentmemo;
  
  @ApiModelProperty(value = "案件的基本情况")
  @TableField("CASEBASICINFO")
  private String casebasicinfo;
  
  @ApiModelProperty(value = "企业采取的处理措施或诉讼思路")
  @TableField("TAKEMEASURES")
  private String takemeasures;
  
  @ApiModelProperty(value = "判决或仲裁结果")
  @TableField("JUDGMENTRESULT")
  private String judgmentresult;
  
  
  
  @Transient
  private List<TblAttachment> attList;

  @Transient
  private TblLegalDisputregistration dispute;

//  @Transient
//  private String disputeitem;//隶属纠纷

  @Transient
  private String startdate;
  @Transient
  private String enddate;

  //映射TblLegalProceedingsrecord

  @Transient
  private Integer proceedid;
  @Transient//案号
  private String proceedno;
  @Transient//诉讼阶段
  private String porceedstage;
  @Transient//审理法院
  private String court;
  @Transient
  private String courtlink;
  @Transient
  private String courtcontact;
  @Transient
  @JSONField(format = "yyyy-MM-dd")
  private Date filingtime;
  @Transient
  @JSONField(format = "yyyy-MM-dd")
  private Date paymentremindtime;
  @Transient
  @JSONField(format = "yyyy-MM-dd")
  private Date openingtime;
  @Transient
  @JSONField(format = "yyyy-MM-dd")
  private Date judgetiem;
  @Transient
  private BigDecimal isexternallawyer;
  @Transient
  private String lawyearword;
  @Transient
  private String lawyearname;
  @Transient
  private String lawyearlink;
  @Transient
  private BigDecimal litigationinfo;
  @Transient
  private String negotiator;
  @Transient
  private String negotiatorlink;
  @Transient
  private String presedingjudge;
  @Transient
  private String casepromotion;
  @Transient
  private String existingdifficulties;
  @Transient
  private String measurespromote;


  //TblLegalDisputregistration
//  @Transient
//  private Integer disputeid;
  @Transient
  private String disputeno;//登记编号
//  @Transient
//  private BigDecimal disputestatus;
//  @Transient//纠纷类型
//  private String disputetype;
  @Transient
  private BigDecimal contractinfo;
  @Transient//争议焦点
  private String disputecours;
  @Transient//是否紧急：1是，2否
  private BigDecimal isuegent;
  @Transient//起诉类型：1起诉，2被诉
  private BigDecimal whethersued;
  @Transient
  private BigDecimal disputeundertaker;
  @Transient//办结时间
  @JSONField(format = "yyyy-MM-dd")
  @DateTimeFormat(pattern="yyyy-MM-dd")
  private Date lastdealdate;
  @Transient
  private String solutionsuggestions;//初步解决建议
  @Transient//原告
  private String plaintiff;
  @Transient//被告
  private String defendant;
  @Transient//代理律师
  private String attorney;
  @Transient//代理律师联系电话
  private String attorneyphont;
  @Transient
  private BigDecimal dispuinfo;
  @Transient
  private String disputeitem;//纠纷主题
//  @Transient//是否聘请律师：1是，2否
//  private BigDecimal isattorney;

  @ApiModelProperty(value = "贵安配合人员")
  @Transient
  private String coordination;

  @ApiModelProperty(value = "贵安纠纷登记-案由")
  @Transient
  private String casecause;

  @ApiModelProperty(value = "贵安纠纷登记-涉诉项目")
  @Transient
  private String ssproject;

  @ApiModelProperty(value = "贵安纠纷登记-管辖法院（仲裁机构）")
  @Transient
  private String courtfirst;



  @ApiModelProperty(value = "贵安纠纷登记-发生时间")
  @Transient
  @JSONField(format = "yyyy-MM-dd")
  @DateTimeFormat(pattern="yyyy-MM-dd")
  private Date occurrencetime;

  @ApiModelProperty(value = "贵安纠纷登记-涉及子公司")
  @Transient
  private String subsidiaries;

}
