package com.huabo.compliance.vo.param;

import com.huabo.compliance.util.PageableParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TblComplianceRiskQueryParam extends PageableParam implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "事件编号")
	private String riskNumber;

	@ApiModelProperty(value = "事件名称")
	private String riskName;

	@ApiModelProperty(value = "创建人", hidden = true)
	private Integer creator;

	@ApiModelProperty(value = "工作单位", hidden = true)
	private Integer workUnit;

	@ApiModelProperty(value = "所属集团", hidden = true)
	private Integer belongGroup;

	@ApiModelProperty("是否管理员 0-否 1-是")
	private Integer authorityType;
}
