package com.huabo.audit.oracle.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;

/**
 * <AUTHOR>
 * @ClassName RequireSuggestionEntity
 * @Description
 * @DATE 2023/9/6
 */
@Data
@TableName("TBL_YQNS_REQUIRE_SUGGESTION")
@ApiModel("需求建议实体")
@Accessors(chain = true)
public class RequireSuggestionEntity implements Serializable {

    @TableId(value="ID", type= IdType.AUTO)
    @ApiModelProperty("ID")
    private BigDecimal id;

    @TableField(value="CONCERNS")
    @ApiModelProperty("重点关注领域、项目事项和风险")
    private String concerns;

    @TableField(value="CONCERNS_CONTENT")
    @ApiModelProperty("关注内容")
    private String concernsContent;

    @TableField(value="UNIT_ID")
    @ApiModelProperty("单位")
    private String organizationId;

	@TableField(exist = false)
	private TblOrganization organization;

    @TableField(value="PROJECT_TYPE")
    @ApiModelProperty("项目类型")
    private String projectType;

    @TableField(value="DRAFT_Id")
    @ApiModelProperty("底稿编号")
    private Integer draftId;

    @TableField(value="PERSON_IDS")
    @ApiModelProperty("下发的人员ID")
    private String personIds;

    @TableField(value="REMARK")
    @ApiModelProperty("备注")
    private String remark;

    @TableField(value="CREATE_USER")
    @ApiModelProperty("创建人")
    private TblStaff createUser;

    @TableField(value="CREATE_TIME")
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;


}
