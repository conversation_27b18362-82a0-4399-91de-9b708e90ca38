package com.huabo.system.oracle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hbfk.util.PageInfo;
import com.huabo.system.oracle.entity.TblCourse;
import com.huabo.system.oracle.entity.TblVideoType;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface ScheduledTaskMapper{

	@Select("SELECT T1.DISPATCHSTAFF AS DISPATCHSTAFF,T1.PROJECTID AS PROJECTID,T1.CONTRACTID AS CONTRACTID,T1.CONTRACTNO AS CONTRACTNO,T1.CONTRACTNAME AS CONTRACTNAME,T1.CONTRACTMONEY AS CONTRACTMONEY,T1.DCTYPE AS DCTYPE,TRUNC(SYSDATE-T1.PLANENDDATE) AS DIFFDAY FROM (SELECT TCP.DISPATCHSTAFF,TCP.PROJECTID,MAX(PLANENDDATE) AS PLANENDDATE,TCU.CONTRACTID,TCU.CONTRACTNO,TCU.CONTRACTNAME,TCU.CONTRACTMONEY,TCU.DCTYPE FROM TBL_CONTRACT_PLANNODE TCP LEFT JOIN TBL_CYHW_UNIT TCU ON TCP.PROJECTID = TCU.CONTRACTID WHERE (TCP.PLANNODESTATUS != 2 OR TCP.PLANNODESTATUS IS NULL) GROUP BY TCP.DISPATCHSTAFF,TCP.PROJECTID,TCU.CONTRACTID,TCU.CONTRACTNO,TCU.CONTRACTNAME,TCU.CONTRACTMONEY,TCU.DCTYPE ) T1 WHERE ( TO_CHAR(T1.PLANENDDATE+3,'yyyy-mm-dd') = TO_CHAR(SYSDATE, 'yyyy-mm-dd') OR TO_CHAR(T1.PLANENDDATE+7,'yyyy-mm-dd') = TO_CHAR(SYSDATE, 'yyyy-mm-dd')  OR TO_CHAR(T1.PLANENDDATE+15,'yyyy-mm-dd') <= TO_CHAR(SYSDATE, 'yyyy-mm-dd')) ORDER BY T1.DISPATCHSTAFF,T1.PLANENDDATE DESC")
	List<Map<String, Object>> selectContractPlanNodeTimeout() throws Exception;

	List<Map<String, Object>> selectContractPlanNodePaymentTimeout() throws Exception;



}
