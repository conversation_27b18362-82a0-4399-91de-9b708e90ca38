package com.huabo.system.service.impl;


import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblStaffUtil;
import com.hbfk.util.PageInfo;
import com.huabo.system.config.DateBaseConfig;
import com.huabo.system.mysql.entity.TblAcctBookMySql;
import com.huabo.system.mysql.entity.TblAcquisitionRecordMySql;
import com.huabo.system.mysql.mapper.TblAccBookMySqlMapper;
import com.huabo.system.mysql.mapper.TblAcquisitionRecordMySqlMapper;
import com.huabo.system.oracle.entity.TblAcctBook;
import com.huabo.system.oracle.entity.TblAcquisitionRecord;
import com.huabo.system.oracle.mapper.TblAccBookMapper;
import com.huabo.system.oracle.mapper.TblAcquisitionRecordMapper;
import com.huabo.system.service.TblAcquisitionRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TblAcquisitionRecordServiceImpl implements TblAcquisitionRecordService {

    @Resource
    private TblAccBookMapper accBookMapper;

    @Resource
    private TblAccBookMySqlMapper accBookMySqlMapper;

    @Resource
    private TblAcquisitionRecordMapper tblAcquisitionRecordMapper;

    @Resource
    private TblAcquisitionRecordMySqlMapper tblAcquisitionRecordMySqlMapper;

    @Override
    public Map<String, Object> findByPage(Integer pageNumber, Integer pageSize, String token, String staffId) {
        Map<String, Object> resultMap = new HashMap<String, Object>(0);
        try {
            TblStaffUtil staff = DealUserToken.parseUserToken(token);

            BigDecimal orgid = staff.getCurrentOrg().getOrgid();


            if (DateBaseConfig.DATABASETYPE.equals("Oracle")) {
                List<Integer> yearList = accBookMapper.findAcquisitionYear(orgid);
                //List<Integer> yearList1 = selectAcquisitionYear(yearList);
                yearList.remove(yearList.size() - 1);
                String ztname = accBookMapper.findAcquisitoionName(orgid);
                List<TblAcctBook> accBookList = accBookMapper.findAccBookByOrgId(orgid);
                PageInfo<TblAcquisitionRecord> pageInfo = new PageInfo<TblAcquisitionRecord>();
                pageInfo.setCurrentPage(pageNumber);
                pageInfo.setPageSize(pageSize);
                pageInfo.setTlist(tblAcquisitionRecordMapper.selectListByPageInfo(pageInfo, orgid));
                pageInfo.setTotalRecord(tblAcquisitionRecordMapper.selectCountByPageInfo(orgid));
                Map<String, Object> dataMap = new HashMap<String, Object>(0);
                dataMap.put("pageInfo", pageInfo);
                dataMap.put("yearList", yearList);
                dataMap.put("ztname", ztname);
                dataMap.put("accBookList", accBookList);
                resultMap.put("data", dataMap);
            } else {

                List<Integer> yearList = accBookMySqlMapper.findAcquisitionYear(orgid);
                //List<Integer> yearList1 = selectAcquisitionYear(yearList);
                yearList.remove(yearList.size() - 1);
                String ztname = accBookMySqlMapper.findAcquisitoionName(orgid);
                List<TblAcctBookMySql> accBookList = accBookMySqlMapper.findAccBookByOrgId(orgid);
                PageInfo<TblAcquisitionRecordMySql> pageInfo = new PageInfo<TblAcquisitionRecordMySql>();
                pageInfo.setCurrentPage(pageNumber);
                pageInfo.setPageSize(pageSize);
                pageInfo.setTlist(tblAcquisitionRecordMySqlMapper.selectListByPageInfo(pageInfo, orgid));
                pageInfo.setTotalRecord(tblAcquisitionRecordMySqlMapper.selectCountByPageInfo(orgid));
                Map<String, Object> dataMap = new HashMap<String, Object>(0);
                dataMap.put("pageInfo", pageInfo);
                dataMap.put("yearList", yearList);
                dataMap.put("ztname", ztname);
                dataMap.put("accBookList", accBookList);
                resultMap.put("data", dataMap);


            }
            resultMap.put("code", "1");
            resultMap.put("msg", "数据访问成功");


        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultMap;
    }

//	private List<Integer> selectAcquisitionYear(List<Integer> yearList) {
//		try {
//
//			List<Integer> yearList1 = new ArrayList(0);
//			Object[] objs = null;
//			Iterator var7 = yearList.iterator();
//
//			label58:
//			while(true) {
//				if (var7.hasNext()) {
//					Object next = var7.next();
//					objs = (Object[])((Object[])obj);
//					int i = Integer.parseInt(objs[0].toString());
//
//					while(true) {
//						if (i > Integer.parseInt(objs[1].toString())) {
//							continue label58;
//						}
//
//						if (!yearList1.contains(i)) {
//							yearList1.add(i);
//						}
//
//						++i;
//					}
//				}
//				return yearList1;
//			}
//		} finally {
//
//		}
//	}
}

