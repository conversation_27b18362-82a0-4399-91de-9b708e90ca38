package com.huabo.audit.controller;



import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.hbfk.util.JsonBean;
import com.hbfk.util.ResponseFormat;
import com.huabo.audit.service.ExpectLeaveService;
import com.huabo.audit.service.InterimAuditDetailService;


@RestController
@Api(tags = {"最近一次审计情况-所有接口"})
@Slf4j
public class AuditTjsjController {
	
	@Autowired
    private ExpectLeaveService expectLeaveService;
	
	@Autowired
    private InterimAuditDetailService interimAuditDetailService;
	
	@GetMapping("/wwtsj/getList")
    @ApiOperation("未委托及预计离任列表数据")
    public JsonBean getList1(HttpServletRequest request,
                            @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token")String token,
                            @ApiParam(name="pageNumber",value="分页当前页数",required=false)@RequestParam(value = "pageNumber", required = false, defaultValue = "1") Integer pageNumber,
                            @ApiParam(name="pageSize",value="每页记录数",required=false)@RequestParam(value = "pageSize", required = false, defaultValue = "15") Integer pageSize,
                            @ApiParam(name="ids",value="ids",required = false)@RequestParam(value = "ids", required = false, defaultValue = "") String ids,
                            @ApiParam(name="name",value="姓名",required = false)@RequestParam(value = "orgName", required = false, defaultValue = "") String name,
                            @ApiParam(name="projectName",value="项目名称",required = false)@RequestParam(value = "projectName", required = false, defaultValue = "") String projectName,
                            @ApiParam(name="teamLeader",value="组长",required = false)@RequestParam(value = "teamLeader", required = false, defaultValue = "") String teamLeader
    ){
        JsonBean jsonBean = null;
        try{

            jsonBean = expectLeaveService.findAll(token,pageNumber,pageSize,name,teamLeader,projectName,ids);
        }catch (Exception e){
            e.printStackTrace();
            jsonBean= ResponseFormat.retParam(0,e.getMessage(),null);
        }
        return jsonBean;
    }
	
	
	@GetMapping("/ywtsj/getList")
    @ApiOperation("已委托未实施列表")
    public JsonBean getList(HttpServletRequest request,
                            @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token")String token,
                            @ApiParam(name="pageNumber",value="分页当前页数",required=false)@RequestParam(value = "pageNumber", required = false, defaultValue = "1") Integer pageNumber,
                            @ApiParam(name="pageSize",value="每页记录数",required=false)@RequestParam(value = "pageSize", required = false, defaultValue = "15") Integer pageSize,
                            @ApiParam(name="ids",value="ids",required = false)@RequestParam(value = "ids", required = false, defaultValue = "") String ids,
                            @ApiParam(name="orgName",value="单位名称",required = false)@RequestParam(value = "orgName", required = false, defaultValue = "") String orgName,
                            @ApiParam(name="projectName",value="项目名称",required = false)@RequestParam(value = "projectName", required = false, defaultValue = "") String projectName,
                            @ApiParam(name="teamLeaderId",value="组长",required = false)@RequestParam(value = "teamLeaderId", required = false, defaultValue = "") String teamLeaderId,
                            @ApiParam(name="createyear",value="年度",required = false)@RequestParam(value = "createyear", required = false, defaultValue = "") String createyear

                            ){
        JsonBean jsonBean = null;
        try{

            jsonBean = interimAuditDetailService.findAll(token,pageNumber,pageSize, orgName,teamLeaderId,projectName,createyear,null,ids);
        }catch (Exception e){
            e.printStackTrace();
            jsonBean= ResponseFormat.retParam(0,e.getMessage(),null);
        }
        return jsonBean;
    }

}
