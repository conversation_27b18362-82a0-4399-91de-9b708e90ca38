package com.huabo.contract.mysql.mapper;

import com.hbfk.util.PageInfo;
import com.huabo.contract.mysql.entity.TblContractPaymentMySql;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.huabo.contract.mysql.entity.TblCyhwUnitMySql;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-15
 */
public interface TblContractPaymentMySqlMapper extends BaseMapper<TblContractPaymentMySql> {

    @SelectProvider(type=TblContractPaymentMySqlMapperSqlConfig.class,method="selectPaymentManagemenByPageInfo")
    List<TblContractPaymentMySql> selectPaymentManagemenByPageInfo(PageInfo<TblContractPaymentMySql> pageInfo, String orgid, TblContractPaymentMySql payment, TblCyhwUnitMySql unit);

    @SelectProvider(type=TblContractPaymentMySqlMapperSqlConfig.class,method="findPaymentManagemenByPageInfoCount")
    Integer findPaymentManagemenByPageInfoCount(String orgid, TblContractPaymentMySql payment, TblCyhwUnitMySql unit);

    @Select("SELECT * FROM TBL_CONTRACT_PAYMENT TCP\n" +
            "LEFT JOIN TBL_ORGANIZATION TOR ON TCP.LINKORG = TOR.ORGID\n" +
            "LEFT JOIN TBL_CYHW_UNIT TCU ON TCP.CONTRACTID = TCU.CONTRACTID\n" +
            "LEFT JOIN TBL_ORG_BANKACCOUNT TOB ON TCP.ORGBANK = TOB.BANKID\n" +
            "LEFT JOIN TBL_COUNTERPART_BANKINFO TCB ON TCP.COUNTERBANK = TCB.BANKID\n" +
            "LEFT JOIN TBL_CONTRACT_PLANNODE TCPL ON TCP.NODEID = TCPL.NODEID\n" +
            "LEFT JOIN TBL_CONTRACT_INVOICESMANAGEMEN TCI ON TCP.COUNTERBANK = TCI.INVOICEID\n" +
            "WHERE PAYMENTID = #{paymentid}")
    @Options(useGeneratedKeys=true, keyProperty="paymentid", keyColumn="PAYMENTID")
    Object selectPaymentid(BigDecimal paymentid);


    @UpdateProvider(type=TblContractPaymentMySqlMapperSqlConfig.class,method="updatePaymentInfo")
    void updatePaymentInfo(TblContractPaymentMySql oldPayment);

    @InsertProvider(type=TblContractPaymentMySqlMapperSqlConfig.class,method="savePaymenInfo")
    @Options(useGeneratedKeys=true, keyProperty="paymentid", keyColumn="PAYMENTID")
    void savePaymenInfo(TblContractPaymentMySql payment);

    @Select("SELECT TCP.PAYMENTID,TCP.PAYMENTTITLE,TCP.APPLYDATE,TCP.PAYMENTRECORD,TCP.PAYMENTTYPE,TCP.PAYMENTLATEDATE,TCP.PAYMENTMEMO,APS.STAFFID AS APSSTAFFID,APS.REALNAME AS APSREALNAME,APO.ORGID AS APOORGID,APO.ORGNAME AS APOORGNAME,TCPL.NODEID,TCPL.NODECONTENT,TCPL.NODEPOST, TCPL.NODEMONEY nodemoney,TCPB.BUDGETID,TCPB.COUNTERPARTNO,TCPB.BUDGETNAME,TCPB.COUNTERPARTHANK,TCPB.COUNTERPARTHANKACCOUNT,TCPB.PROJECTSTAGEGOAL,TCPB.COUNTERPARTPHONE,TCU.CONTRACTID,TCU.CONTRACTNAME,TCU.CONTRACTNO, TCU.CONTRACTMONEY,TCS.REALNAME," +
            " (SELECT SUM(PAYMENMONEY) FROM TBL_CONTRACT_PAYMENT WHERE CONTRACTID = TCU.CONTRACTID AND PAYMENTID !=  #{paymentId}" +	
            " AND PAYMENTSTATUS = 6) AS PAYMONEY,TCPL.NODEPOST*TCU.CONTRACTMONEY/100 AS FKMONEY,TCB.BANKID bankbankid,TCB.BANKACCOUNT,TOB.BANKID bankid,TOB.BANKACCNUM,TCI.INVOICEID,TCI.INVOICENO,TCI.INVOICESPORG,TCI.INVOICEMONEY invoicemoney,TCI.INVOICETYPE,TCI.INVOICEDATE,TCI.INVOICESTATUS,TCP.PAYMENTSTATUS " +
            "FROM TBL_CONTRACT_PAYMENT TCP LEFT JOIN TBL_STAFF APS ON TCP.APPLYSTAFF = APS.STAFFID LEFT JOIN TBL_ORGANIZATION APO ON TCP.APPLYORG = APO.ORGID LEFT JOIN TBL_CONTRACT_PLANNODE TCPL ON TCP.NODEID = TCPL.NODEID LEFT JOIN TBL_CYHW_PROJECTBUDGET TCPB ON TCP.BUDGETID = TCPB.BUDGETID LEFT JOIN TBL_CYHW_UNIT TCU ON TCP.CONTRACTID = TCU.CONTRACTID LEFT JOIN TBL_STAFF TCS ON TCS.STAFFID = TCU.CONTRACTSTAFF LEFT JOIN TBL_COUNTERPART_BANKINFO TCB ON TCB.BANKID = TCP.COUNTERBANK LEFT JOIN TBL_ORG_BANKACCOUNT TOB ON TOB.BANKID = TCP.ORGBANK LEFT JOIN TBL_CONTRACT_INVOICESMANAGEMEN TCI ON TCI.INVOICEID = TCP.INVOICEID WHERE PAYMENTID = #{paymentId}")
    TblContractPaymentMySql findPaymentInfoByParmentId(Integer paymentId);

    @Delete("DELETE FROM TBL_CONTRACT_PAYMENT WHERE PAYMENTID = #{paymentid}")
    void deletePaymentId(Integer paymentId);

    @Select("SELECT TCP.PAYMENTID,TCP.PAYMENMONEY,TCP.PAYMENTTITLE,TCP.PAYMENTLATEDATE,TCP.PAYMENTTYPE,TCPB.BUDGETNAME FROM TBL_CONTRACT_PAYMENT TCP LEFT JOIN TBL_CYHW_PROJECTBUDGET TCPB ON TCP.BUDGETID = TCPB.BUDGETID WHERE CONTRACTID = #{contractid} ")
    List<TblContractPaymentMySql> findpaymentListByContractId(BigDecimal contractid);

    @Select("SELECT * FROM TBL_CONTRACT_PAYMENT WHERE PAYMENTID = #{parmentId}")
    TblContractPaymentMySql findByParmentId(BigDecimal parmentId);
}
