package com.huabo.audit.service.impl;

import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblStaffUtil;
import com.hbfk.util.JsonBean;
import com.hbfk.util.PageInfo;
import com.hbfk.util.ResponseFormat;
import com.hbfk.util.StringUtil;
import com.huabo.audit.oracle.entity.AuditProjectZkEntity;
import com.huabo.audit.oracle.entity.TblNbsjProject;
import com.huabo.audit.oracle.mapper.AuditProjectZkMapper;
import com.huabo.audit.service.AuditProjectZkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName  AuditProjectZkServiceImpl
 * @Description
 * @DATE 2023/10/17
 */
@Service
public class AuditProjectZkServiceImpl implements  AuditProjectZkService {

    @Autowired
    private  AuditProjectZkMapper auditProjectZkMapper;

    @Override
    public JsonBean findAll(String token, Integer pageNumber, Integer pageSize,String projectName, BigDecimal money) throws Exception {
        TblStaffUtil user = DealUserToken.parseUserToken(token);
        if(user == null) {
            return ResponseFormat.retParam(0,20006,null);
        }

        AuditProjectZkEntity auditProjectZkEntity = new  AuditProjectZkEntity();

        if(money != null){
            auditProjectZkEntity.setMoney(money);
        }

        if(StringUtil.isNotEmpty(projectName)){
            auditProjectZkEntity.setProjectName(projectName);
        }


        PageInfo< AuditProjectZkEntity> pageInfo = new PageInfo< AuditProjectZkEntity>();
        pageInfo.setPageSize(pageSize);
        pageInfo.setCurrentPage(pageNumber);

        List< AuditProjectZkEntity> list = auditProjectZkMapper.selectByPageInfo(pageInfo,auditProjectZkEntity);

        Integer total = auditProjectZkMapper.selectCountByEntity(auditProjectZkEntity);

        pageInfo.setTlist(list);
        pageInfo.setTotalRecord(total);


        return ResponseFormat.retParam(1,200,pageInfo);
    }

    @Override
    public JsonBean findById(String id) throws Exception{
        AuditProjectZkEntity auditProjectZkEntity = auditProjectZkMapper.selectById(id);
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("data", auditProjectZkEntity);
        return ResponseFormat.retParam(1,200,resultMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEntity( AuditProjectZkEntity auditProjectZkEntity) throws Exception{
        auditProjectZkMapper.updateEntity(auditProjectZkEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveEntity(String token,  AuditProjectZkEntity auditProjectZkEntity) throws Exception{
        auditProjectZkMapper.insertEntity(auditProjectZkEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(String ids) throws Exception{
        auditProjectZkMapper.deleteEntity(ids);
    }


}
