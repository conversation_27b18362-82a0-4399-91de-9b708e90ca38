package com.huabo.compliance.vo.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserInfo {

	@ApiModelProperty("用户表主键ID")
	private Integer staffId;
	@ApiModelProperty("用户名称")
	private String realName;

	@ApiModelProperty("单位ID")
	private Integer WorkUnitId;
	@ApiModelProperty("单位名称")
	private String WorkUnitName;

	@ApiModelProperty(value = "所属集团ID")
	private Integer belongGroupId;
	@ApiModelProperty(value = "所属集团名称")
	private String belongGroupName;
}
