package com.huabo.system.entity;


import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("TBL_FLOW_OUTERRULE")
@ApiModel(value="TblFlowIOuterrule")
public class TblFlowIOuterrule {

    @TableField("OUTRULID")
    @ApiModelProperty("外规主键")
    private BigDecimal outrulid;
    
    @TableField("FLOWID")
    @ApiModelProperty("流程主键")
    private BigDecimal flowid;
}
