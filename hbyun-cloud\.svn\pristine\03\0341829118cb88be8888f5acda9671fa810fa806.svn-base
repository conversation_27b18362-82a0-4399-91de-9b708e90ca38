package com.huabo.system.oracle.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-27
 */
@Data
  @EqualsAndHashCode(callSuper = false)
    @TableName("TBL_HOME_PAGE_MODEL")
@ApiModel(value="TblHomePageModel对象", description="")
public class TblHomePageModel implements Serializable {

    private static final long serialVersionUID = 1L;

      @TableId("ID")
      private BigDecimal id;

    @TableField("NAME")
    private String name;

    @TableField("URL")
    private String url;

    @TableField("TYPE")
    private String type;

    @TableField("RIGHTID")
    private BigDecimal rightid;


}
