package com.huabo.contract.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblStaffUtil;
import com.huabo.contract.config.DateBaseConfig;
import com.huabo.contract.oracle.entity.TblBiPage;
import com.huabo.contract.oracle.entity.TblYyPrice;
import com.huabo.contract.oracle.entity.TblYyXdfTeam;
import com.huabo.contract.oracle.mapper.TblBiPageMapper;
import com.huabo.contract.oracle.mapper.TblYyPriceMapper;
import com.huabo.contract.oracle.mapper.TblYyXdfCompanyMapper;
import com.huabo.contract.oracle.mapper.TblYyXdfTeamMapper;
import com.huabo.contract.service.TblYyXdfTeamService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class TblYyXdfTeamServiceImpl implements TblYyXdfTeamService {

    @Resource
    private TblYyXdfTeamMapper tblYyXdfTeamMapper;

    @Resource
    private TblYyPriceMapper tblYyPriceMapper;
    @Resource
    private TblYyXdfCompanyMapper tblYyXdfCompanyMapper;
    @Resource
    private TblBiPageMapper tblBiPageMapper;

    @Override
    public Map<String, Object> saveOrupdateTeam(TblYyXdfTeam team, String token) {
        if(DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            try {
                TblStaffUtil staff = DealUserToken.parseUserToken(token);
                if (staff == null) {
                    resultMap.put("code", "0");
                    resultMap.put("msg", "用户已失效！");
                    return resultMap;
                }
                if (team.getTeamid() != null) {
                    tblYyXdfTeamMapper.updateTeam(team);
                } else {
                    team.setCompanyid(staff.getCurrentOrg().getOrgid());
                    team.setCreatedate(new Date());
                    team.setStaffid(staff.getStaffid());
                    tblYyXdfTeamMapper.insertTeam(team);
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
            resultMap.put("code", "1");
            resultMap.put("msg", "成功");
            resultMap.put("data", team);
            return resultMap;
        } else {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            try {
                TblStaffUtil staff = DealUserToken.parseUserToken(token);
                if (staff == null) {
                    resultMap.put("code", "0");
                    resultMap.put("msg", "用户已失效！");
                    return resultMap;
                }
                if (team.getTeamid() != null) {
                    tblYyXdfTeamMapper.updateTeam(team);
                } else {
                    team.setCompanyid(staff.getCurrentOrg().getOrgid());
                    team.setCreatedate(new Date());
                    team.setStaffid(staff.getStaffid());
                    tblYyXdfTeamMapper.insertTeam(team);
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
            resultMap.put("code", "1");
            resultMap.put("msg", "成功");
            resultMap.put("data", team);
            return resultMap;
        }
    }

    @Override
    public Map<String, Object> findBYuseridAndCompanid(String token) {
        if(DateBaseConfig.DATABASETYPE.equals("Oracle")) {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            Map<String, Object> dataMap = new HashMap<String, Object>(0);
            try {
                TblStaffUtil staff = DealUserToken.parseUserToken(token);
                if (staff == null) {
                    resultMap.put("code", "0");
                    resultMap.put("msg", "用户已失效！");
                    return resultMap;
                }
                List<TblYyPrice> pricelist = tblYyPriceMapper.findAll();
                List<TblBiPage> pageChilds = tblBiPageMapper.findByOrgid(staff.getStaffid(), staff.getLinkDetp().getOrgid());
                if (pageChilds != null && pageChilds.size() > 0) {
                    dataMap.put("pageChilds", pageChilds);
                }
                List<TblYyXdfTeam> list = this.tblYyXdfTeamMapper.listByOrgidAndStaffid(staff.getCurrentOrg().getOrgid(), staff.getStaffid());
                List<TblYyXdfTeam> newlist = new ArrayList();
                if (list != null && list.size() > 0) {
                    Iterator var6 = list.iterator();

                    while (var6.hasNext()) {
                        TblYyXdfTeam TblyyxdfTeam = (TblYyXdfTeam) var6.next();
                        Integer count = this.tblYyXdfCompanyMapper.listBySqlPageCount(TblyyxdfTeam.getTeamid());
                        TblyyxdfTeam.setCopanycount(count);
                        newlist.add(TblyyxdfTeam);
                    }
                }

                if (newlist != null && newlist.size() > 0) {
                    dataMap.put("teams", newlist);
                    dataMap.put("count", newlist.size());
                }
                dataMap.put("pricelist", pricelist);
            } catch (Exception e) {
                e.printStackTrace();
            }
            resultMap.put("code", "1");
            resultMap.put("msg", "成功");
            resultMap.put("data", dataMap);
            return resultMap;
        } else {
            Map<String, Object> resultMap = new HashMap<String, Object>(0);
            Map<String, Object> dataMap = new HashMap<String, Object>(0);
            try {
                TblStaffUtil staff = DealUserToken.parseUserToken(token);
                if (staff == null) {
                    resultMap.put("code", "0");
                    resultMap.put("msg", "用户已失效！");
                    return resultMap;
                }
                List<TblYyPrice> pricelist = tblYyPriceMapper.findAll();
                List<TblBiPage> pageChilds = tblBiPageMapper.findByOrgid(staff.getStaffid(), staff.getLinkDetp().getOrgid());
                if (pageChilds != null && pageChilds.size() > 0) {
                    dataMap.put("pageChilds", pageChilds);
                }
                List<TblYyXdfTeam> list = this.tblYyXdfTeamMapper.listByOrgidAndStaffid(staff.getCurrentOrg().getOrgid(), staff.getStaffid());
                List<TblYyXdfTeam> newlist = new ArrayList();
                if (list != null && list.size() > 0) {
                    Iterator var6 = list.iterator();

                    while (var6.hasNext()) {
                        TblYyXdfTeam TblyyxdfTeam = (TblYyXdfTeam) var6.next();
                        Integer count = this.tblYyXdfCompanyMapper.listBySqlPageCount(TblyyxdfTeam.getTeamid());
                        TblyyxdfTeam.setCopanycount(count);
                        newlist.add(TblyyxdfTeam);
                    }
                }

                if (newlist != null && newlist.size() > 0) {
                    dataMap.put("teams", newlist);
                    dataMap.put("count", newlist.size());
                }
                dataMap.put("pricelist", pricelist);
            } catch (Exception e) {
                e.printStackTrace();
            }
            resultMap.put("code", "1");
            resultMap.put("msg", "成功");
            resultMap.put("data", dataMap);
            return resultMap;
        }
    }

}
