package com.huabo.legal.exam.modules.user.book.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hbfk.entity.TblStaffUtil;
import com.hbfk.util.JsonBean;
import com.huabo.legal.exam.core.api.ApiRest;
import com.huabo.legal.exam.core.api.controller.BaseController;
import com.huabo.legal.exam.core.api.dto.BaseIdRespDTO;
import com.huabo.legal.exam.core.api.dto.BaseIdsReqDTO;
import com.huabo.legal.exam.core.api.dto.PagingReqDTO;
import com.huabo.legal.exam.modules.user.book.dto.UserBookDTO;
import com.huabo.legal.exam.modules.user.book.service.UserBookService;
import com.huabo.legal.exception.ServiceException;
import com.huabo.legal.util.LegalDealUserToken;
import com.huabo.legal.util.PageResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 错题本控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27 17:56
 */
@Api(tags = {"错题本"})
@RestController
@RequestMapping("/exam/api/user/wrong-book")
public class UserBookController extends BaseController {

	@Autowired
	private UserBookService baseService;


	/**
	 * 批量删除
	 * @param reqDTO
	 * @return
	 */
	@ApiOperation(value = "批量删除")
	@RequestMapping(value = "/delete", method = {RequestMethod.POST})
	public ApiRest delete(@RequestBody BaseIdsReqDTO reqDTO) {
		//根据ID删除
		baseService.removeByIds(reqDTO.getIds());
		return super.success();
	}

	
	@ApiOperation(value = "分页查找")
	@RequestMapping(value = "/paging", method = {RequestMethod.POST})
	public JsonBean paging(@RequestHeader("token") String token, @RequestBody PagingReqDTO<UserBookDTO> reqDTO) {
		TblStaffUtil tblStaffUtil = LegalDealUserToken.parseUserToken(token);

		JsonBean jsonBean = null;
		try {
			jsonBean = baseService.paging(reqDTO, tblStaffUtil.getStaffid().toString());
		} catch (ServiceException ex) {
			throw ex;
		} 
		return jsonBean;
	}

	/**
	 * 查找列表，每次最多返回200条数据
	 * @param reqDTO
	 * @return
	 */
	@ApiOperation(value = "查找列表")
	@RequestMapping(value = "/next", method = {RequestMethod.POST})
	public ApiRest<BaseIdRespDTO> nextQu(@RequestBody UserBookDTO reqDTO, @RequestHeader("token") String token) {
		TblStaffUtil tblStaffUtil = LegalDealUserToken.parseUserToken(token);
		//转换并返回
		String quId = baseService.findNext(reqDTO.getExamId(), reqDTO.getQuId(), tblStaffUtil.getStaffid().toString());
		return super.success(new BaseIdRespDTO(quId));
	}
}
