#server:
#  port: 8081
#spring:
#  application:
#    name: hbyunCyberMonitor #应用名称
#  datasource:
#    type: com.alibaba.druid.pool.DruidDataSource
#    #Oracle数据库配置
#    oracle:
#      url: ********************************************
#      username: HBGRCTEST
#      password: HBGRCTEST
#      driverClassName: oracle.jdbc.driver.OracleDriver
#  druid:
#    oracle:
#      initialSize: 5
#      minIdle: 5
#      maxActive: 20
#      maxWait: 60000
#      timeBetweenEvictionRunsMillis: 60000
#      minEvictableIdleTimeMillis: 300000
#      validationQuery: SELECT 1 FROM DUAL
#      testWhileIdle: true
#      testOnBorrow: false
#      testOnReturn: false
#      poolPreparedStatement: true
#      maxPoolPreparedStatementPerConnectionSize: 20
#      filters: stat,wall,log4j
#      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
#
#    #Mysql数据库配置
#    mysql:
#      url: ******************************************************************************************************************************************
#      username: ROOT
#      password: HBGRC
#      driverClassName: com.mysql.jdbc.Driver
#    druid:
#      mysql:
#        initialSize: 5
#        minIdle: 5
#        maxActive: 20
#        maxWait: 60000
#        timeBetweenEvictionRunsMillis: 60000
#        minEvictableIdleTimeMillis: 300000
#        validationQuery: SELECT 1 FROM DUAL
#        testWhileIdle: true
#        testOnBorrow: false
#        testOnReturn: false
#        poolPreparedStatement: true
#        maxPoolPreparedStatementPerConnectionSize: 20
#        filters: stat,wall,log4j
#        connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
#
##eureka服务注册中心配置
#eureka:
#  instance:
#    ip-address: 127.0.0.1
#    instance-id: hbyunContractModule-8064
#    prefer-ip-address: true
#    hostname: hbyun-contract-service
#  client:
#    serviceUrl:
#      defaultZone: **********************************************/eureka
#swagger:
#  enabled: true
#  pathMapping:
#logging:
#  level:
#    com:
#      huabo:
#        cybermonitor:
#          mapper: debug
#
#
##redis配置
#redisAddressIp: 127.0.0.1
#redisAddressPort: 6379
#redisPwd: 123456
#redisMaxTotal: 512
#redisMaxIdle: 100
#redisMaxWait: 10000
#redisTimeOut: 1000
#redisTextonBorrow: true
#
#
#mybatis:
# configuration:
#  map-underscore-to-camel-case: true
#
# #文件上传
# file-upload:
#  base-dir: D:\nginx-1.20.2\html\hbfk
#  #base-url: http://*************/hbfk
#  base-url: http://*************/hbfk