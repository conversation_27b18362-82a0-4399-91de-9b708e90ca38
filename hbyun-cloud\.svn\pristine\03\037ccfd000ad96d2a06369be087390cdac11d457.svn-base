package com.huabo.audit.controller;

import com.hbfk.util.JsonBean;
import com.hbfk.util.ResponseFormat;
import com.huabo.audit.oracle.entity.ExpectLeaveEntity;
import com.huabo.audit.service.ExpectLeaveService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @ClassName ExpectLeaveController
 * @Description 计划管理-未委托及预计离任
 * @DATE 2023/9/14
 */
@RestController
@Slf4j
@Api(value = "计划管理-未委托及预计离任", tags = {"计划管理-未委托及预计离任所有接口"})
@RequestMapping(value = "/plan/leave/expect")
public class ExpectLeaveController {
    @Autowired
    private ExpectLeaveService expectLeaveService;

    @GetMapping("/getList")
    @ApiOperation("未委托及预计离任列表")
    public JsonBean getList(HttpServletRequest request,
                            @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token")String token,
                            @ApiParam(name="pageNumber",value="分页当前页数",required=false)@RequestParam(value = "pageNumber", required = false, defaultValue = "1") Integer pageNumber,
                            @ApiParam(name="pageSize",value="每页记录数",required=false)@RequestParam(value = "pageSize", required = false, defaultValue = "15") Integer pageSize,
                            @ApiParam(name="name",value="姓名",required = false)@RequestParam(value = "name", required = false, defaultValue = "") String name,
                            @ApiParam(name="teamLeader",value="组长",required = false)@RequestParam(value = "teamLeader", required = false, defaultValue = "") String teamLeader
    ){
        JsonBean jsonBean = null;
        try{
            jsonBean = expectLeaveService.findAll(token,pageNumber,pageSize,name,teamLeader);
        }catch (Exception e){
            e.printStackTrace();
            jsonBean= ResponseFormat.retParam(0,1000,e.getMessage());
        }
        return jsonBean;
    }

    @GetMapping("/detail")
    @ApiOperation("根据ID获取详细信息")
    public JsonBean getById(HttpServletRequest request,@ApiParam(name="id",value="ID",required = false)@RequestParam(value = "id", required = false, defaultValue = "") String id){
        JsonBean jsonBean = null;
        try{
            jsonBean = expectLeaveService.findById(id);
        }catch (Exception e){
            e.printStackTrace();
            jsonBean= ResponseFormat.retParam(0,1000,e.getMessage());
        }
        return jsonBean;
    }

    @PostMapping(value="/saveOrUpdate" , produces = "application/json; charset=utf-8")
    @ApiOperation("添加或修改信息")
    public JsonBean saveOrUpdate(HttpServletRequest request,
                                 @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token")String token,
                                 @ApiParam(name="expectLeaveEntity", value="实体", required = true) @RequestBody ExpectLeaveEntity expectLeaveEntity
    ){
        JsonBean jsonBean = null;
        try{
            if(null != expectLeaveEntity.getId()){
                expectLeaveService.updateEntity(expectLeaveEntity);
            }else{
                expectLeaveService.saveEntity(token,expectLeaveEntity);
            }
            jsonBean= ResponseFormat.retParam(1,200);
        }catch (Exception e){
            e.printStackTrace();
            jsonBean= ResponseFormat.retParam(0,1000,e.getMessage());
        }
        return jsonBean;
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除")
    public JsonBean deleteByIds(HttpServletRequest request,
                                @ApiParam(name="token",value="登录用户token",required=true) @RequestHeader("token")String token,
                                @ApiParam(name="ids", value="Id,多个用,号隔开",required = true) @RequestParam String ids
    ){
        JsonBean jsonBean = null;
        try{
            expectLeaveService.deleteByIds(ids);
            jsonBean= ResponseFormat.retParam(1,200);
        }catch (Exception e){
            e.printStackTrace();
            jsonBean= ResponseFormat.retParam(0,1000,e.getMessage());
        }
        return jsonBean;
    }

}
