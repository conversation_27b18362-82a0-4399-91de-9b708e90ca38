package com.huabo.finance.vr;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 辅助账余额
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="GlAssBalane对象", description="辅助账余额")
public class GlAssBalaneVr implements Serializable {

    private static final long serialVersionUID = 1L;

      @ApiModelProperty(value = "辅助凭证主键")
      private String pkAssbalance;

      @ApiModelProperty(value = "所属集团")
    private String pkGroup;

      @ApiModelProperty(value = "所属公司 --必传")
    private String pkOrg;

      @ApiModelProperty(value = "科目主键")
    private String pkAccasoa;

      @ApiModelProperty(value = "会计年度")
    private String year;

      @ApiModelProperty(value = "会计期间")
    private String period;

      @ApiModelProperty(value = "辅助核算标识")
    private String pkAccass;

      @ApiModelProperty(value = "原币借发生额")
    private BigDecimal debitamount;

      @ApiModelProperty(value = "原币贷发生额")
    private BigDecimal creditmount;

      @ApiModelProperty(value = "初始余额")
    private BigDecimal beginbalancemount;

      @ApiModelProperty(value = "期末余额")
    private BigDecimal endbalancemount;

      @ApiModelProperty(value = "本年累计借发生额")
    private BigDecimal yeardebitamount;

      @ApiModelProperty(value = "本年累计贷发生额")
    private BigDecimal yearcreditmount;

      @ApiModelProperty(value = "数据来源 -2系统同步 ")
    private String dataoriginflag;

      @ApiModelProperty(value = "所属采集方案")
    private String fplanid;
      
      
      @ApiModelProperty(value = "辅助类型")
    private String asstype;

      @ApiModelProperty(value = "辅助信息主键")
    private String pkBunessies;

      @ApiModelProperty(value = "辅助名称")
    private String assname;

      @ApiModelProperty(value = "描述")
    private String assdes;

    private String asslevel;

      @ApiModelProperty(value = "辅助核算项主键  -- 必传")
    private String pkAccassitem;

      @ApiModelProperty(value = "描述")
    private String assdd;
      
      @ApiModelProperty(value = "科目编号")
    private String code;
    
      @ApiModelProperty(value = "科目名称")
    private String name;
    
      @ApiModelProperty(value = "科目方向  0=借方;1=贷方;")
    private Integer balanorient;
      
      
      @ApiModelProperty(value = "期末方向  0=借方;1=贷方;")
      private Integer endBalanorient;
      
      
      @ApiModelProperty(value = "期初方向  0=借方;1=贷方;")
      private Integer beginBalanorient;
      
}
