spring:
 application:
  name: hbyunSystemSetting #应用名称
 datasource:
  type: com.alibaba.druid.pool.DruidDataSource
  url: jdbc:dm://10.200.20.128:5236/HBGRCZH?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
  username: HB<PERSON><PERSON>ZH
  password: HBGRCZH1101
  driverClassName: dm.jdbc.driver.DmDriver
 druid:
  initialSize: 5
  minIdle: 5
  maxActive: 20
  maxWait: 60000
  timeBetweenEvictionRunsMillis: 60000
  minEvictableIdleTimeMillis: 300000
  validationQuery: SELECT 1 FROM DUAL
  testWhileIdle: true
  testOnBorrow: false
  testOnReturn: false
  poolPreparedStatement: true
  maxPoolPreparedStatementPerConnectionSize: 20
  filters: stat,wall,log4j
  connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
 redis: 
  host: 127.0.0.1
  port: 6379
  password: zhRedis@135246

#日志配置
logging:
 level:
  org:
   springframework: WARN
   spring:
    springboot:
     dao: info
  com:
   dc:
    pcasweb:
     mapper: debug
 file: logs/spring-boot-logging.log
 
  # 文件保存目录
file:
 ca:
  secret: b8e9a1c7d4f265a830e7b1f4d8a9c6e2
 upload:
  path: E:\datafile
 previewUrl: http://************:8012/onlinePreview?url=
 downloadUrl: https://www.wenxindamoxing.com/api/file/file/download/decrypt?fileId=
 
#eureka服务注册中心配置
eureka:
 instance:
  ip-address: 127.0.0.1
  instance-id: hbyunSystemSetting-8763
  prefer-ip-address: true
  hostname: hbyun-systemSetting-service
 client:
  register-with-eureka: false
  fetch-registry: false
  serviceUrl:
   defaultZone: **********************************************/eureka
security:
  basic:
    enabled: false

#redis配置
redisAddressIp: 127.0.0.1
redisAddressPort: 6379
redisPwd: zhRedis@135246
redisMaxTotal: 512
redisMaxIdle: 100
redisMaxWait: 10000
redisTimeOut: 1000
redisTextonBorrow: true

acurl : http://127.0.0.1:8003
formurl : http://127.0.0.1:8001
activitiModelerUrl : http://127.0.0.1:8137
getOrgChildrenUrl : http://127.0.0.1:8379
insertOrgOneUrl : http://127.0.0.1:8379
redisurl : http://127.0.0.1:8379

mybatis:
 configuration:
  map-underscore-to-camel-case: true
# Swagger配置
swagger:
 enabled: true
 # 是否开启swagger
 # 请求前缀
 pathMapping:
application:
 #文件上传
 file-upload:
  base-dir: D:\nginx-1.20.2\html\hbfk
  #base-url: http://*************/hbfk
  base-url: http://*************/hbfk
 #图片上传前缀域名
 file-url: http://cn-gz-txy.starryfrp.com:64108
 #上帝视角用户名称
 admin-name: 张孝昆