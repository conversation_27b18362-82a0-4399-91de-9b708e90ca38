package com.huabo.compliance.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 描述: 
 * author: ziyao
 * date: 2022-04-19
 */
@TableName("TBL_NBSJ_TEAMSTAFF")
@Data
@ApiModel("实体类")
@Accessors(chain = true)
public class TblNbsjTeamstaffEntity {
	
	public static final Integer LEADER=0;
	public static final Integer CREW=1;
	
    @TableId(value = "id", type= IdType.AUTO)
    @ApiModelProperty(value="id")
    private Integer id;

    @TableField(value = "teamid")
    @ApiModelProperty(value="")
    private Integer teamid;

    @TableField(value = "staffid")
    @ApiModelProperty(value="")
    private Integer staffid;

    @TableField(value = "stafftype")
    @ApiModelProperty(value="")//0=组长 1=组员
    private Integer stafftype;
    
    @TableField(value = "staff")
    @ApiModelProperty(value="")
    private TblStaff staff;
    
    @TableField(value = "")
    @ApiModelProperty(value="")
    private String createUserName;
    
    @TableField(value = "")
    @ApiModelProperty(value="")
    private Date createTime;
    
    @TableField(value = "")
    @ApiModelProperty(value="")
    private String teamName;
}
