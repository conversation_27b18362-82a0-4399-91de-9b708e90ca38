package com.huabo.audit.oracle.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.incrementer.OracleKeyGenerator;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.context.annotation.Bean;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 计划管理计划草稿
 *
 * @TableName TBL_YQNS_JHGL_JHCG
 */
@TableName(value = "TBL_YQNS_JHGL_JHCG")
@Data
@KeySequence(value = "HIBERNATE_SEQUENCE")
public class TblYqnsJhglJhcg implements Serializable {

    @Bean
    public OracleKeyGenerator genkey() {
        return new OracleKeyGenerator();
    }

    /**
     * 计划草稿主键
     */
    @ApiModelProperty(value = "计划草稿主键")
    @TableId(value = "JHCGID", type = IdType.INPUT)
    private BigDecimal jhcgid;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "CJR")
    private String cjr;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "CJSJ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date cjsj;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    @TableField(value = "EXT1")
    private String ext1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    @TableField(value = "EXT2")
    private String ext2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    @TableField(value = "EXT3")
    private String ext3;

    /**
     * 组织主键
     */
    @ApiModelProperty(value = "组织主键")
    @TableField(value = "ORGID")
    private Long orgid;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @TableField(value = "GXR")
    private String gxr;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(value = "GXSJ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date gxsj;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态")
    @TableField(value = "SPZT")
    private Long spzt;

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    @TableField(value = "JHMC")
    private String jhmc;

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    @TableField(value = "SJ")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date sj;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField(value = "BZ")
    private String bz;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "开始时间")
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startDate;

    @ApiModelProperty(value = "结束时间")
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endDate;

    @ApiModelProperty(value = "主键集合")
    @TableField(exist = false)
    private List<String> ids;

    @ApiModelProperty(value = "附件主键集合")
    @TableField(exist = false)
    private List<String> attIds;

    @ApiModelProperty(value = "附件集合")
    @TableField(exist = false)
    private List<TblAttachment> attachments;


    /**
     * 项目类别：年度计划、新增计划
     */
    @ApiModelProperty(value = "项目类别")
    @TableField(value = "XMLB")
    private String xmlb;

    
    @ApiModelProperty(value = "计划管理计划草稿 关联表")
    @TableField(exist = false)
    private List<TblYqnsJhglJhcgGL> tblYqnsJhglJhcgGLList;

    @ApiModelProperty(value = "专项审计_生产经营管理专项审计")
    @TableField(exist = false)
    private List<TblYqnsJhglJhcgMx> mx11;

    @ApiModelProperty(value = "专项审计_基建与投资专项审计")
    @TableField(exist = false)
    private List<TblYqnsJhglJhcgMx> mx12;

    @ApiModelProperty(value = "经济责任审计_二级单位及所属成员单位离任经济责任审计")
    @TableField(exist = false)
    private List<LeaveAudit2LEntity> leaveAudit2LEntityList;

    @ApiModelProperty(value = "经济责任审计_二级单位任中经济责任审计")
    @TableField(exist = false)
    private List<AuditSuggestion2LEntity> auditSuggestion2LEntityList;

    @ApiModelProperty(value = "经济责任审计_三级单位离任经济责任审计")
    @TableField(exist = false)
    private List<LeaveAudit3LEntity> leaveAudit3LEntityList;

    /**
     * 工程建设项目审计_工程建设项目结算审计  指定  建设项目基本情况表
     */
    @ApiModelProperty(value = "工程建设项目审计_工程建设项目结算审计")
    @TableField(exist = false)
    private List<TblYqnsJsxmJbqk> tblYqnsJsxmJbqkList;

    /**
     * 工程建设项目审计_工程建设项目竣工决算审计  指定 工程项目竣工验收计划
     */
    @ApiModelProperty(value = "工程建设项目审计_工程建设项目竣工决算审计")
    @TableField(exist = false)
    private List<TblYqnsGcxmjgYsjh> tblYqnsGcxmjgYsjhList;

    @ApiModelProperty(value = "审计类型明细主键集合")
    @TableField(exist = false)
    private List<String> mxIds;
    ////////////////////////////////////////////////////////////
    @ApiModelProperty(value = "立项单位标识")
    @TableField(value = "LXDWID")
    private String lxdwid;


    @ApiModelProperty(value = "立项单位名称")
    @TableField(value = "LXDWMC")
    private String lxdwmc;


    @ApiModelProperty(value = "被审计单位标识")
    @TableField(value = "BSJDWID")
    private String bsjdwid;


    @ApiModelProperty(value = "被审计单位名称")
    @TableField(value = "BSJDWMC")
    private String bsjdwmc;


    @ApiModelProperty(value = "实施类型")
    @TableField(value = "SSLX")
    private String sslx;


    @ApiModelProperty(value = "境外项目")
    @TableField(value = "JWXM")
    private String jwxm;

    @ApiModelProperty(value = "实施审计机构标识")
    @TableField(value = "SSSJJGID")
    private String sssjjgid;

    @ApiModelProperty(value = "实施审计机构名称")
    @TableField(value = "SSSJJGMC")
    private String sssjjgmc;

    @ApiModelProperty(value = "项目年度")
    @TableField(value = "XMND")
    private String xmnd;

    @ApiModelProperty(value = "项目负责处(科)室标识")
    @TableField(value = "XMFZCKSID")
    private String xmfzcksid;

    @ApiModelProperty(value = "项目负责处(科)室标识名称")
    @TableField(value = "XMFZCKSMC")
    private String xmfzcksmc;

    @ApiModelProperty(value = "计划实施月份")
    @TableField(value = "JHSSYF")
    private String jhssyf;

    @ApiModelProperty(value = "审计项目类型")
    @TableField(value = "SSXMLX")
    private String ssxmlx;

    @ApiModelProperty(value = "立项依据")
    @TableField(value = "LXYJ")
    private String lxyj;

    @ApiModelProperty(value = "审计项目名称")
    @TableField(value = "SJXMMC")
    private String sjxmmc;

    @ApiModelProperty(value = "计划类型")
    @TableField(value = "JHLX")
    private String jhlx;

    @ApiModelProperty(value = "计划投入人日")
    @TableField(value = "JHTRRR")
    private String jhtrrr;

    @ApiModelProperty(value = "是否对全部经营活动审计")
    @TableField(value = "SFDQBJYHDSJ")
    private String sfdqbjyhdsj;

    @ApiModelProperty(value = "变更原因")
    @TableField(value = "BGYY")
    private String bgyy;


}