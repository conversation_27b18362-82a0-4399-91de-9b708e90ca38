package com.huabo.system.service.impl;


import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblStaffUtil;
import com.hbfk.util.JsonBean;
import com.hbfk.util.ResponseFormat;
import com.huabo.system.mysql.entity.TblAccBookMySql;
import com.huabo.system.mysql.mapper.TblAccBookMySqlMapper;
import com.huabo.system.oracle.entity.TblAccBook;
import com.huabo.system.oracle.mapper.TblAccBookMapper;
import com.huabo.system.service.TblAccBookService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TblAccBookServiceImpl implements TblAccBookService {

    @Resource
    private TblAccBookMapper tblAccBookMapper;

    @Resource
    private TblAccBookMySqlMapper tblAccBookMySqlMapper;


    @Override
    public List<TblAccBookMySql> findBookIdByMySqlUserAll(BigDecimal staffid, BigDecimal orgid) {
        return this.tblAccBookMySqlMapper.listBySql(staffid, orgid);
    }

    @Override
    public List<TblAccBook> findBookIdByUserAll(BigDecimal staffid, BigDecimal orgid) {
        //return this.tblAccBookMapper.listBySql(staffid,orgid);
        return this.tblAccBookMapper.listBySql(staffid, orgid);
    }

    @Override
    public TblAccBook findByBookIdOne(String connectionstrings) {
//    	 return tblAccBookMapper.findByBookIdOne(connectionstrings);
        return tblAccBookMapper.findByBookIdOne(connectionstrings);
    }

    @Override
    public TblAccBookMySql findByMySqlBookIdOne(String connectionstrings) {
        return tblAccBookMySqlMapper.findByBookIdOne(connectionstrings);
    }

	@Override
	public JsonBean findstaffid(String token) throws Exception {
		TblStaffUtil loginStaff = DealUserToken.parseUserToken(token);
		if(loginStaff == null) {
			return ResponseFormat.retParam(0,20006,null);
		}
		Map<String,Object> resultMap = new HashMap<String,Object>(0);
		resultMap.put("data",tblAccBookMapper.listBySqlstaffid(loginStaff.getStaffid()));
		return ResponseFormat.retParam(1,200,resultMap);
	}

}
