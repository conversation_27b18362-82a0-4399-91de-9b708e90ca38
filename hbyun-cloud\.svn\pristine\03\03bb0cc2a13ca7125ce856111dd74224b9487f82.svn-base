package com.huabo.know.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2024-04-03
 */
@Getter
@Setter
@TableName("TBL_ZSGX_LAW_REGULATION")
@ApiModel(value = "TblZsgxLawRegulation对象", description = "")
public class TblZsgxLawRegulation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableField("ID")
    private String id;

    @ApiModelProperty(value = "标题")
    @TableField("TITLE")
    private String title;

    @ApiModelProperty(value = "发文字号")
    @TableField("DOCUMENT_NO")
    private String documentNo;

    @ApiModelProperty(value = "全文")
    @TableField("CONTENT")
    private String content;

    @ApiModelProperty(value = "实施时间")
    @TableField("IMPLEMENT_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date implementDate;

    @ApiModelProperty(value = "发布时间")
    @TableField("ISSUE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date issueDate;

    @ApiModelProperty(value = "发布年份")
    @TableField("ISSUE_YEAR")
    private String issueYear;

    @ApiModelProperty(value = "发布部门code")
    @TableField("ISSUE_DEPARTMENT_CODE")
    private String issueDepartmentCode;

    @ApiModelProperty(value = "发布部门name")
    @TableField("ISSUE_DEPARTMENT_NAME")
    private String issueDepartmentName;

    @ApiModelProperty(value = "合同类型code")
    @TableField("CONTRACT_TYPE_CODE")
    private String contractTypeCode;

    @ApiModelProperty(value = "合同类型name")
    @TableField("CONTRACT_TYPE_NAME")
    private String contractTypeName;

    @ApiModelProperty(value = "效力级别code")
    @TableField("EFFECTIVENESS_DIC_CODE")
    private String effectivenessDicCode;

    @ApiModelProperty(value = "效力级别name")
    @TableField("EFFECTIVENESS_DIC_NAME")
    private String effectivenessDicName;

    @ApiModelProperty(value = "时效性code")
    @TableField("TIMELINESS_DIC_CODE")
    private String timelinessDicCode;

    @ApiModelProperty(value = "时效性name")
    @TableField("TIMELINESS_DIC_NAME")
    private String timelinessDicName;

    @ApiModelProperty(value = "创建公司")
    @TableField("CREATE_COMPANY")
    private String createCompany;

    @ApiModelProperty(value = "创建部门")
    @TableField("CREATE_DEPT")
    private String createDept;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATE_BY")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;

    @ApiModelProperty(value = "更新人")
    @TableField("UPDATE_BY")
    private String updateBy;

    @ApiModelProperty(value = "是否删除，0：未删除；1：已删除")
    @TableField("DELETED")
    private Integer deleted;


}
