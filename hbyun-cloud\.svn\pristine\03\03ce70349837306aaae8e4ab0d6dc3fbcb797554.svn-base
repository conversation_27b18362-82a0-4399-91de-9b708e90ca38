package com.huabo.legal.mysql.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 制度审核-制度表
 */
@ApiModel(value = "TblFwglInstitutionAuditExtMySql")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tbl_fwgl_institution_audit_ext")
public class TblFwglInstitutionAuditExtMySql implements Serializable {

	@Id
	@Column(name = "INSTITUTIONAUDITEXTID")
	@GeneratedValue(generator = "JDBC")
	@ApiModelProperty(value = "制度审核-制度ID")
	private Integer institutionAuditExtId;

	@Column(name = "INSTITUTIONNAME")
	@ApiModelProperty(value = "制度名称")
	private String institutionName;

	@Column(name = "INSTITUTIONTYPE")
	@ApiModelProperty(value = "制度分类（制度审核） 1-经营管理类-一般制度 2-经营管理类-基本制度 3-经营管理类-重要制度 4-非经营管理类")
	private Integer institutionType;

	@Column(name = "FILEIDS")
	@ApiModelProperty(value = "上传文件ids 多个逗号隔开")
	private String fileIds;

	@Column(name = "STATE")
	@ApiModelProperty(value = "状态", hidden = true)
	private Integer state;

	@Column(name = "CREATOR")
	@ApiModelProperty(value = "创建人", hidden = true)
	private String creator;

	@Column(name = "WORKUNIT")
	@ApiModelProperty(value = "工作单位", hidden = true)
	private String workUnit;

	@Column(name = "BELONGGROUP")
	@ApiModelProperty(value = "所属集团", hidden = true)
	private String belongGroup;

	@Column(name = "CREATEDTIME")
	@ApiModelProperty(value = "创建时间", hidden = true)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createdTime;

	@Column(name = "UPDATEDTIME")
	@ApiModelProperty(value = "更新时间", hidden = true)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updatedTime;

	@Column(name = "REMARK")
	@ApiModelProperty(value = "备注")
	private String remark;

	private static final long serialVersionUID = 1L;

	public static TblFwglInstitutionAuditExtMySql ofId(Integer id) {
		TblFwglInstitutionAuditExtMySql tblFwglInstitutionAuditExtMySql = new TblFwglInstitutionAuditExtMySql();
		tblFwglInstitutionAuditExtMySql.setInstitutionAuditExtId(id);
		return tblFwglInstitutionAuditExtMySql;
	}
}
