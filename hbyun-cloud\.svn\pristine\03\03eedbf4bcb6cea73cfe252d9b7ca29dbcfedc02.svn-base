package com.huabo.audit.oracle.vo;

import afu.org.checkerframework.checker.igj.qual.I;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.huabo.audit.util.BaseVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("缺陷管理列表查询入参")
public class TblNbsjBugVo extends BaseVo{
	
	@ApiModelProperty(value = "缺陷id")
    private BigDecimal bugid;
	
	@ApiModelProperty(value = "缺陷编号")	
	private String bugnumber;
	
	@ApiModelProperty(value = "缺陷级别")
    private String bugcriid;

    @ApiModelProperty(value = "发现开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startDate;
    
    @ApiModelProperty(value = "发现结束时间 ")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endDate;

    @ApiModelProperty(value = "缺陷级别")
    private BigDecimal companyid;

    @ApiModelProperty(value = "缺陷部门")
    private BigDecimal orgid;
    
    @ApiModelProperty("缺陷名称")
    private String defectsname;
    
    @ApiModelProperty(value = "是否使用密级 0 不使用；1 使用")
    private Integer useSecrect;
    
    @ApiModelProperty(value = "创建人-密级用")
    private BigDecimal secrectStaff;
    
    @ApiModelProperty(value = "当前用户所能查看密级数据的范围主键")
    private String secrectScopeIds;
    



}

