package com.huabo.legal.vo.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LegalCompanyLawyerResult {

	@ApiModelProperty(value = "数量")
	private Integer count;

	@ApiModelProperty(value = "所属集团名称")
	private String belongGroupName;

	@ApiModelProperty(value = "所属集团名称")
	private String belongGroup;
}
