package com.huabo.audit.oracle.entity;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.huabo.audit.config.IgnoreSwaggerParameter;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import tk.mybatis.mapper.annotation.KeySql;
import tk.mybatis.mapper.code.ORDER;

/**
 * 描述:
 * author: tj
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Table(name = "TBL_YQNS_RESULT")
@ApiModel(value="审计结果确认对象", description="")
public class TblYqnsResult {
	

    @ApiModelProperty(value = "主键")
    @TableId("RESULTID")
    @Id
    @KeySql(sql = "select HIBERNATE_SEQUENCE.nextval from dual", order= ORDER.DEFAULT)
    private Integer resultid;

    @TableField(value = "RESULTCODE")
    @Column(name = "RESULTCODE")
    @ApiModelProperty(value = "编号")
    private String resultcode;



    @TableField(value = "CREATESTAFF")
    @Column(name = "CREATESTAFF")
    @ApiModelProperty(value = "创建人id",hidden = true)
    private BigDecimal createstaff;

    @TableField(value = "CREATETIME")
    @Column(name = "CREATETIME")
    @ApiModelProperty(value = "创建时间",hidden = true)
    @IgnoreSwaggerParameter
    private Date createtime;

    @TableField(value = "PROJECTID")
    @Column(name = "PROJECTID")
    @ApiModelProperty(value = "所属项目id")
    private Integer projectid;


    @TableField(value = "STATUS")
    @Column(name = "STATUS")
    @ApiModelProperty(value = "审核状态 1 未审核;2 审核中;3 审核驳回;6 审核完成;5 需调整")
    private Integer status;


	@Column(name = "PROJECTNAME")
    @ApiModelProperty(value = "项目名称")
    private String projectname;

    
    @TableField(exist = false)
    @ApiModelProperty(value = "拟稿人",hidden = true)
    private String realname;
  
    
    @TableField(value = "ORGIDS")
    @Column(name = "ORGIDS")
	@ApiModelProperty(value="被审计对象id")
	private String orgids;

	@TableField(value = "ORGIDNAMES")
	@Column(name = "ORGIDNAMES")
	@ApiModelProperty(value="被审计对象名称")
	private String orgidnames;
	
	
	@TableField(value = "CONTRACTCODE")
	@Column(name = "CONTRACTCODE")
	@ApiModelProperty(value="合同编号")
	private String contractcode; 
	
	@TableField(value = "CONTRACTNAME")
	@Column(name = "CONTRACTNAME")
	@ApiModelProperty(value="合同名称")
	private String contractname; 
	
	@TableField(value = "CONTRACTMONEY")
	@Column(name = "CONTRACTMONEY")
	@ApiModelProperty(value="合同金额")
	private String contractmoney; 

	@TableField(value = "SGORGID")
	@Column(name = "SGORGID")
	@ApiModelProperty(value="施工单位id")
	private String sgorgid; 
	
	@TableField(value = "SGORGNAME")
	@Column(name = "SGORGNAME")
	@ApiModelProperty(value="施工单位名称")
	private String sgorgname; 
	
	
	@TableField(value = "HZMONEY")
	@Column(name = "HZMONEY")
	@ApiModelProperty(value="核增金额")
	private String hzmoney; 
	
	@TableField(value = "HJMONEY")
	@Column(name = "HJMONEY")
	@ApiModelProperty(value="核减金额")
	private String hjmoney; 
	
	
	@TableField(value = "SDMONEY")
	@Column(name = "SDMONEY")
	@ApiModelProperty(value="审计认定金额")
	private String sdmoney; 
}
