package com.huabo.monitor.entity;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-09
 */
@TableName("TBL_TESTTASK_ATT")
@ApiModel(value = "TblTesttaskAtt对象", description = "")
public class TblTesttaskAtt implements Serializable {

    private static final long serialVersionUID = 1L;

    private BigDecimal attid;

    private BigDecimal testtaskid;

    public BigDecimal getAttid() {
        return attid;
    }

    public void setAttid(BigDecimal attid) {
        this.attid = attid;
    }
    public BigDecimal getTesttaskid() {
        return testtaskid;
    }

    public void setTesttaskid(BigDecimal testtaskid) {
        this.testtaskid = testtaskid;
    }

    @Override
    public String toString() {
        return "TblTesttaskAtt{" +
            "attid=" + attid +
            ", testtaskid=" + testtaskid +
        "}";
    }
}
