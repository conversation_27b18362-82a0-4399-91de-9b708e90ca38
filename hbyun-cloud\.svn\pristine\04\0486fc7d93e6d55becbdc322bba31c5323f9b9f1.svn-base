package com.huabo.contract.oracle.mapper;

import com.hbfk.util.PageInfo;
import com.huabo.contract.oracle.entity.TblCounterpartBankinfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-14
 */
public interface TblCounterpartBankinfoMapper extends BaseMapper<TblCounterpartBankinfo> {

    @SelectProvider(type=TblCounterpartBankinfoMapperSqlConfig.class,method="findListByPageInfo")
    List<TblCounterpartBankinfo> findListByPageInfo(PageInfo<TblCounterpartBankinfo> pageInfo, TblCounterpartBankinfo bank);

    @SelectProvider(type=TblCounterpartBankinfoMapperSqlConfig.class,method="selectCountByPageInfoCount")
    Integer selectCountByPageInfoCount(TblCounterpartBankinfo bank);

    @InsertProvider(type=TblCounterpartBankinfoMapperSqlConfig.class,method="saveBank")
    @Options(useGeneratedKeys=true, keyProperty="bankid", keyColumn="BANKID")
    void saveBank(TblCounterpartBankinfo bank);

    @SelectProvider(type=TblCounterpartBankinfoMapperSqlConfig.class,method="findAllListByBankInfo")
    List<TblCounterpartBankinfo> findAllListByBankInfo(PageInfo<TblCounterpartBankinfo> pageInfo);
    @SelectProvider(type=TblCounterpartBankinfoMapperSqlConfig.class,method="findCountListByBankInfo")
    Integer findCountListByBankInfo(PageInfo<TblCounterpartBankinfo> pageInfo);

    @UpdateProvider(type=TblCounterpartBankinfoMapperSqlConfig.class,method="updateByBankId")
    void updateByBankId(TblCounterpartBankinfo bank);

    @Delete("DELETE FROM TBL_COUNTERPART_BANKINFO WHERE BANKID = #{bankId}")
    void removeBank(String bankId);

    @Update("UPDATE TBL_COUNTERPART_BANKINFO SET BANKSTATUS = #{bankstatus} WHERE BANKID = #{bankId}")
    void modifyBankStatus(String bankId, Integer bankstatus);

    @Select("SELECT * FROM TBL_COUNTERPART_BANKINFO WHERE BUDGETID = #{budgetId}")
    List<TblCounterpartBankinfo> selectBankInfoByBugetId(Integer budgetId);

    @Select("SELECT * FROM TBL_COUNTERPART_BANKINFO WHERE BUDGETID = #{budgetId} and BANKACCOUNT=#{BANKACCOUNT}")
    TblCounterpartBankinfo  selectBankInfoById(String budgetId,String BANKACCOUNT);

    @Delete("DELETE FROM TBL_COUNTERPART_BANKINFO WHERE BUDGETID = #{newBudgetId}")
    void deleteByBankInfo(Integer newBudgetId);

    @Select("SELECT SUM(PCOUNT) FROM ( SELECT COUNT(0) PCOUNT FROM TBL_CONTRACT_PAYMENT WHERE COUNTERBANK = #{bankId} UNION SELECT COUNT(0) PCOUNT FROM TBL_CONTRACT_COLLECTION WHERE COUNTERBANK = #{bankId} )")
    Integer selectCountByUser(String bankId);

    @Update("UPDATE TBL_COUNTERPART_BANKINFO SET bankaccount = #{bankaccount},bankaccname = #{bankaccname},bankstatus = #{bankstatus},banknature = #{banknature},bankkhyh = #{bankkhyh} WHERE bankaccount = #{bankaccount}")
    void updateBybmbh(TblCounterpartBankinfo tblCounterpartBankinfo);
    
//    @SelectProvider(type=TblCounterpartBankinfoMapperSqlConfig.class,method="findListByBankInfo")
//    List<TblCounterpartBankinfo> findListByBankInfo(TblCounterpartBankinfo bank);
    
    
    @Select("SELECT * FROM TBL_COUNTERPART_BANKINFO WHERE  BANKACCOUNT = #{bankaccount}")
    List<TblCounterpartBankinfo> findListByBankInfo(String bankaccount);

    @Select("SELECT * FROM TBL_COUNTERPART_BANKINFO WHERE OUTSIDEID = #{bankaccount}")
	TblCounterpartBankinfo selectBankInfoByAccount(String bankaccount) throws Exception;

}
