package com.huabo.legal.vo.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.huabo.legal.util.PageableParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "TblFwglPopularizeLawPlanQueryParam")
@AllArgsConstructor
@NoArgsConstructor
public class TblFwglPopularizeLawPlanQueryParam extends PageableParam implements Serializable {

	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "计划名称")
	private String popularizeLawPlanName;
	@ApiModelProperty(value = "年份开始时间")
	@JsonFormat(pattern = "yyyy", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy")
	private Date planYearBeginDate;
	@ApiModelProperty(value = "年份结束时间")
	@JsonFormat(pattern = "yyyy", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy")
	private Date planYearEndDate;
	@Column(name = "CREATOR")
	@ApiModelProperty(value = "创建人", hidden = true)
	private String creator;
	@Column(name = "WORKUNIT")
	@ApiModelProperty(value = "工作单位", hidden = true)
	private String workUnit;
	@Column(name = "BELONGGROUP")
	@ApiModelProperty(value = "所属集团", hidden = true)
	private String belongGroup;
}