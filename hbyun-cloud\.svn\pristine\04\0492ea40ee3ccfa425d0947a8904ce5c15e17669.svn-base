package com.huabo.system.service.impl;


import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.huabo.system.config.DateBaseConfig;
import com.huabo.system.mysql.entity.TblProcessAnalysisMySql;
import com.huabo.system.mysql.mapper.TblProcessAnalysisMySqlMapper;
import com.huabo.system.oracle.entity.TblProcessAnalysis;
import com.huabo.system.oracle.mapper.TblProcessAnalysisMapper;
import com.huabo.system.service.TblProcessAnalysisService;

import cn.hutool.core.util.StrUtil;

@Service
public class TblProcessAnalysisServiceImpl implements TblProcessAnalysisService {

    @Resource
    private TblProcessAnalysisMapper tblProcessAnalysisMapper;

    @Resource
    private TblProcessAnalysisMySqlMapper tblProcessAnalysisMySqlMapper;


    @Override
    public void updateSetting(TblProcessAnalysis tblAnalysis) {
    	try {
	        if(DateBaseConfig.DATABASETYPE.equals("Oracle")) {
	        	if(StrUtil.isNotBlank(tblAnalysis.getUserid())) {
					tblProcessAnalysisMapper.insertUser(tblAnalysis.getProcessname(), tblAnalysis.getProcessid(), tblAnalysis.getUserid(), tblAnalysis.getRolename(), tblAnalysis.getUsertaskid());
	        	}else {
	        		tblProcessAnalysisMapper.insertRole(tblAnalysis.getProcessname(), tblAnalysis.getProcessid(), tblAnalysis.getUserid(), tblAnalysis.getRolename(), tblAnalysis.getUsertaskid());
	        	}
	        } else {
	        	if(StrUtil.isNotBlank(tblAnalysis.getUserid())) {
					tblProcessAnalysisMapper.insertUser(tblAnalysis.getProcessname(), tblAnalysis.getProcessid(), tblAnalysis.getUserid(), tblAnalysis.getRolename(), tblAnalysis.getUsertaskid());
	        	}else {
	        		tblProcessAnalysisMapper.insertRole(tblAnalysis.getProcessname(), tblAnalysis.getProcessid(), tblAnalysis.getUserid(), tblAnalysis.getRolename(), tblAnalysis.getUsertaskid());
	        	}
	        }
    	} catch (Exception e) {
    		e.printStackTrace();
    	}
    }

    @Override
    public void updateMySqlSetting(TblProcessAnalysisMySql tblAnalysis) {
        tblProcessAnalysisMySqlMapper.updateByPrimaryKeySelective(tblAnalysis);
    }
}
