package com.huabo.fxgl.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblOrganizationUtil;
import com.hbfk.entity.TblStaffUtil;
import com.hbfk.util.JsonBean;
import com.huabo.fxgl.entity.*;
import com.huabo.fxgl.service.*;
import com.huabo.fxgl.service.impl.RiskcategoryServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;
import springfox.documentation.spring.web.json.Json;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  风险应对模块前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-01
 */
@RestController
@RequestMapping(method = {RequestMethod.GET, RequestMethod.POST})
@Api(tags = "风险管控 - 风险应对管理")
public class RiskCopingController {

    @Autowired
    private RiskcategoryServiceImpl riskcategoryService;
    @Autowired
    private IRiskControlmatrixService riskControlmatrixService;
    @Autowired
    private IRiskService riskService;
    @Autowired
    private IRiskCopingService copingService;
    @Autowired
    private IOrganizationService organizationService;
    @Autowired
    private IStaffService staffService;
    @Autowired
    private IControlmatrixService controlmatrixService;
    @Autowired
    private IAttachmentService attachmentService;

    /**
     * 保存风险应对
     */
    @ApiOperation("风险应对 - 保存 /fxxt/fxyd/strategy_reply_update")
    @RequestMapping(value = "/fxxt/fxyd/strategy_reply_update")
    public JsonBean updatestrategreply(@ApiParam(name = "riskid", value = "风险ID, 必须传", required = true) @RequestParam(required = true) String riskid,
                                   @ApiParam(name = "copingId", value = "风险应对ID", required = false) @RequestParam(required = false) String copingId,
                                   @ApiParam(name = "copingPlot", value = "风险应对策略类型, 数字0-4", required = true) @RequestParam(required = true) String copingPlot,
                                   @ApiParam(name = "riskHopeValue", value = "风险期望值, 数字", required = false) @RequestParam(required = true) String riskHopeValue,
                                   @ApiParam(name = "userId", value = "风险应对负责人ID, 数字", required = true) @RequestParam(required = true) String userId,
                                   @ApiParam(name = "yddes", value = "应对方案描述", required = false) @RequestParam(required = false) String yddes,
                                   @ApiParam(name = "reporteds", value = "reporteds") @RequestParam(required = false) String reporteds,
                                   @ApiParam(name = "removeReporteds", value = "removeReporteds 上传附件ids", required = true) @RequestParam String removeReporteds,
                                   @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {

        TblStaffUtil staffUtil = DealUserToken.parseUserToken(token);//得到了当前登录的用户信息
        if (staffUtil == null) {
            return new JsonBean(401, "未查到用户登录信息，用户登录信息或已过期", null);
        }
        TblOrganizationUtil selectOrg = staffUtil.getCurrentOrg(); //当前用户选择的组织

        Risk risk = null;
        if (StringUtils.isEmpty(riskid) || (risk = riskService.getById(riskid)) == null) {
            return new JsonBean(400, "未提供准确的riskid，无法继续", null);
        }
        QueryWrapper<RiskCoping> queryWrapper = new QueryWrapper<RiskCoping>();
        queryWrapper.eq("RISKID", risk.getRiskid());
        List<RiskCoping> list = copingService.list(queryWrapper);
        if (list != null && list.size()>0) {
            if(StringUtils.isNotEmpty(copingId) && StringUtils.isNotEmpty(riskHopeValue)){
                RiskCoping riskCoping =  copingService.getById(copingId);
                Staff tblStaff = staffService.getById(userId);
                if(null==tblStaff){
                    return new JsonBean(400, "风险应对负责人ID错误，无法继续", null);
                }
                riskCoping.setRiskhopevalue(Integer.valueOf(riskHopeValue));
                riskCoping.setCopinghead(userId.toString());
                riskCoping.setCopingplot(copingPlot);
                riskCoping.setYddes(yddes);
                copingService.updateById(riskCoping,reporteds);
                if (StringUtils.isNotEmpty(removeReporteds)){
                    attachmentService.removeByIds(Arrays.asList(removeReporteds.split(",")));
                }
                return new JsonBean(200, "成功", riskCoping);
            }
        } else{
            RiskCoping riskCoping = new RiskCoping();
            riskCoping.setRiskhopevalue(Integer.valueOf(riskHopeValue));
            riskCoping.setCopinghead(userId);
            riskCoping.setCopingplot(copingPlot);
            riskCoping.setYddes(yddes);
            riskCoping.setRiskid(new BigDecimal(riskid));
            copingService.save(riskCoping,reporteds);
            return new JsonBean(200, "成功", riskCoping);
        }
        return new JsonBean(400, "数据不完整，请核实后重试", null);
    }

    /**
     * 风险应对详查询
     */
    @ApiOperation("风险应对 - 详情查询 /fxxt/fxyd/strategy_reply_info")
    @RequestMapping(value = "/fxxt/fxyd/strategy_reply_info")
    public JsonBean strategy_reply_info(@ApiParam(name = "riskid", value = "风险ID", required = true) @RequestParam(required = true) String riskid,
                                            @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {

        TblStaffUtil staffUtil = DealUserToken.parseUserToken(token);//得到了当前登录的用户信息
        if (staffUtil == null) {
            return new JsonBean(401, "未查到用户登录信息，用户登录信息或已过期", null);
        }
        TblOrganizationUtil selectOrg = staffUtil.getCurrentOrg(); //当前用户选择的组织

        Risk risk = null;
        if (StringUtils.isEmpty(riskid) || (risk = riskService.getById(riskid)) == null) {
            return new JsonBean(400, "未提供准确的riskid，无法继续", null);
        }

        risk.setSsjgName(organizationService.getById(risk.getUnit()).getOrgname());
        risk.setLevel(riskService.getMaxLevelById(risk.getRiskid()));
        QueryWrapper<RiskCoping> queryWrapper = new QueryWrapper<RiskCoping>();
        queryWrapper.eq("RISKID", risk.getRiskid());
        List<RiskCoping> copings = copingService.list(queryWrapper);

        Map<String, Object> result = new HashMap<String, Object>();
        if(null!=copings && copings.size()>0){
            RiskCoping coping = copings.get(0);
            result.put("copings", coping);
            if(StringUtils.isNotBlank(coping.getCopinghead())){
                result.put("userName", staffService.getById(new BigDecimal(coping.getCopinghead())).getRealname());
            }
            List<Controlmatrix> cons = controlmatrixService.findTblControlmatrixByRiskCoping(coping.getRiskcopingid().toString());
            result.put("controls", cons);
            List<Attachment> atts = attachmentService.findAttachmentByCoping(coping.getRiskcopingid().toString());
            result.put("attachments", atts);
        }
        result.put("risk", risk);
        return new JsonBean(200, "成功", result);
    }

    @RequestMapping(value = "/fxxt/fxyd/strategylist")
    @ApiOperation("风险应对 - 列表查询 /fxxt/fxyd/strategylist")
    public JsonBean strategylist(@ApiParam(name = "riskcatid", value = "风险类别ID", required = false) @RequestParam(required = false) String riskcatid,
                              @ApiParam(name = "risknumber", value = "风险编号关键字") @RequestParam(required = false) String risknumber,
                              @ApiParam(name = "riskname", value = "风险名称关键字") @RequestParam(required = false) String riskname,
                              @ApiParam(name = "pageNo", value = "pageNo") @RequestParam(defaultValue = "1") Integer pageNo,
                              @ApiParam(name = "pageSize", value = "pageSize") @RequestParam(defaultValue = "20") Integer pageSize,
                              @ApiParam(name = "token", value = "登录用户token", required = true) @RequestHeader("token") String token) throws Exception {

        TblStaffUtil staffUtil = DealUserToken.parseUserToken(token);//得到了当前登录的用户信息
        if (staffUtil == null) {
            return new JsonBean(401, "未查到用户登录信息，用户登录信息或已过期", null);
        }
        TblOrganizationUtil selectOrg = staffUtil.getCurrentOrg(); //当前用户选择的组织

        if (StringUtils.isEmpty(riskcatid)) {
            Riskcategory cat = riskcategoryService.findQYFXByOrgid(selectOrg.getOrgid().toString(), Riskcategory.FXSJK);
            if (cat != null) {
                riskcatid = cat.getRiskcatid().toString();
            }
        }

        List<Riskcategory> list = riskcategoryService.findRiskCateByRoot(new BigDecimal(riskcatid));
        String ids = "";
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            stringBuilder.append(list.get(i).getRiskcatid().toString());
            if (i < list.size()-1) {
                stringBuilder.append(",");
            }
        }
        ids = stringBuilder.toString();

        Risk risk = new Risk();
        risk.setRiskname(riskname);
        risk.setRisknumber(risknumber);
        IPage<Risk> page = new Page<Risk>(pageNo, pageSize);
        riskService.findRiskByPGRisks(ids, risk, page);

        for (Risk tblRisk : page.getRecords()) {
    			/*List<RiskAssPlanRisk> assPlanRisks = assPlanRiskService.findRiskByRisk(tblRisk.getRiskid());
				tblRisk.setLevel(assPlanRisks.size()==0?"0":assPlanRisks.get(0).getRiskLevel());*/
            QueryWrapper<RiskCoping> queryWrapper = new QueryWrapper<RiskCoping>();
            queryWrapper.eq("RISKID", tblRisk.getRiskid());

            if (copingService.count(queryWrapper)==0) {
                tblRisk.setCopingPlot("0");
            } else {
                List<RiskCoping> copings = copingService.list(queryWrapper);
                switch (copings.get(0).getCopingplot()) {
//                	case "0": tblRisk.setCopingPlot("0"); break;
                    case "1": tblRisk.setCopingPlot("承担"); break;
                    case "2": tblRisk.setCopingPlot("转移"); break;
                    case "3": tblRisk.setCopingPlot("规避"); break;
                    case "4": tblRisk.setCopingPlot("降低（风险控制）"); break;
                    default: tblRisk.setCopingPlot("0");
                }
            }
            String level = riskService.getMaxLevelById(tblRisk.getRiskid());
            if (level == null) {
                tblRisk.setLevel("未评估");
            } else {
                switch (level) {
                    case "1": tblRisk.setLevel("很低"); break;
                    case "2": tblRisk.setLevel("较低"); break;
                    case "3": tblRisk.setLevel("中等"); break;
                    case "4": tblRisk.setLevel("较高"); break;
                    case "5": tblRisk.setLevel("很高"); break;
                    default: tblRisk.setLevel("未评估");
                }
            }
        }
        Map<String, IPage<Risk>> result = new HashMap<String, IPage<Risk>>();
        result.put("data", page);
        return new JsonBean(200, "成功", result);
    }
}