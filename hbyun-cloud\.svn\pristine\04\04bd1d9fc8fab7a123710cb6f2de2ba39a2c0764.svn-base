DEMO:替代表：TBL_RISK_REVIEW、TBL_RISK_REVIEWOPINION、TBL_RISKEVENT、TBL_NBSJ_INNERRULE、TBL_NBSJ_OUTERRULE、
TBL_RISK_REVIEWOPINION  TBL_RISK_COPING、TBL_CONTROLMATRIX、TBL_RISK、TBL_RISK_ASSPLAN_RISK、TBL_RISK_ASSPLAN、TBL_RISK_GROUPPLAN、
TBL_RISK_ASSESSMENTSTD、TBL_MAJORRISKCREATE、TBL_RISK_IMPLEMENTGROUP、TBL_MAJORRISK_BRANCHCREATE、TBL_RISK_IMPLEMENT、
TBL_RISK_MONTHLY_EVALUATION、TBL_RISKMONITORING_CREATION、TBL_RISK_MONITORINGFILL
--预留字符串（输入框）：reservedstring 10个
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSTRING1" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSTRING1" is '预留字符串1';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSTRING2" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSTRING2" is '预留字符串2';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSTRING3" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSTRING3" is '预留字符串3';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSTRING4" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSTRING4" is '预留字符串4';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSTRING5" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSTRING5" is '预留字符串5';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSTRING6" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSTRING6" is '预留字符串6';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSTRING7" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSTRING7" is '预留字符串7';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSTRING8" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSTRING8" is '预留字符串8';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSTRING9" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSTRING9" is '预留字符串9';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSTRING10" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSTRING10" is '预留字符串10';
--预留大文本（文本域）：reservedcontent 10个
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDCONTENT1" CLOB);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDCONTENT1" is '预留大文本1';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDCONTENT2" CLOB);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDCONTENT2" is '预留大文本2';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDCONTENT3" CLOB);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDCONTENT3" is '预留大文本3';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDCONTENT4" CLOB);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDCONTENT4" is '预留大文本4';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDCONTENT5" CLOB);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDCONTENT5" is '预留大文本5';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDCONTENT6" CLOB);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDCONTENT6" is '预留大文本6';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDCONTENT7" CLOB);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDCONTENT7" is '预留大文本7';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDCONTENT8" CLOB);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDCONTENT8" is '预留大文本8';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDCONTENT9" CLOB);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDCONTENT9" is '预留大文本9';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDCONTENT10" CLOB);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDCONTENT10" is '预留大文本10';
--预留下拉多选字符串（多选下拉）：reserveddropdownmultiple 5个
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDDROPDOWNMULTIPLE1" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDDROPDOWNMULTIPLE1" is '预留下拉多选字符串1';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDDROPDOWNMULTIPLE2" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDDROPDOWNMULTIPLE2" is '预留下拉多选字符串2';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDDROPDOWNMULTIPLE3" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDDROPDOWNMULTIPLE3" is '预留下拉多选字符串3';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDDROPDOWNMULTIPLE4" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDDROPDOWNMULTIPLE4" is '预留下拉多选字符串4';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDDROPDOWNMULTIPLE5" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDDROPDOWNMULTIPLE5" is '预留下拉多选字符串5';
--预留多选字符串（多选框）：reservedmultiplechoice 5个
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDMULTIPLECHOICE1" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDMULTIPLECHOICE1" is '预留多选字符串1';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDMULTIPLECHOICE2" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDMULTIPLECHOICE2" is '预留多选字符串2';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDMULTIPLECHOICE3" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDMULTIPLECHOICE3" is '预留多选字符串3';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDMULTIPLECHOICE4" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDMULTIPLECHOICE4" is '预留多选字符串4';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDMULTIPLECHOICE5" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDMULTIPLECHOICE5" is '预留多选字符串5';
--预留年份（年份）：reservedyeartime 5个
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDYEARTIME1" DATE);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDYEARTIME1" is '预留年份1';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDYEARTIME2" DATE);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDYEARTIME2" is '预留年份2';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDYEARTIME3" DATE);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDYEARTIME3" is '预留年份3';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDYEARTIME4" DATE);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDYEARTIME4" is '预留年份4';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDYEARTIME5" DATE);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDYEARTIME5" is '预留年份5';
--预留时间（日期（年月日时分秒））：reservedyearaccuratetime 5个
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDYEARACCURATETIME1" TIMESTAMP);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDYEARACCURATETIME1" is '预留时间1';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDYEARACCURATETIME2" TIMESTAMP);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDYEARACCURATETIME2" is '预留时间2';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDYEARACCURATETIME3" TIMESTAMP);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDYEARACCURATETIME3" is '预留时间3';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDYEARACCURATETIME4" TIMESTAMP);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDYEARACCURATETIME4" is '预留时间4';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDYEARACCURATETIME5" TIMESTAMP);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDYEARACCURATETIME5" is '预留时间5';
--预留年月日（日期（年月日））：reservedtime 5个
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDTIME1" DATE);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDTIME1" is '预留年月日1';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDTIME2" DATE);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDTIME2" is '预留年月日2';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDTIME3" DATE);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDTIME3" is '预留年月日3';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDTIME4" DATE);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDTIME4" is '预留年月日4';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDTIME5" DATE);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDTIME5" is '预留年月日5';
--预留单选字符串（单选框）：reservedsinglechoice 5个
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSINGLECHOICE1" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSINGLECHOICE1" is '预留单选字符串1';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSINGLECHOICE2" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSINGLECHOICE2" is '预留单选字符串2';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSINGLECHOICE3" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSINGLECHOICE3" is '预留单选字符串3';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSINGLECHOICE4" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSINGLECHOICE4" is '预留单选字符串4';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDSINGLECHOICE5" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDSINGLECHOICE5" is '预留单选字符串5';
--预留下拉单选字符串（单选下拉框）：reserveddropdownsinglechoice 5个
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDDROPDOWNSINGLECHOICE1" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDDROPDOWNSINGLECHOICE1" is '预留下拉单选字符串1';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDDROPDOWNSINGLECHOICE2" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDDROPDOWNSINGLECHOICE2" is '预留下拉单选字符串2';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDDROPDOWNSINGLECHOICE3" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDDROPDOWNSINGLECHOICE3" is '预留下拉单选字符串3';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDDROPDOWNSINGLECHOICE4" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDDROPDOWNSINGLECHOICE4" is '预留下拉单选字符串4';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDDROPDOWNSINGLECHOICE5" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDDROPDOWNSINGLECHOICE5" is '预留下拉单选字符串5';
--预留数字（数字输入框）：reservednum 5个
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDNUM1" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDNUM1" is '预留数字1';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDNUM2" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDNUM2" is '预留数字2';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDNUM3" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDNUM3" is '预留数字3';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDNUM4" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDNUM4" is '预留数字4';
alter table "HBGRCZHEZ"."DEMO" add column("RESERVEDNUM5" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."RESERVEDNUM5" is '预留数字5';
--预留人员单选：staffid =》附属字段：realname 5个
alter table "HBGRCZHEZ"."DEMO" add column("STAFFID1" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."STAFFID1" is '预留人员单选1';
alter table "HBGRCZHEZ"."DEMO" add column("STAFFID2" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."STAFFID2" is '预留人员单选2';
alter table "HBGRCZHEZ"."DEMO" add column("STAFFID3" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."STAFFID3" is '预留人员单选3';
alter table "HBGRCZHEZ"."DEMO" add column("STAFFID4" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."STAFFID4" is '预留人员单选4';
alter table "HBGRCZHEZ"."DEMO" add column("STAFFID5" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."STAFFID5" is '预留人员单选5';
--预留人员多选：staffids =》附属字段：realnames 5个
alter table "HBGRCZHEZ"."DEMO" add column("STAFFIDS1" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."STAFFIDS1" is '预留人员多选1';
alter table "HBGRCZHEZ"."DEMO" add column("STAFFIDS2" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."STAFFIDS2" is '预留人员多选2';
alter table "HBGRCZHEZ"."DEMO" add column("STAFFIDS3" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."STAFFIDS3" is '预留人员多选3';
alter table "HBGRCZHEZ"."DEMO" add column("STAFFIDS4" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."STAFFIDS4" is '预留人员多选4';
alter table "HBGRCZHEZ"."DEMO" add column("STAFFIDS5" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."STAFFIDS5" is '预留人员多选5';
--预留组织单选：orgid =》附属字段：orgname 5个
alter table "HBGRCZHEZ"."DEMO" add column("ORGID1" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."ORGID1" is '预留组织单选1';
alter table "HBGRCZHEZ"."DEMO" add column("ORGID2" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."ORGID2" is '预留组织单选2';
alter table "HBGRCZHEZ"."DEMO" add column("ORGID3" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."ORGID3" is '预留组织单选3';
alter table "HBGRCZHEZ"."DEMO" add column("ORGID4" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."ORGID4" is '预留组织单选4';
alter table "HBGRCZHEZ"."DEMO" add column("ORGID5" NUMBER);
comment on column "HBGRCZHEZ"."DEMO"."ORGID5" is '预留组织单选5';
--预留组织多选：orgids =》附属字段：orgnames 5个
alter table "HBGRCZHEZ"."DEMO" add column("ORGIDS1" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."ORGIDS1" is '预留组织多选1';
alter table "HBGRCZHEZ"."DEMO" add column("ORGIDS2" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."ORGIDS2" is '预留组织多选2';
alter table "HBGRCZHEZ"."DEMO" add column("ORGIDS3" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."ORGIDS3" is '预留组织多选3';
alter table "HBGRCZHEZ"."DEMO" add column("ORGIDS4" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."ORGIDS4" is '预留组织多选4';
alter table "HBGRCZHEZ"."DEMO" add column("ORGIDS5" VARCHAR2(4000));
comment on column "HBGRCZHEZ"."DEMO"."ORGIDS5" is '预留组织多选5';