package com.huabo.legal.vo.result;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 法律服务-律师信息表
 */
@ApiModel(value = "TblFwglLawServiceLawyer")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tbl_fwgl_law_service_lawyer")
public class TblFwglLawServiceLawyer implements Serializable {

	private static final long serialVersionUID = 1L;
	@Id
	@Column(name = "LAWYERID")
	@ApiModelProperty(value = "律师ID")
	@GeneratedValue(generator = "JDBC")
	private Long lawyerId;
	@Column(name = "LAWYERNAME")
	@ApiModelProperty(value = "姓名")
	private String lawyerName;
	@Column(name = "SEX")
	@ApiModelProperty(value = "性别")
	private Integer sex;
	@Column(name = "IDENTITYCARD")
	@ApiModelProperty(value = "身份证")
	private String identityCard;
	@Column(name = "PHONE")
	@ApiModelProperty(value = "联系电话")
	private String phone;
	@Column(name = "POSITION")
	@ApiModelProperty(value = "职务")
	private String position;
	@Column(name = "EDUCATION")
	@ApiModelProperty(value = "学历")
	private String education;
	@Column(name = "STARTYEAR")
	@ApiModelProperty(value = "起始年份")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date startYear;
	@Column(name = "COMMUNITYPARTTIMEWORK")
	@ApiModelProperty(value = "社会兼职")
	private String communityPartTimeWork;
	@Column(name = "EXPERTISEDOMAIN")
	@ApiModelProperty(value = "专长领域")
	private String expertiseDomain;
	@Column(name = "LAWOCCUPATION")
	@ApiModelProperty(value = "法律职业")
	private String lawOccupation;
	@Column(name = "LAWYEROCCUPATION")
	@ApiModelProperty(value = "律师职业")
	private String lawyerOccupation;
	@Column(name = "BIOGRAPHICALNOTES")
	@ApiModelProperty(value = "简历")
	private String biographicalNotes;
	@Column(name = "STATE")
	@ApiModelProperty(value = "状态", hidden = true)
	private Integer state;
	@Column(name = "CREATOR")
	@ApiModelProperty(value = "创建人", hidden = true)
	private String creator;
	@Column(name = "WORKUNIT")
	@ApiModelProperty(value = "工作单位", hidden = true)
	private String workUnit;
	@Column(name = "BELONGGROUP")
	@ApiModelProperty(value = "所属集团", hidden = true)
	private String belongGroup;
	@Column(name = "CREATEDTIME")
	@ApiModelProperty(value = "创建时间", hidden = true)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createdTime;
	@Column(name = "UPDATEDTIME")
	@ApiModelProperty(value = "更新时间", hidden = true)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updatedTime;

	public static TblFwglLawServiceLawyer ofId(Long id) {
		TblFwglLawServiceLawyer tblFwglLawServiceLawyerMySql = new TblFwglLawServiceLawyer();
		tblFwglLawServiceLawyerMySql.setLawyerId(id);
		return tblFwglLawServiceLawyerMySql;
	}
}
