package com.huabo.central.enterprises.audit.service.impl;

import com.github.pagehelper.PageInfo;
import com.huabo.central.enterprises.audit.oracle.entity.TblCeaCancelHolidayOracle;
import com.huabo.central.enterprises.audit.oracle.service.TblCeaCancelHolidayOracleService;
import com.huabo.central.enterprises.audit.oracle.service.TblStaffOracleService;
import com.huabo.central.enterprises.audit.service.CeaCancelHolidayService;
import com.huabo.central.enterprises.audit.util.MyJsonBean;
import com.huabo.central.enterprises.audit.util.MyResponseFormat;
import com.huabo.central.enterprises.audit.util.PageResult;
import com.huabo.central.enterprises.audit.vo.param.TblCeaCancelHolidayQueryParam;
import com.vip.vjtools.vjkit.collection.CollectionUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class CeaCancelHolidayServiceImpl implements CeaCancelHolidayService {

	@Resource
	private TblCeaCancelHolidayOracleService tblCeaCancelHolidayOracleService;
	@Resource
	private TblStaffOracleService tblStaffOracleService;

	/**
	 * 销假单 列表查询
	 * @param param
	 * @return
	 */
	@Override
	public MyJsonBean<TblCeaCancelHolidayOracle> getTblCeaCancelHolidayList(TblCeaCancelHolidayQueryParam param) {
		PageInfo<TblCeaCancelHolidayOracle> pageInfo = tblCeaCancelHolidayOracleService.getList(param);
		if (CollectionUtil.isNotEmpty(pageInfo.getList())) {
			List<Long> creatorList = new ArrayList<>();
			//销假人、创建人
			List<Long> peoples = pageInfo.getList().stream().filter(x -> Objects.nonNull(x.getPeople())).distinct()
					.map(TblCeaCancelHolidayOracle::getPeople).collect(Collectors.toList());
			List<Long> creators = pageInfo.getList().stream().filter(x -> Objects.nonNull(x.getCreator())).distinct()
					.map(TblCeaCancelHolidayOracle::getCreator).distinct().collect(Collectors.toList());
			if (CollectionUtil.isNotEmpty(peoples)) {
				creatorList.addAll(peoples);
			}
			if (CollectionUtil.isNotEmpty(creators)) {
				creatorList.addAll(creators);
			}
			Map<Long, String> creatorUserInfoMap = tblStaffOracleService.getCreatorUserInfoMap(StringUtils.join(creatorList, ","));
			//信息提供单位
			List<Long> peopleWorkUnits = pageInfo.getList().stream().filter(x -> Objects.nonNull(x.getPeopleWorkUnit())).distinct()
					.map(TblCeaCancelHolidayOracle::getPeopleWorkUnit).distinct().collect(Collectors.toList());
			Map<Long, String> workUnitIdUserInfoMap = tblStaffOracleService.getWorkUnitIdUserInfoMap(StringUtils.join(peopleWorkUnits, ","));
			pageInfo.getList().forEach(item -> {
				item.setCreatorName(creatorUserInfoMap.getOrDefault(item.getCreator(), ""));
				item.setPeopleName(creatorUserInfoMap.getOrDefault(item.getPeople(), ""));
				item.setPeopleWorkUnitName(workUnitIdUserInfoMap.getOrDefault(item.getPeopleWorkUnit(), ""));
			});
			PageResult<TblCeaCancelHolidayOracle> build = new PageResult<TblCeaCancelHolidayOracle>().build(pageInfo);
			return MyResponseFormat.retParam(200, 200, build);
		}
		return MyResponseFormat.retParam(200, 200, PageResult.buildNoData());
	}

	/**
	 * 销假单 新增/更新
	 * @param param
	 * @return
	 */
	@Override
	public MyJsonBean<TblCeaCancelHolidayOracle> saveOrUpdateTblCeaCancelHoliday(TblCeaCancelHolidayOracle param) {
		TblCeaCancelHolidayOracle result = tblCeaCancelHolidayOracleService.saveOrUpdate(param);
		return MyResponseFormat.retParam(200, 200, result);
	}

	/**
	 * 销假单 刪除
	 * @param id
	 * @return
	 */
	@Override
	public MyJsonBean<Void> deleteTblCeaCancelHoliday(Long id) {
		tblCeaCancelHolidayOracleService.delete(id);
		return MyResponseFormat.retParam(200, 200, null);
	}

	/**
	 * 销假单 详情 查询
	 * @param id
	 * @return
	 */
	@Override
	public MyJsonBean<TblCeaCancelHolidayOracle> getTblCeaCancelHoliday(Long id) {
		TblCeaCancelHolidayOracle result = tblCeaCancelHolidayOracleService.findById(id);
		if (Objects.nonNull(result.getCreator())) {
			result.setCreatorName(tblStaffOracleService.getCreatorUserInfo(result.getCreator()));
		}
		if (Objects.nonNull(result.getPeople())) {
			result.setPeopleName(tblStaffOracleService.getCreatorUserInfo(result.getPeople()));
		}
		if (Objects.nonNull(result.getPeopleWorkUnit())) {
			result.setPeopleWorkUnitName(tblStaffOracleService.getWorkUnitIdUserInfo(result.getPeopleWorkUnit()));
		}
		return MyResponseFormat.retParam(200, 200, result);
	}
}
