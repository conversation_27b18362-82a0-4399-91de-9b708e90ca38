package com.huabo.audit.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hbfk.entity.DealUserToken;
import com.hbfk.entity.TblStaffUtil;
import com.hbfk.util.JsonBean;
import com.hbfk.util.PageInfo;
import com.hbfk.util.ResponseFormat;
import com.huabo.audit.oracle.entity.TblAttachment;
import com.huabo.audit.oracle.entity.TblYqnsSjzgWtzg;
import com.huabo.audit.oracle.entity.TblYqnsSjzgYsnr;
import com.huabo.audit.oracle.mapper.TblAttachmentMapper;
import com.huabo.audit.oracle.mapper.TblYqnsSjzgWtzgMapper;
import com.huabo.audit.oracle.mapper.TblYqnsSjzgYsnrMapper;
import com.huabo.audit.service.TblYqnsSjzgWtzgService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【TBL_YQNS_SJZG_WTZG(问题整改表)】的数据库操作Service实现
 */
@Service
public class TblYqnsSjzgWtzgServiceImpl extends ServiceImpl<TblYqnsSjzgWtzgMapper, TblYqnsSjzgWtzg>
        implements TblYqnsSjzgWtzgService {

    TblStaffUtil loginStaff;

    @Resource
    private TblYqnsSjzgYsnrMapper sjzgYsnrMapper;
    @Resource
    private TblAttachmentMapper tblAttachmentMapper;

    /**
     * 查询
     *
     * @param token
     * @param pageNumber
     * @param pageSize
     * @param vo
     * @return
     * @throws Exception
     */
    @Override
    public JsonBean list(String token, Integer pageNumber, Integer pageSize, TblYqnsSjzgWtzg vo) throws Exception {
        /*JsonBean retParam = validToken(token);
        if (retParam != null) return retParam;*/
        PageInfo<TblYqnsSjzgWtzg> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(pageSize);
        pageInfo.setCurrentPage(pageNumber);
        pageInfo.setTlist(this.baseMapper.selectListByPageInfo(pageInfo, vo));
        pageInfo.setTotalRecord(this.baseMapper.selectCountByPageInfo(pageInfo, vo));
        return ResponseFormat.retParam(1, 200, pageInfo);
    }


    /**
     * 保存
     * 修改
     *
     * @param token
     * @param vo
     * @return
     * @throws Exception
     */
    @Override
    @Transactional
    public JsonBean saveOrUpdate(String token, TblYqnsSjzgWtzg vo) throws Exception {
        /*JsonBean retParam = validToken(token);
        if (retParam != null) return retParam;*/
        if(vo.getSfys()==1){//移送
            Integer ysnrid = sjzgYsnrMapper.insert(vo.getTblYqnsSjzgYsnr());
            if(ysnrid==1){
                vo.setYsnrid(ysnrid.longValue());
            }
        }
        boolean ret = this.saveOrUpdate(vo);
        if (!ret) {
            return ResponseFormat.retParam(0, -1, Boolean.FALSE);
        }
            this.baseMapper.deleteAttByPk(vo.getWtzgid().toString());
            List<String> attIds = vo.getAttIds();
            if (attIds != null && attIds.size() > 0) {
                for (String attId : attIds) {
                    this.baseMapper.saveAtt(vo.getWtzgid().toString(), attId);
                }
            }
        return ResponseFormat.retParam(1, 200, Boolean.TRUE);
    }

    /**
     * 详情
     *
     * @param token
     * @param vo
     * @return
     * @throws Exception
     */
    @Override
    public JsonBean detail(String token, TblYqnsSjzgWtzg vo) throws Exception {
        JsonBean retParam = validToken(token);
        if (retParam != null) return retParam;
        TblYqnsSjzgWtzg bean = this.getById(vo.getWtzgid());
        if(bean.getYsnrid()!=null){
            TblYqnsSjzgYsnr tblYqnsSjzgYsnr = sjzgYsnrMapper.selectById(bean.getYsnrid());
            bean.setTblYqnsSjzgYsnr(tblYqnsSjzgYsnr);
        }
        if (bean == null) {
            return ResponseFormat.retParam(0, -1, "记录不存在");
        }
        if(bean.getYsnrid()!=null){
            List<TblAttachment> attachments = this.baseMapper.selectAttachmentListByPk(bean.getYsnrid().toString());
            bean.setAttachments(attachments);
        }
        return ResponseFormat.retParam(1, 200, bean);
    }

    /**
     * 一个多个删除
     *
     * @param token
     * @param vo    ids[]
     * @return
     * @throws Exception
     */
    @Override
    @Transactional
    public JsonBean delete(String token, TblYqnsSjzgWtzg vo) throws Exception {
        JsonBean retParam = validToken(token);
        if (retParam != null) return retParam;
        boolean ret = this.removeByIds(vo.getIds());
        if (!ret) {
            return ResponseFormat.retParam(0, -1, Boolean.FALSE);
        }
        return ResponseFormat.retParam(1, 200, Boolean.TRUE);
    }

    /**
     * 附件删除
     *
     * @param token
     * @return
     * @throws Exception
     */
    @Override
    @Transactional
    public JsonBean deleteAttach(String token, String attid) throws Exception {
        JsonBean retParam = validToken(token);
        if (retParam != null) return retParam;
        baseMapper.deleteAttById(attid);
        BigDecimal attId = new BigDecimal(attid);
        tblAttachmentMapper.deleteEntity(attId);
        return ResponseFormat.retParam(1, 200, Boolean.TRUE);
    }

    /**
     * 校验登录token有效性
     *
     * @param token
     * @return null or not null
     * @throws Exception
     */
    private JsonBean validToken(String token) throws Exception {
        loginStaff = DealUserToken.parseUserToken(token);
        if (loginStaff == null) {
            return ResponseFormat.retParam(0, 20006, null);
        }
        return null;
    }
}




