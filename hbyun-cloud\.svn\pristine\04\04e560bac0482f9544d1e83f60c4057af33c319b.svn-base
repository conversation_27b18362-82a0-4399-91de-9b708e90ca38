package com.huabo.system.oracle.service.impl;

import com.huabo.system.constant.YesNo;
import com.huabo.system.exception.ServiceException;
import com.huabo.system.oracle.entity.TblSystemCustomizeShow;
import com.huabo.system.oracle.mapper.TblSystemCustomizeShowMapper;
import com.huabo.system.oracle.service.TblSystemCustomizeShowService;
import com.huabo.system.vo.param.TblSystemCustomizeShowQueryParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class TblSystemCustomizeShowServiceImpl implements TblSystemCustomizeShowService {

	@Resource
	private TblSystemCustomizeShowMapper tblSystemCustomizeShowMapper;

	/**
	 * 自定义展示列表 查询
	 * @param param
	 * @return
	 */
	@Override
	public List<TblSystemCustomizeShow> getList(TblSystemCustomizeShowQueryParam param) {
		Example example = new Example(TblSystemCustomizeShow.class);
		Example.Criteria criteria = example.createCriteria();
		if (param.getSceneId() != null) {
			criteria.andEqualTo("sceneId", param.getSceneId());
		}
		if (StringUtils.isNotBlank(param.getSceneCode())) {
			criteria.andEqualTo("sceneCode", param.getSceneCode());
		}
		if (param.getIsList() != null) {
			criteria.andEqualTo("isList", param.getIsList());
		}
		if (param.getIsDetails() != null) {
			criteria.andEqualTo("isDetails", param.getIsDetails());
		}
		example.setOrderByClause(" sort asc,id desc ");
		return tblSystemCustomizeShowMapper.selectByExample(example);
	}

	/**
	 * 自定义展示 新增/更新
	 * @param param
	 * @return
	 */
	@Override
	public TblSystemCustomizeShow saveOrUpdate(TblSystemCustomizeShow param) {
		Date now = new Date();
		if (param.getId() == null) {
			int count = tblSystemCustomizeShowMapper.selectCount(TblSystemCustomizeShow.of(param.getField(), param.getSceneId()));
			if (count > 0) {
				throw new ServiceException(400, "字段名已存在");
			}
			param.setState(YesNo.NO);
			param.setCreatedTime(now);
			param.setUpdatedTime(now);
			tblSystemCustomizeShowMapper.insertSelective(param);
		} else {
			if (idById(param.getId())) {
				throw new ServiceException(400, 50001);
			}
			param.setUpdatedTime(now);
			tblSystemCustomizeShowMapper.updateByPrimaryKeySelective(param);
		}
		return findById(param.getId());
	}

	/**
	 * 自定义展示详情 查询
	 * @param id
	 * @return
	 */
	@Override
	public TblSystemCustomizeShow findById(Integer id) {
		TblSystemCustomizeShow systemCustomizeShow = tblSystemCustomizeShowMapper.selectByPrimaryKey(id);
		if (systemCustomizeShow == null) {
			throw new ServiceException(400, 50001);
		}
		return systemCustomizeShow;
	}

	/**
	 * 自定义展示 刪除
	 * @param id
	 */
	@Override
	public void delete(Integer id) {
		tblSystemCustomizeShowMapper.deleteByPrimaryKey(id);
	}

	/**
	 * 自定义展示-状态变更
	 * @param sceneId
	 * @param state
	 */
	@Override
	public void updateState(Integer sceneId, Integer state) {
		TblSystemCustomizeShow update = new TblSystemCustomizeShow();
		update.setState(state);
		Example example = new Example(TblSystemCustomizeShow.class);
		example.createCriteria().andEqualTo("sceneId", sceneId);
		tblSystemCustomizeShowMapper.updateByExampleSelective(update, example);
	}

	/**
	 * 根据id查询 自定义展示 是存在
	 * @param id
	 * @return
	 */
	private Boolean idById(Integer id) {
		int count = tblSystemCustomizeShowMapper.selectCount(TblSystemCustomizeShow.ofId(id));
		if (count == 0) {
			return true;
		}
		return false;
	}
}
