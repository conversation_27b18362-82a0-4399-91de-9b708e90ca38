package com.huabo.monitor.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "TBL_ORGANIZATION_INFO")
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value="审计机构管理", description="审计机构管理")
public class TblOrganizationInfo implements Serializable {
	 
	public final static String REGISTERUSERPASSWORD = "12345678";
	
	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY,generator = "select HIBERNATE_SEQUENCE.nextval from dual")  
	@TableId("INFOID")
	@ApiModelProperty(value = "主键ID,自动增长")
	private BigDecimal infoid;//主键ID,自动增长
	
	@Column(name = "UNIFIEDCODE")
	@ApiModelProperty(value = "统一社会信用代码")
	private String unifiedcode; 
	
	@Column(name = "NOTUNIFIEDCODE")
	@ApiModelProperty(value = "尚未领取统一社会信用代码的填写原组织机构代码")
	private String notunifiedcode; 
	@Column(name = "ORGNAME")
	@ApiModelProperty(value = "单位详细名称")
	private String orgname; 
	@Column(name = "ORGID")
	@ApiModelProperty(value = "单位ID")
	private BigDecimal orgid; 
	@Column(name = "ORGANIZATIONTYPE")
	@ApiModelProperty(value = "机构类型  10 企业 20 事业单位 30 机关 40 社会团体 51 民办非企业单位")
	private BigDecimal organizationtype; 
	@Column(name = "MAINACTIVITYONE")
	@ApiModelProperty(value = "主要活动1")
	private String mainactivityone; 
	@Column(name = "MAINACTIVITYTWO")
	@ApiModelProperty(value = "主要活动2")
	private String mainactivitytwo; 
	@Column(name = "MAINACTIVITYTHR")
	@ApiModelProperty(value = "主要活动3")
	private String mainactivitythr; 
	@Column(name = "INDUSTRYCODE")
	@ApiModelProperty(value = "行业代码")
	private String industrycode; 
	@Column(name = "PROVINCE")
	@ApiModelProperty(value = "省")
	private String province; 
	@Column(name = "LAND")
	@ApiModelProperty(value = "地")
	private String land;   
	@Column(name = "COUNTY")
	@ApiModelProperty(value = "县")
	private String county;   
	@Column(name = "COUNTRY")
	@ApiModelProperty(value = "乡")
	private String country;  
	@Column(name = "STREET")
	@ApiModelProperty(value = "街")
	private String street;  
	@Column(name = "SUBDISTRICTOFFICE")
	@ApiModelProperty(value = "街道办事处")
	private String subdistrictoffice;  
	@Column(name = "COMMUNITY")
	@ApiModelProperty(value = "社区")
	private String community;  
	@Column(name = "AREACODE")
	@ApiModelProperty(value = "区域代码")
	private String areacode;  
	@Column(name = "RURALCODE")
	@ApiModelProperty(value = "城乡代码")
	private String ruralcode;  
	@Column(name = "UNITSIZE")
	@ApiModelProperty(value = "单位规模  1 大型 2 中型 3 小型 4 微型")
	private BigDecimal unitsize;  
	@Column(name = "NUMBEREMPLOYEES")
	@ApiModelProperty(value = "从业人员期末人数")
	private BigDecimal numberemployees;  
	@Column(name = "LEGALREPRESENTATIVE")
	@ApiModelProperty(value = "法定代表人")
	private String legalrepresentative;  
	@Column(name = "ACCOUNTINGSTANDARDS")
	@ApiModelProperty(value = "执行会计标准类别1 企业会计准则制度 2 政府会计准则制度")
	private BigDecimal accountingstandards;  
	
	@Column(name = "EMAIL")
	@ApiModelProperty(value = "电子邮件")
	private String email;  
	@Column(name = "PHONE")
	@ApiModelProperty(value = "长途电话")
	private BigDecimal phone;  
	@Column(name = "FIXEDTELEPHONE")
	@ApiModelProperty(value = "固定电话")
	private String fixedtelephone;  
	@Column(name = "POSTALCODE")
	@ApiModelProperty(value = "邮政编码")
	private BigDecimal postalcode;  
	@Column(name = "WEBSITE")
	@ApiModelProperty(value = "网址")
	private String website;  
	@Column(name = "SFYSJFR")
	@ApiModelProperty(value = "本法人单位是否有上一级法人  1是 0否")
	private BigDecimal sfysjfr;  
	@Column(name = "UNIFIEDCODEONE")
	@ApiModelProperty(value = "上一级法人社会信用代码")
	private String unifiedcodeone;  
	@Column(name = "UNIFIEDCODETWO")
	@ApiModelProperty(value = "尚未领取统一社会信用代码的填写原组织机构代码")
	private String unifiedcodetwo;  
	@Column(name = "UNIFIEDCODETHR")
	@ApiModelProperty(value = "上一级法人单位单位详细名称")
	private String unifiedcodethr;  
	@Column(name = "WHETHERFIT")
	@ApiModelProperty(value = "是否设置总审计师  1 是 2 否")
	private BigDecimal whetherfit;  
	@Column(name = "POSITIONLEVEL")
	@ApiModelProperty(value = "1 领导班子成员 2 除领导班子成  总审计师职位层级   员以外的其他高级管理人员3 中层机构主要负责人或以下级别 4 其他")
	private BigDecimal positionlevel;  
	@Column(name = "APPOINTMENTMODE")
	@ApiModelProperty(value = "1 上一级法人单位委派 2 本单位自行产生（须报上级单位批准或备案）3 本单位自行产生（无须批准或备案） 4 其他")
	private BigDecimal appointmentmode;  
	 
	@Column(name = "IFSETUP")
	@ApiModelProperty(value = "是否设置内部审计机构  1 是 2 否")
	private BigDecimal ifsetup;  
	 
	@Column(name = "AUDITNAME")
	@ApiModelProperty(value = "内部审计机构名称")
	private String auditname;  
	 
	@Column(name = "LEADINGORGANIZATION")
	@ApiModelProperty(value = "内部审计工作的领导机构1 主要负责人分管 2 主要负责人分管（其他领导人员协管）3 其他班子成员分管 4 总审计师分管5 其他")
	private BigDecimal leadingorganization;  
	 
	@Column(name = "IFINDEPENDENTLY")
	@ApiModelProperty(value = "是否独立设置内部机构 1 是 2 否")
	private BigDecimal ifindependently;  
	 
	@Column(name = "FUNCTIONALDEPARTMENT")
	@ApiModelProperty(value = "如为2，则内部审计机构与以下哪些职能部门合并设置")
	private String functionaldepartment;   
	
	@Column(name = "INTERNALAUDIT")
	@ApiModelProperty(value = "内部审计机构层级  1 本单位二级部门 2 本单位所属独立核算的二级单位3 本单位二级部门的内设部门 4 其他")
	private BigDecimal internalaudit;   
	 
	@Column(name = "STATISTICAL")
	@ApiModelProperty(value = "统计负责人")
	private String statistical;   
	 
	@Column(name = "PREPARER")
	@ApiModelProperty(value = "填表人")
	private String preparer;   
	 
	@Column(name = "CONTACTNUMBER")
	@ApiModelProperty(value = "联系电话")
	private String contactnumber;   
	 
	@Column(name = "FILLINGDATE")
	@TableField("FILLINGDATE")
	@JSONField(format = "yyyy-MM-dd")
	@DateTimeFormat(pattern="yyyy-MM-dd")
	@ApiModelProperty(value = "填表日期")
	private Date fillingdate;   
	 
 
 
}
