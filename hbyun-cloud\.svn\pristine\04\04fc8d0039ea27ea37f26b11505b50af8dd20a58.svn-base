package com.huabo.compliance.controller;

import com.hbfk.util.JsonBean;
import com.huabo.compliance.exception.ServiceException;
import com.huabo.compliance.oracle.entity.TblComplianceDtyOracle;
import com.huabo.compliance.oracle.entity.TblComplianceImOracle;
import com.huabo.compliance.oracle.entity.TblComplianceManualMgtOracle;
import com.huabo.compliance.oracle.entity.TblCompliancePlanMgtOracle;
import com.huabo.compliance.service.MgtService;
import com.huabo.compliance.vo.param.TblComplianceDtyQueryParam;
import com.huabo.compliance.vo.param.TblComplianceImQueryParam;
import com.huabo.compliance.vo.param.TblComplianceManualMgtQueryParam;
import com.huabo.compliance.vo.param.TblCompliancePlanMgtQueryParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Api(tags = {"合规管理"})
@RequestMapping(value = "/api-auth/im")
@Slf4j
public class MgtController {

	@Resource
	private MgtService mgtService;

	@ApiOperation("计划管理 列表查询")
	@PostMapping("/conference/getList")
	public JsonBean getTblCompliancePlanMgtList(@RequestBody TblCompliancePlanMgtQueryParam param) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.getTblCompliancePlanMgtList(param);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("计划管理 列表查询 ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("计划管理 新增/更新")
	@PostMapping("/conference/saveOrUpdate")
	public JsonBean saveOrUpdateTblCompliancePlanMgt(@RequestBody @Validated TblCompliancePlanMgtOracle param) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.saveOrUpdateTblCompliancePlanMgt(param);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("计划管理 新增/更新 ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("计划管理 刪除")
	@DeleteMapping("/conference/{id}")
	public JsonBean deleteTblCompliancePlanMgt(@PathVariable Integer id) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.deleteTblCompliancePlanMgt(id);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("计划管理 刪除 ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("计划管理 详情 查询")
	@GetMapping("/conference/{id}")
	public JsonBean getTblCompliancePlanMgt(@PathVariable Integer id) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.getTblCompliancePlanMgt(id);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("计划管理 详情 查询  ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("重点岗位合规责任 列表查询")
	@PostMapping("/dty/getList")
	public JsonBean getTblComplianceDtyList(@RequestBody TblComplianceDtyQueryParam param) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.getTblComplianceDtyList(param);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("重点岗位合规责任 列表查询 ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("重点岗位合规责任 新增/更新")
	@PostMapping("/dty/saveOrUpdate")
	public JsonBean saveOrUpdateTblComplianceDty(@RequestBody @Validated TblComplianceDtyOracle param) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.saveOrUpdateTblComplianceDty(param);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("重点岗位合规责任 新增/更新 ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("重点岗位合规责任 刪除")
	@DeleteMapping("/dty/{id}")
	public JsonBean deleteTblComplianceDty(@PathVariable Integer id) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.deleteTblComplianceDty(id);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("重点岗位合规责任 刪除 ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("重点岗位合规责任 详情 查询")
	@GetMapping("/dty/{id}")
	public JsonBean getTblComplianceDty(@PathVariable Integer id) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.getTblComplianceDty(id);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("重点岗位合规责任 详情 查询  ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("合规管理员信息管理 列表查询")
	@PostMapping("/info/getList")
	public JsonBean getTblComplianceImList(@RequestBody TblComplianceImQueryParam param) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.getTblComplianceImList(param);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("合规管理员信息管理 列表查询 ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("合规管理员信息管理 新增/更新")
	@PostMapping("/info/saveOrUpdate")
	public JsonBean saveOrUpdateTblComplianceIm(@RequestBody @Validated TblComplianceImOracle param) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.saveOrUpdateTblComplianceIm(param);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("合规管理员信息管理 新增/更新 ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("合规管理员信息管理 刪除")
	@DeleteMapping("/info/{id}")
	public JsonBean deleteTblComplianceIm(@PathVariable Integer id) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.deleteTblComplianceIm(id);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("合规管理员信息管理 刪除 ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("合规管理员信息管理 详情 查询")
	@GetMapping("/info/{id}")
	public JsonBean getTblComplianceIm(@PathVariable Integer id) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.getTblComplianceIm(id);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("合规管理员信息管理 详情 查询  ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("合规手册管理 列表查询")
	@PostMapping("/manual/getList")
	public JsonBean getTblComplianceManualMgtList(@RequestBody TblComplianceManualMgtQueryParam param) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.getTblComplianceManualMgtList(param);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("合规手册管理 列表查询 ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("合规手册管理 新增/更新")
	@PostMapping("/manual/saveOrUpdate")
	public JsonBean saveOrUpdateTblComplianceManualMgt(@RequestBody @Validated TblComplianceManualMgtOracle param) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.saveOrUpdateTblComplianceManualMgt(param);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("合规手册管理 新增/更新 ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("合规手册管理 刪除")
	@DeleteMapping("/manual/{id}")
	public JsonBean deleteTblComplianceManualMgt(@PathVariable Integer id) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.deleteTblComplianceManualMgt(id);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("合规手册管理 刪除 ...接口 异常", e);
		}
		return jsonBean;
	}

	@ApiOperation("合规手册管理 详情 查询")
	@GetMapping("/manual/{id}")
	public JsonBean getTblComplianceManualMgt(@PathVariable Integer id) {
		JsonBean jsonBean = null;
		try {
			jsonBean = mgtService.getTblComplianceManualMgt(id);
		} catch (ServiceException ex) {
			throw ex;
		} catch (Exception e) {
			log.error("合规手册管理 详情 查询  ...接口 异常", e);
		}
		return jsonBean;
	}
}
