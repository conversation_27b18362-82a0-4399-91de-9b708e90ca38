<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huabo.etl.mapper.XRepositoryMapper">

    <resultMap type="XRepository" id="XRepositoryResult">
        <result property="id"    column="id"    />
        <result property="repoId"    column="repo_id"    />
        <result property="repoName"    column="repo_name"    />
        <result property="repoUsername"    column="repo_username"    />
        <result property="repoPassword"    column="repo_password"    />
        <result property="repoType"    column="repo_type"    />
        <result property="dbAccess"    column="db_access"    />
        <result property="dbHost"    column="db_host"    />
        <result property="dbPort"    column="db_port"    />
        <result property="dbName"    column="db_name"    />
        <result property="dbUsername"    column="db_username"    />
        <result property="dbPassword"    column="db_password"    />
        <result property="isDel"    column="is_del"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createdBy"    column="created_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="type"    column="type"    />
        <result property="baseDir"    column="base_dir"    />
    </resultMap>

    <sql id="selectXRepositoryVo">
        select id, repo_id, repo_name, repo_username, repo_password, repo_type, db_access, db_host, db_port, db_name, db_username, db_password, is_del, created_time, update_time, created_by, update_by, type, base_dir from kettle_repository
    </sql>

    <select id="selectXRepositoryList" parameterType="XRepository" resultMap="XRepositoryResult">
        <include refid="selectXRepositoryVo"/>
        <where>
            <if test="repoId != null  and repoId != ''"> and repo_id = #{repoId}</if>
            <if test="repoName != null  and repoName != ''"> and repo_name like concat(concat('%', #{repoName}), '%')</if>
            <if test="repoUsername != null  and repoUsername != ''"> and repo_username like concat(concat('%', #{repoUsername}), '%')</if>
            <if test="repoPassword != null  and repoPassword != ''"> and repo_password = #{repoPassword}</if>
            <if test="repoType != null  and repoType != ''"> and repo_type = #{repoType}</if>
            <if test="dbAccess != null  and dbAccess != ''"> and db_access = #{dbAccess}</if>
            <if test="dbHost != null  and dbHost != ''"> and db_host = #{dbHost}</if>
            <if test="dbPort != null  and dbPort != ''"> and db_port = #{dbPort}</if>
            <if test="dbName != null  and dbName != ''"> and db_name like concat(concat('%', #{dbName}), '%')</if>
            <if test="dbUsername != null  and dbUsername != ''"> and db_username like concat(concat('%', #{dbUsername}), '%')</if>
            <if test="dbPassword != null  and dbPassword != ''"> and db_password = #{dbPassword}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="baseDir != null  and baseDir != ''"> and base_dir = #{baseDir}</if>
            and  is_del = 0
        </where>
    </select>

    <select id="selectXRepositoryById" parameterType="Long" resultMap="XRepositoryResult">
        <include refid="selectXRepositoryVo"/>
        where id = #{id}
    </select>


    <update id="updateXRepository" parameterType="XRepository">
        update kettle_repository
        <trim prefix="SET" suffixOverrides=",">
            <if test="repoId != null and repoId != ''">repo_id = #{repoId},</if>
            <if test="repoName != null">repo_name = #{repoName},</if>
            <if test="repoUsername != null">repo_username = #{repoUsername},</if>
            <if test="repoPassword != null">repo_password = #{repoPassword},</if>
            <if test="repoType != null">repo_type = #{repoType},</if>
            <if test="dbAccess != null">db_access = #{dbAccess},</if>
            <if test="dbHost != null">db_host = #{dbHost},</if>
            <if test="dbPort != null">db_port = #{dbPort},</if>
            <if test="dbName != null">db_name = #{dbName},</if>
            <if test="dbUsername != null">db_username = #{dbUsername},</if>
            <if test="dbPassword != null">db_password = #{dbPassword},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="type != null">type = #{type},</if>
            <if test="baseDir != null">base_dir = #{baseDir},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateIsDel" parameterType="Long" >
        update kettle_repository set is_del=1 where id = #{id}
    </update>
    <update id="updateIsDelBatch" parameterType="String">
        update kettle_repository set is_del=1 where id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <delete id="deleteXRepositoryById" parameterType="Long">
        delete from kettle_repository where id = #{id}
    </delete>

    <delete id="deleteXRepositoryByIds" parameterType="String">
        delete from kettle_repository where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>