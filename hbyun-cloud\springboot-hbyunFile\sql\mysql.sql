-- -- 创建文件上传管理表（MySQL）
-- CREATE TABLE TBL_FILE_UPLOAD
-- (
--     FILEID          BIGINT       NOT NULL PRIMARY KEY COMMENT '文件ID，主键，需外部生成，非自增',
--     FILENAME        VARCHAR(255) NOT NULL COMMENT '文件原始名称（上传时的名称）',
--     FILEEXTENSION   VARCHAR(10) COMMENT '文件后缀，例如 .jpg, .pdf, .png 等',
--     FILESIZE        BIGINT       NOT NULL COMMENT '文件大小，单位字节',
--     FILEPATH        VARCHAR(500) NOT NULL COMMENT '文件存储路径（绝对路径或相对路径）',
--     SAVETYPE        VARCHAR(20)  NOT NULL COMMENT '文件保存类型，本地：LOCAL，NFS：NFS，云存储：OSS 等',
--     UPLOADTIME      TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '文件上传时间，默认当前时间',
--     FILEHASH        VARCHAR(64) COMMENT '文件的唯一哈希值，用于校验文件内容或防止重复上传',
--     ISENCRYPTED     TINYINT(1) DEFAULT 1 COMMENT '是否加密存储，1表示加密，0表示不加密，默认值为1',
--     ISACTIVE        TINYINT(1) DEFAULT 1 COMMENT '文件是否有效，1表示有效，0表示无效，默认值为1',
--     DESCRIPTION     TEXT COMMENT '文件的附加描述信息',
--     UPLOADERNAME    VARCHAR(64)  NOT NULL COMMENT '上传人名称',
--     UPLOADERACCOUNT VARCHAR(64)  NOT NULL COMMENT '上传账号',
--     USERID          FILESIZE BIGINT NOT NULL COMMENT '用户id'
-- ) COMMENT='用于管理文件上传的表';

-- 添加字段
ALTER TABLE TBL_ATTACHMENT
    ADD COLUMN ISENCRYPTED VARCHAR(1) DEFAULT '0' NOT NULL;

-- 添加注释
ALTER TABLE TBL_ATTACHMENT
    MODIFY COLUMN ISENCRYPTED VARCHAR(1) COMMENT '是否加密存储，1表示加密，0表示不加密，默认值为0';
