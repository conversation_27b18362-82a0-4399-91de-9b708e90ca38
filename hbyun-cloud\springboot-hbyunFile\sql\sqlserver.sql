-- -- 创建文件上传管理表（SQL Server）
-- CREATE TABLE TBL_FILE_UPLOAD
-- (
--     FILEID          BIGINT NOT NULL PRIMARY KEY, -- 文件ID，主键，需外部生成，非自增
--     FILENAME        NVARCHAR(255) NOT NULL,      -- 文件原始名称（上传时的名称）
--     FILEEXTENSION   NVARCHAR(10),                -- 文件后缀，例如 .jpg, .pdf, .png 等
--     FILESIZE        BIGINT NOT NULL,             -- 文件大小，单位字节
--     FILEPATH        NVARCHAR(500) NOT NULL,      -- 文件存储路径（绝对路径或相对路径）
--     SAVETYPE        NVARCHAR(20) NOT NULL,       -- 文件保存类型，本地：LOCAL，NFS：NFS，云存储：OSS 等
--     UPLOADTIME      DATETIME DEFAULT GETDATE(),  -- 文件上传时间，默认当前时间
--     FILEHASH        NVARCHAR(64),                -- 文件的唯一哈希值，用于校验文件内容或防止重复上传
--     ISENCRYPTED     BIT      DEFAULT 1,          -- 是否加密存储，1表示加密，0表示不加密，默认值为1
--     ISACTIVE        BIT      DEFAULT 1,          -- 文件是否有效，1表示有效，0表示无效，默认值为1
--     DESCRIPTION     NVARCHAR( MAX),              -- 文件的附加描述信息
--     UPLOADERNAME    NVARCHAR(64),                -- 上传人名称
--     UPLOADERACCOUNT NVARCHAR(64),                -- 上传账号
--     USERID          BIGINT NOT NULL              -- 用户id
-- );
--
-- -- 添加表和列的注释（SQL Server 需使用扩展属性）
-- EXEC sp_addextendedproperty 'MS_Description', '用于管理文件上传的表', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD';
--
-- EXEC sp_addextendedproperty 'MS_Description', '文件ID，主键，需外部生成，非自增', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'FILEID';
-- EXEC sp_addextendedproperty 'MS_Description', '文件原始名称（上传时的名称）', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'FILENAME';
-- EXEC sp_addextendedproperty 'MS_Description', '文件后缀，例如 .jpg, .pdf, .png 等', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'FILEEXTENSION';
-- EXEC sp_addextendedproperty 'MS_Description', '文件大小，单位字节', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'FILESIZE';
-- EXEC sp_addextendedproperty 'MS_Description', '文件存储路径（绝对路径或相对路径）', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'FILEPATH';
-- EXEC sp_addextendedproperty 'MS_Description', '文件保存类型，本地：LOCAL，NFS：NFS，云存储：OSS 等', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'SAVETYPE';
-- EXEC sp_addextendedproperty 'MS_Description', '文件上传时间，默认当前时间', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'UPLOADTIME';
-- EXEC sp_addextendedproperty 'MS_Description', '文件的唯一哈希值，用于校验文件内容或防止重复上传', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'FILEHASH';
-- EXEC sp_addextendedproperty 'MS_Description', '是否加密存储，1表示加密，0表示不加密，默认值为1', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'ISENCRYPTED';
-- EXEC sp_addextendedproperty 'MS_Description', '文件是否有效，1表示有效，0表示无效，默认值为1', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'ISACTIVE';
-- EXEC sp_addextendedproperty 'MS_Description', '文件的附加描述信息', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'DESCRIPTION';
-- EXEC sp_addextendedproperty 'MS_Description', '上传人名称', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'UPLOADERNAME';
-- EXEC sp_addextendedproperty 'MS_Description', '上传账号', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'UPLOADERACCOUNT';
-- EXEC sp_addextendedproperty 'MS_Description', '用户id', 'SCHEMA', 'dbo', 'TABLE', 'TBL_FILE_UPLOAD', 'COLUMN', 'USERID';


-- 添加字段
ALTER TABLE TBL_ATTACHMENT
    ADD ISENCRYPTED VARCHAR(1) DEFAULT '0' NOT NULL;

-- 添加注释
EXEC sp_addextendedproperty
    @name = N'MS_Description',
    @value = '是否加密存储，1表示加密，0表示不加密，默认值为0',
    @level0type = N'SCHEMA',
    @level0name = 'dbo',
    @level1type = N'TABLE',
    @level1name = 'TBL_ATTACHMENT',
    @level2type = N'COLUMN',
    @level2name = 'ISENCRYPTED';
