-- -- 创建文件上传管理表
-- CREATE TABLE TBL_FILE_UPLOAD (
--                             FILEID          NUMBER NOT NULL PRIMARY KEY COMMENT '文件ID，主键，需外部生成，非自增',
--                             FILENAME        VARCHAR2(255) NOT NULL COMMENT '文件原始名称（上传时的名称）',
--                             FILEEXTENSION   VARCHAR2(10) COMMENT '文件后缀，例如 .jpg, .pdf, .png 等',
--                             FILESIZE        NUMBER NOT NULL COMMENT '文件大小，单位字节',
--                             FILEPATH        VARCHAR2(500) NOT NULL COMMENT '文件存储路径（绝对路径或相对路径）',
--                             SAVETYPE        VARCHAR2(20) NOT NULL COMMENT '文件保存类型，本地：LOCAL，NFS：NFS，云存储：OSS 等',
--                             UPLOADTIME      TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '文件上传时间，默认当前时间',
--                             FILEHASH        VARCHAR2(64) COMMENT '文件的唯一哈希值，用于校验文件内容或防止重复上传',
--                             ISENCRYPTED     NUMBER(1) DEFAULT 1 COMMENT '是否加密存储，1表示加密，0表示不加密，默认值为1',
--                             ISACTIVE        NUMBER(1) DEFAULT 1 COMMENT '文件是否有效，1表示有效，0表示无效，默认值为1',
--                             DESCRIPTION     CLOB COMMENT '文件的附加描述信息',
--                             UPLOADERNAME   VARCHAR2(64) NOT NULL COMMENT '上传人名称',
--                             UPLOADERACCOUNT VARCHAR2(64) NOT NULL COMMENT '上传账号',
--                             USERID             NUMBER    NOT NULL COMMENT '用户id'
-- );
--
-- -- 添加表的注释
-- COMMENT ON TABLE TBL_FILE_UPLOAD IS '用于管理文件上传的表';
--
-- -- 添加字段的注释
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.FILEID IS '文件ID，主键，需外部生成，非自增';
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.FILENAME IS '文件原始名称（上传时的名称）';
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.FILEEXTENSION IS '文件后缀，例如 .jpg, .pdf, .png 等';
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.FILESIZE IS '文件大小，单位字节';
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.FILEPATH IS '文件存储路径（绝对路径或相对路径）';
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.SAVETYPE IS '文件保存类型，本地：LOCAL，NFS：NFS，云存储：OSS 等';
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.UPLOADTIME IS '文件上传时间，默认当前时间';
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.FILEHASH IS '文件的唯一哈希值，用于校验文件内容或防止重复上传';
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.ISENCRYPTED IS '是否加密存储，1表示加密，0表示不加密，默认值为1';
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.ISACTIVE IS '文件是否有效，1表示有效，0表示无效，默认值为1';
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.DESCRIPTION IS '文件的附加描述信息';
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.UPLOADERNAME IS '上传人名称';
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.UPLOADERACCOUNT IS '上传账号';
-- COMMENT ON COLUMN TBL_FILE_UPLOAD.USERID IS '用户id';
--

-- 新增表字段
ALTER TABLE TBL_ATTACHMENT
    ADD ISENCRYPTED VARCHAR2(1) DEFAULT '0' NOT NULL;

COMMENT ON COLUMN TBL_ATTACHMENT.ISENCRYPTED IS '是否加密存储，1表示加密，0表示不加密，默认值为0';
