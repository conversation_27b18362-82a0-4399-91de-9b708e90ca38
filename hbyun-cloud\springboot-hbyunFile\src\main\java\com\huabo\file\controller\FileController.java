package com.huabo.file.controller;

import com.huabo.file.db.service.TblAttachmentService;
import com.huabo.file.util.JsonBean;
import com.huabo.file.vo.FileUploadRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/file")
@Api(value = "文件管理接口", tags = {"文件管理接口"})
public class FileController {
    @Resource
    TblAttachmentService tblAttachmentService;


    /**
     * 文件上传
     */
    @PostMapping("/upload")
    @ApiOperation("多文件上传接口（返回多个文件信息）")
    public JsonBean<List<FileUploadRes>> fileUpload(@ApiParam(name = "file", value = "文件上传流实体", required = true) MultipartFile[] file) {
        return JsonBean.success(tblAttachmentService.fileUpload(file,false));
    }

    /**
     * 文件上传（加密）
     */
    @PostMapping("/upload/encrypt")
    @ApiOperation("(加密)多文件上传接口（返回多个文件信息）")
    public JsonBean<List<FileUploadRes>> fileUploadEncrypt(@ApiParam(name = "file", value = "文件上传流实体", required = true) MultipartFile[] file) {
        return JsonBean.success(tblAttachmentService.fileUpload(file,true));
    }

    /**
     * 文件下载
     */
    @GetMapping(value = "/download")
    @ApiOperation("文件下载接口")
    public void fileDownLoad(HttpServletResponse response,
                             @ApiParam(name = "fileId", value = "文件ID", required = true) @RequestParam("fileId") String fileId) {
        tblAttachmentService.fileDownLoad(response, fileId,false);
    }

    /**
     * 文件下载（解密）
     */
    @GetMapping(value = "/download/decrypt")
    @ApiOperation("(解密)文件下载接口")
    public void fileDownLoadDecrypt(HttpServletResponse response,
                             @ApiParam(name = "fileId", value = "文件ID", required = true) @RequestParam("fileId") String fileId) {
        tblAttachmentService.fileDownLoad(response, fileId,true);
    }

    /**
     * 文件删除
     */
    @GetMapping(value = "/delete")
    @ApiOperation("文件删除接口")
    public JsonBean<Long> fileRemove(@ApiParam(name = "fileId", value = "文件ID", required = true) @RequestParam("fileId") long fileId) {
        tblAttachmentService.removeFile(fileId);
        return JsonBean.success(fileId);
    }

    /**
     * 根据文件id列表查询文件信息
     */
    @PostMapping("/list")
    @ApiOperation("根据文件id列表查询文件信息")
    public JsonBean<List<FileUploadRes>> listFileUpload(@ApiParam(name = "ids", value = "文件id列表", required = true) @RequestBody List<String> ids) {
        return JsonBean.success(tblAttachmentService.listFileUpload(ids));
    }
}
