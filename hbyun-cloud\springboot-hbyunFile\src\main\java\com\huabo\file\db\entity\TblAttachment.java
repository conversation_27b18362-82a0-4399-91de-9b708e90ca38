package com.huabo.file.db.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 *
 */
@Data
@TableName("TBL_ATTACHMENT")
@ApiModel(value="TblAttachment对象")
public class TblAttachment  {


    @ApiModelProperty(value = "主键Id 自增")
    @TableId(value = "ATTID")
    private Long attid;

    @ApiModelProperty(value = "附件名称")
    @TableField("ATTNAME")
    private String attname;

    @ApiModelProperty(value = "附件路径")
    @TableField("ATTPATH")
    private String attpath;

    @ApiModelProperty(value = "附件大小")
    @TableField("ATTSIZE")
    private double attsize;

    @ApiModelProperty(value = "备注")
    @TableField("MEMO")
    private String memo;

    @ApiModelProperty(value = "上传时间")
    @TableField("UPLOADTIME")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date uploadtime;

    @ApiModelProperty(value = "上传人")
    @TableField("UPLOADER")
    private String uploader;

    @ApiModelProperty(value = "是否是python爬取文件 0是")
    @TableField("ISPYTHONFLAG")
    private String ispythonflag;

    @TableField("ISENCRYPTED")
    @ApiModelProperty(value = "是否加密存储，1表示加密，0表示不加密，默认值为0")
    private Boolean isEncrypted = true;
    
    
    @ApiModelProperty(value = "加密地址，用于预览")
    @TableField("JMURL")
    private String jmurl;

}
