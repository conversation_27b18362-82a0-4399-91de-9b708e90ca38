package com.huabo.file.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

@AllArgsConstructor
@Data
public class FileUploadRes {
//    @ApiModelProperty(value = "文件ID")
//    private String fileId;
//    @ApiModelProperty(value = "文件原始名称（上传时的名称）")
//    private String fileName;
//    @ApiModelProperty(value = "文件大小，单位字节")
//    private long fileSize;

    @ApiModelProperty(value = "文件ID")
    private String attid;
    @ApiModelProperty(value = "附件名称")
    private String attname;
    @ApiModelProperty(value = "附件路径")
    private String attpath;
    @ApiModelProperty(value = "附件大小")
    private double attsize;
    @ApiModelProperty(value = "上传人名称")
    private String uploader;
    @ApiModelProperty(value = "上传时间")
    private String uploadTime;
    @ApiModelProperty(value = "预览地址")
    private String previewUrl;
    @ApiModelProperty(value = "是否加密存储，1表示加密，0表示不加密，默认值为1")
    private String isEncrypted = "1";

}
