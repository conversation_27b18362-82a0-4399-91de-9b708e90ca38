#日志配置

spring:
 redis:
  host: *************
  port: 6379
  password: 

logging:
 level:
  com.alibaba.druid: DEBUG
  org:
   springframework: WARN
   spring:
    springboot:
     dao: info

#eureka服务注册中心配置
eureka:
 instance:
  ip-address: **************
  instance-id: hbyunFile-8077
  prefer-ip-address: true
 client:
  register-with-eureka: true
  fetch-registry: true
  serviceUrl:
   defaultZone: ***************************************************/eureka

mybatis-plus:
 configuration:
  log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
 plugins:
  pagination:
   enabled: true
# Swagger配置
swagger:
 enabled: true
 # 是否开启swagger
 # 请求前缀
 pathMapping: /

knife4j:
 enable: true
 production: false


# 文件保存目录
file:
 ca:
  secret: b8e9a1c7d4f265a830e7b1f4d8a9c6e2
 upload:
  path: D:\datafile
 previewUrl: http://************:8012/onlinePreview?url=
 downloadUrl: https://www.wenxindamoxing.com/api/file/file/download/decrypt?fileId=

