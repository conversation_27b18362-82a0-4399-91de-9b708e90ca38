spring:
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    use: oracle
    sqlserver:
      url: ****************************************************
      username: sa
      password: "'YourStrong#Passw0rd123'"
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    dm:
      url: jdbc:dm://60.205.166.36:5236/HBGRCZHEZ?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
      username: HB<PERSON><PERSON>ZHEZ
      password: HBGRCZHEZ
      driverClassName: dm.jdbc.driver.DmDriver
    #Oracle数据库配置
    oracle:
      url: *****************************************
      username: HB<PERSON><PERSON><PERSON>HEZ
      password: HBGR<PERSON>ZHEZ
      driverClassName: oracle.jdbc.driver.OracleDriver
    #Mysql数据库配置
    mysql:
      url: ********************************************************************************************************************************************************************************************************************************************************************************************************************
      username: cbim
      password: Cbim!123456
      driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      min-idle: 5
      max-active: 50
      max-wait: 120000
      initialSize: 5
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      testWhileIdle: true
      validationQuery: SELECT 1
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatement: true
      maxPoolPreparedStatementPerConnectionSize: 20
      filters: stat,wall,log4j
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
