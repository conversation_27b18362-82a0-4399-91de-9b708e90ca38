package com.global.treasurer.oracle.mapper;

import com.global.treasurer.oracle.entity.TblCeaConferenceMgtOracle;
import com.global.treasurer.vo.param.TblCeaConferenceMgtQueryParam;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TblCeaConferenceMgtOracleMapper extends Mapper<TblCeaConferenceMgtOracle> {


	List<TblCeaConferenceMgtOracle> getList(@Param("example") TblCeaConferenceMgtQueryParam example);
}