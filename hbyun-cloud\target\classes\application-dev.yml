spring:
 application:
  name: hbyunknow #应用名称
 datasource:
  type: com.alibaba.druid.pool.DruidDataSource
  #Oracle数据库配置
  oracle:
   url: *****************************************
   username: HB<PERSON><PERSON><PERSON>HEZ
   password: H<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Z
   driverClassName: oracle.jdbc.driver.OracleDriver
  #Mysql数据库配置
  mysql:
   url: ******************************************************************************************************************************************
   username: root
   password: HBGRC
   driverClassName: com.mysql.cj.jdbc.Driver
 druid:
  oracle:
   initialSize: 5
   minIdle: 5
   maxActive: 20
   maxWait: 60000
   timeBetweenEvictionRunsMillis: 60000
   minEvictableIdleTimeMillis: 300000
   validationQuery: SELECT 1 FROM DUAL
   testWhileIdle: true
   testOnBorrow: false
   testOnReturn: false
   poolPreparedStatement: true
   maxPoolPreparedStatementPerConnectionSize: 20
   filters: stat,wall,log4j
   connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
  mysql:
   initialSize: 5
   minIdle: 5
   maxActive: 20
   maxWait: 60000
   timeBetweenEvictionRunsMillis: 60000
   minEvictableIdleTimeMillis: 300000
   validationQuery: SELECT 1 FROM DUAL
   testWhileIdle: true
   testOnBorrow: false
   testOnReturn: false
   poolPreparedStatement: true
   maxPoolPreparedStatementPerConnectionSize: 20
   filters: stat,wall,log4j
   connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
 activiti:
  check-process-definitions: false
  database-schema-update: false
#通用mapper配置
mapper:
 mappers: tk.mybatis.mapper.common.Mapper
 not-empty: false
 identity: oracle
 before: true

mysqlmapper:
 mappers: tk.mybatis.mapper.common.MySqlMapper
 not-empty: false
 identity: mysql
 before: true

#日志配置
logging:
 level:
  org:
   springframework: WARN
   spring:
    springboot:
     dao: info
  com:
   dc:
    pcasweb:
     mapper: debug
 file: logs/spring-boot-logging.log
 
#eureka服务注册中心配置
eureka:
 instance:
  ip-address: **************
  instance-id: hbyunSystemSetting-8763
  prefer-ip-address: true
  hostname: hbyun-systemSetting-service
 client:
  register-with-eureka: false
  fetch-registry: false
  serviceUrl:
   defaultZone: ***************************************************/eureka
security:
  basic:
    enabled: false

#redis配置
redisAddressIp: *************
redisAddressPort: 6379
redisPwd: 
redisMaxTotal: 512
redisMaxIdle: 100
redisMaxWait: 10000
redisTimeOut: 1000
redisTextonBorrow: true

acurl : http://**************:8003
formurl : http://**************:8001
activitiModelerUrl : http://**************:8137
getOrgChildrenUrl : http://**************:8379
insertOrgOneUrl : http://**************:8379
redisurl : http://**************:8379

mybatis:
 configuration:
  map-underscore-to-camel-case: true
# Swagger配置
swagger:
 enabled: true
 # 是否开启swagger
 # 请求前缀
 pathMapping:
application:
 #文件上传
 file-upload:
  base-dir: D:\nginx-1.20.2\html\hbfk
  #base-url: http://*************/hbfk
  base-url: http://*************/hbfk
 #图片上传前缀域名
 file-url: http://cn-gz-txy.starryfrp.com:64108
 #上帝视角用户名称
 admin-name: 张孝昆


