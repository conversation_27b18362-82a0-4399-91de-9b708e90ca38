﻿#系统上传的文件
uploadfilepath=/opt/ftp/uploadfile/
#系统通过发送自动生成的文件
sengfilepath=/opt/ftp/sengfile/

#流程图相关的xml文件
xmlfilepath=/opt/ftp/xmlfile/

#模板相关的文件
templatefilepath=/opt/ftp/templatefile/

#合同相关的文件
constractfilePath=/opt/ftp/constract/

systemImgFilePath=/opt/ftp/formimg/

pythonFlfgPaht=/opt/ftp/python/flfg/

#openoffice在本地的路径
openOfficePath=C:/Program Files (x86)/OpenOffice 4/
#openofficeurl
openOfficeip=127.0.0.1
#openoffice端口号
openOfficeport=8100

#ftp相关的配置
ftpip=***************
username=ftpuser
password=ftpuser
port=21

previewurl=http://***************:8012/onlinePreview
#ftpip=***********
#username=ftpadmin
#password=123456
#port=21

#cmd 命令 cd 进入安装目录 C:\Program Files (x86)\OpenOffice 4\program
#openoffice window启动命令，启动成功后 会看到soffice.exe soffice.bin 进程
#soffice.exe -headless -accept="socket,host=127.0.0.1,port=8100;urp;" -nofirststartwizard

pythonFlfgPath=/opt/ftp/python/flfg/


ssqHost=https://api.bestsign.info
clientId=1631433333011256371
clientSecret=7f96196018c64889b05e5a519b4ccd3e
privateKey=MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCK12GO4+couoqeWteM6tFC7IMwaf4xwsArY5Kc+V3WfOKec2VNbEdTXCbFvPGVFbnCJ0cjyN0R1RKEIXVuQLuT97/+dW/2TXQjDZ5uxSfxg+UD4aIa41uXB/zHuiL9mMF0TnmRsH/Mt2BwGQd/sdoEY9Ww8bDgsm6rD5dL+HbcgD5LmN0N/gA2UpT1HbAcNGi7sOLWXnx8Nr6CkQzdlVBdTZUISBVVyd/xJC/nOT3SVEDJUh7oweVzyppiQPtDQl+Y0rXzOBirTup3VMIgMFzRozIZZvNHqN00yBAarGVIMUrwqouQsGC7RNYYPp7dCQiSCWq78+cjj1qC+jvpkDk9AgMBAAECggEAGxiJi49ir4ooP1tPMcsp57uspqgo2aE+S7l1BXmNHKzmtTGkFYYKLXSQhIqpQwUoaYGs2oj6y94fbPXyqv+rG+d/6rMUlKbtAWJJBykm8HVjHmzMbB7u4vzGElx7M666Im6mPw6JAnTPN1FLK1qUR8MMEyo38MIh4vR5/HOTDtkWt1YCY3MOc1Dk4L4i5LrZRxRq+ogU1qdboqXb25SeA5VdP1vz1wFcd102c+blkTeCY0ABnJb2nMWWwDew3aKZ/a6Hfut9BNwbmIvtPt16HBHL8X9YiLAYobbB5N/EmjcLIdO/FnT8dDmP+YNmnwMV8mLkDU6BteQ4rKRlidRUKQKBgQC/ESALeNxOF8QW2NmhTLHdEHv+4w5U/mj3DAIuBThZbQEVbt1z50DEPsOZoK8afBbglhMUv7g6ao08a3BzmDbHYs5FDq/rQyKlvM/cVl471Qn6flgp8645LFjtNbKKP6GlHbdcRi803v71UTDCO33/L3cs1QL5QfPcHzDqM8oxHwKBgQC6Bp3u5XIKYYCEkA5X2a7cLFBFSZlskhiDAm//CqIG07qbm1Bi1/5zT4vnjodi8b1dGkJD1VxjgWjZvqRQUYGIwNXcbX/P4wvfwbG8ntARV/OlyTAhwaby8Do5CgL+QuTOrbQJf1gpO/KHZ/4L+tGXtPBYuBfmOjKuNbqfxDo+IwKBgGaVmEHwk0AujG7D4R00Y27QsfJpEEHcEplJzkXNMbL5zpImYyoeqGx1UmnClyE+LL5KdVY3lX9vxAMDoG2/9PFagGsddqB2/DxGQsgHazLusBvZCBcGKa2yvNAne4O0pGJVsSQO3ruhWvx1qhsu+/guEOt2NtGDcp3l9UpZoh57AoGAWklb3db+wosQFUbVr0230KSxUO+nx0ohG3TTZrWCbk3VBDEdktvCY7T38ir7CsrJXaahAstLTABDI2uj1Fe6xuTQ52s93WgVG0SnPyTVV5WYa555/eg/Xym5GHYDfeR5qNYly3ZDE8BEtfODGc1ibH8lJ+oxr0wPzP3G1kQnAIECgYAxTRw8YXLdAyDZCsmNJv/FwIyYjRm6yGcB00gbjf43XaDWl/+ZSSxCP8JE/Lfx1d0w5e7VABQxNvytobO/pS3euYdhwbh8kRU95Jyrdvs2xVmzmFJih3Oy1Z+6Zhl2YMLvcvrOuXcHKon867AuhAA/R7FPArZpm/Sub/LiNX0E2Q==