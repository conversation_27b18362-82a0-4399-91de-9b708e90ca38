﻿﻿#人事系统访问IP：***************
#开放平台信息
#开发者账号：
dev_account=1591B4D705C9EA6ED2FD6E402C67F076950D040DD1F51A4DBDF457650D39757F38C04FC5BF47E07C
#开发者密码：
dev_password=8f1489e920865317
#--  8c40fb52062247ab
#开发者IP限制
#开发者IP：***************
tokenUrl=http://***************:80/api/auth.do?method=token
 
orgUrl=api/org.do?method=findDepts

JobUrl=api/org.do?method=findAllJob

JobGardeUrl=api/ehrOrg.do?method=findJobGrades
 
personUrl=api/org.do?method=findPersonsByPager

findPerson=api/ehrOrg.do?method=findPerson


serverUrl=http://***************:80/

phone=***********

#-------以下是单点登录及消息抄送 消息待办

#单点登录验证ticket 获取OA用户名地址
#测试环境
OaLoginUrl=http://***************:8080/seeyon/thirdpartyController.do?ticket=
#OA测试地址
oaUrl=http://***************:8080/
#测试环境应用编号
oaCode=3008
#系统登录地址 给待办发送的地址链接 测试环境
loginUrl=http://***************/setting/

#正式环境
#OaLoginUrl=http://***************:8080/seeyon/thirdpartyController.do?ticket=
#OA正式地址
#oaUrl=http://***************:8080/
#正式环境应用编号
#oaCode=3008
#系统登录地址 给待办发送的地址链接 测试环境
#loginUrl=http://192.168.100.131:88/setting/
#获取oa验证token地址
getToken=/seeyon/rest/token/
#rest用户名 
restuname=RestForSzfk
#rest密码
restpassword=ae54616e-60ae-4637-8664-3609896c2142
#消息接口调用单条
singleMessage=/seeyon/rest/thirdpartyMessage/receive/singleMessage
#批量推送地址
messageList=/seeyon/rest/thirdpartyMessage/receive/messageList
#待办调用接口
sendToDo=/seeyon/rest/thirdpartyPending/receive/pendings
#处理待办接口
sendToDoDone=/seeyon/rest/thirdpartyPending/updatePendingState
#获取用户所有公文列表/协同列表
getOAlist=/seeyon/rest/eu/quote/docs
#获取用户待办列表/已办列表
getOAYblist=/seeyon/rest/eu/affairs
#oa获取待办已办信息
oaFlowInfoList=seeyon/rest/eu/affairs
