#工作流对接：
#
IP=http://**************:89
#参数是module 和 file流程文件
#流程发布的地址：http://*************:8081/workFlow/process/deploy
fburl=http://**************:89/workFlow/process/deploy

#启动流程：
#http://*************:8081/workFlow/process/start/{key}
#参数：text  传流程变量数据          key：流程图的process的id
qdurl=http://**************:89/workFlow/process/start/


#查询代办任务    
#参数  userId:启动流程用户的id    groupId:流程审批组的id    start:分页起始数据       pageSize:分页显示的条数
rwurl=http://**************:89/workFlow/process/todolist


#http://*************:8081/workFlow/process//complete/{taskId}   流程办理
#taskId:代办查询返回的任务id
blurl=http://**************:89/workFlow/process/complete/


#http://*************:8081/workFlow/process/runtime/picture/{processInstanceId}  流程监控  
#processInstanceId：代办查询返回的processInstanceId值
jkurl=http://**************:89/workFlow/process/runtime/picture/


#http://*************:8081/workFlow/process/runtime/getButtonsForTransition/{taskId} 流程走向           
#taskId:代办查询返回的任务id
lczxurl=http://**************:89/workFlow/process/runtime/getButtonsForTransition/


#历史流程实例
#http://*************:8081/workFlow/process/history/queryprocessInstance
lslcslurl=http://**************:89/workFlow/process/history/queryprocessInstance



#流程定义 
#http:/*************:8081/workFlow/process/queryProcessDefinition   String processDefinitionKey,String processDefinitionId
lcdyurl=http://**************:89/workFlow/process/queryProcessDefinition


#流程实例
#http://*************:8081/workFlow/process/runtime/queryProcessInstance
lcslurl=http://**************:89/workFlow/process/runtime/queryProcessInstance



#删除流程定义  deploymentId  cascade true是强制删除
sclcdy=http://**************:89/workFlow/process/deleteProcessDefinition


#/runtime/deleteProcessInstance   processInstanceId deleteReason
sclcsl=http://**************:89/workFlow/process/runtime/deleteProcessInstance


sxjm_suburl=http://**************:8002/process/sxmt/tjsp_sjtbstring


sxjm_recordurl=http://**************:8002/process/sxmt/cxalls

sxjm_entityurl=http://**************:8002/process/sxmt/from

sxjm_blurl=http://**************:8002/process/sxmt/blprocessstring

sxswglj_getkey=http://**************:8080/sharedata/system/appinterface/getTime

acurl=http://**************:8003

formurl=http://**************:8001

redisurl=http://**************:8379