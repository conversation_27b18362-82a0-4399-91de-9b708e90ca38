# 华宝云平台功能列表文档

## 项目概述

华宝云平台是一个基于Spring Boot + Spring Cloud微服务架构的企业级管理平台，采用Eureka作为服务注册中心，支持Oracle和MySQL双数据库，集成了审计、合同、法务、财务、监控等多个业务模块。

## 技术架构

### 核心框架
- **Spring Boot 2.5.6** - 微服务基础框架
- **Spring Cloud 2020.0.4** - 微服务治理
- **Netflix Eureka** - 服务注册与发现
- **MyBatis Plus 3.4.1** - ORM框架
- **Druid 1.0.27** - 数据库连接池

### 数据库支持
- **Oracle** - 主要数据库
- **MySQL** - 备用数据库
- **Redis** - 缓存数据库

### 其他技术组件
- **Swagger 2.9.2** - API文档
- **Knife4j 2.0.2** - API文档增强
- **PageHelper 1.2.3** - 分页插件
- **EasyPOI 4.1.0** - Excel处理
- **EasyExcel 2.2.5** - Excel处理
- **Activiti 7.1.0.M1** - 工作流引擎
- **Elasticsearch** - 搜索引擎
- **Kettle** - ETL数据处理

## 微服务模块功能列表

### 1. 服务注册中心
**模块名称**: springcloud-hbfkEureka
**功能描述**: 
- 微服务注册与发现
- 服务健康检查
- 负载均衡

### 2. 网关服务
**模块名称**: springboot-hbfkGatewayService
**功能描述**:
- API网关路由
- 统一鉴权
- 请求转发
- 限流熔断

### 3. 审计管理模块

#### 3.1 智能审计微服务
**模块名称**: springboot-hbyunAudit
**功能描述**:
- 审计项目管理
- 审计计划制定
- 审计流程控制
- 审计报告生成

#### 3.2 央企审计微服务
**模块名称**: springboot-hbyunOilAudit
**核心功能**:
- **项目管理**
  - 项目启动管理
  - 审计项目表管理
  - 工程项目总结管理
- **审计实施**
  - 审计工作记录
  - 审计方法维护
  - 审计承诺书管理
  - 审计通知管理
- **质量控制**
  - 疑点管理
  - 缺陷管理
  - 风险发现
  - 整改方案
- **底稿管理**
  - 我的底稿
  - 审计建议书
  - 审计指引模板库
  - 拟实施审计指引
- **基础配置**
  - 管理制度
  - 项目资料
  - 审计模板
  - 审计经验库

#### 3.3 央企内审综合管理微服务
**模块名称**: springboot-hbyunCentralEnterprisesAudit
**功能描述**:
- 内部审计管理
- 审计监督
- 审计质量评估
- 审计人员管理

### 4. 合同管理模块

#### 4.1 合同管理微服务
**模块名称**: springboot-hbyunContract
**核心功能**:
- **合同项目管理**
  - 合同项目新增/修改
  - 合同项目查询
  - 合同状态管理
- **发票管理**
  - 发票信息维护
  - 发票状态跟踪
- **合同交易管理**
  - 交易记录管理
  - 交易状态跟踪
- **用友系统集成**
  - 企业信息查询
  - 外部接口对接

### 5. 法务管理模块

#### 5.1 法务管理微服务
**模块名称**: springboot-hbyunLegal
**核心功能**:
- **律师事务所管理**
  - 执业申请管理
  - 人员台账管理
  - 执业活动记录
- **日常管理**
  - 会议管理
  - 其他文件报文管理
- **法务审核**
  - 制度审核
  - 经营事项审核
  - 审核台账管理
- **考试系统**
  - 题目管理
  - 试卷管理
  - 考试结果统计
- **组织架构**
  - 部门管理
  - 人员管理
  - 权限管理

### 6. 财务管理模块

#### 6.1 全球司库微服务
**模块名称**: springboot-hbyunGlobalTreasurer
**功能描述**:
- 全球资金管理
- 司库业务处理
- 资金调度
- 风险控制

#### 6.2 财务共享微服务
**模块名称**: springboothbyunFinancialSharing
**功能描述**:
- 财务共享服务
- 财务数据处理
- 财务报表生成
- 成本核算

#### 6.3 管理会计微服务
**模块名称**: springboothbyunManagementAccountant
**功能描述**:
- 管理会计分析
- 成本管理
- 预算管理
- 绩效评估

#### 6.4 财务数据微服务
**模块名称**: hbyunFinancialData
**功能描述**:
- 财务数据采集
- 数据清洗
- 数据分析
- 报表生成

### 7. 合规管理模块

#### 7.1 合规管理微服务
**模块名称**: springboot-hbyunComplie
**功能描述**:
- 合规检查
- 合规报告
- 合规培训
- 合规监控

#### 7.2 合规扩展管理微服务
**模块名称**: springboot-hbyunComplianceExt
**功能描述**:
- 合规扩展功能
- 组织架构管理
- 树形结构管理
- 公司信息管理

#### 7.3 穿透监管微服务
**模块名称**: springboothbyunRegulatoryPenetration
**功能描述**:
- 穿透式监管
- 风险识别
- 监管报告
- 合规评估

### 8. 监控管理模块

#### 8.1 智能监控微服务
**模块名称**: springboot-hbyuncybermonitor
**核心功能**:
- **模型监控**
  - 模型预警管理
  - 监控方案管理
  - 监控模型管理
  - 预警结果处理
- **监控执行**
  - 指标执行管理
  - 执行结果跟踪
- **风险管理**
  - 风险识别
  - 风险评估
  - 风险预警
- **账簿管理**
  - 账簿信息维护
  - 账簿层级管理

#### 8.2 监控微服务
**模块名称**: springboot-hbyunMonitor
**功能描述**:
- 系统监控
- 性能监控
- 日志监控
- 告警管理

### 9. 风险控制模块

#### 9.1 风险控制微服务
**模块名称**: hbyunRiskControl
**功能描述**:
- 风险识别
- 风险评估
- 风险控制
- 风险报告

### 10. 系统管理模块

#### 10.1 系统设置微服务
**模块名称**: springboot-hbfkSystemSetupModule
**功能描述**:
- 系统参数配置
- 用户权限管理
- 组织架构管理
- 数据字典维护

#### 10.2 知识共享微服务
**模块名称**: springboot-hbfkKnowledgeSharing
**功能描述**:
- 知识库管理
- 文档共享
- 经验分享
- 学习培训

#### 10.3 系统模块
**模块名称**: hbyun-systemModule
**功能描述**:
- 基础系统功能
- 公共组件
- 工具类库

### 11. 数据处理模块

#### 11.1 ETL数据处理微服务
**模块名称**: springboot-hbyunEtl
**功能描述**:
- 数据抽取(Extract)
- 数据转换(Transform)
- 数据加载(Load)
- 与Kettle集成
- 数据清洗管理

#### 11.2 Elasticsearch微服务
**模块名称**: springboot-hbyunEs
**功能描述**:
- 全文检索
- 数据索引
- 搜索分析
- 日志分析

### 12. 文件管理模块

#### 12.1 文件管理微服务
**模块名称**: springboot-hbyunFile
**功能描述**:
- 文件上传下载
- 文件存储管理
- 文件权限控制
- 文件版本管理

### 13. 日志管理模块

#### 13.1 日志管理微服务
**模块名称**: springboot-hbyunLog
**功能描述**:
- 系统日志记录
- 操作日志跟踪
- 日志查询分析
- 日志归档管理

#### 13.2 日志管理SDK
**模块名称**: springboot-sdk-log
**功能描述**:
- 业务日志管理SDK
- 操作日志注解
- 日志拦截器
- SQL日志记录

### 14. 工具模块

#### 14.1 工具类微服务
**模块名称**: springboot-hbyunutils
**功能描述**:
- 公共工具类
- 通用组件
- 加密解密
- 数据转换
- 文件处理
- 时间处理
- 用户信息处理

## 数据库设计

### 主要数据表
- **组织架构表** (TBL_ORGANIZATION) - 组织机构信息
- **人员信息表** (TBL_STAFF) - 员工基础信息
- **审计项目表** (TBL_YQNS_SJXMB) - 审计项目管理
- **合同项目表** (TBL_CONTRACT_PROJECT) - 合同项目信息
- **法务管理表** (TBL_FWGL_*) - 法务相关业务表
- **监控模型表** (MONITOR_*) - 监控相关业务表

### 数据库特性
- 支持Oracle和MySQL双数据库
- 使用Druid连接池
- 集成MyBatis Plus ORM框架
- 支持分页查询
- 支持事务管理

## 系统特色功能

### 1. 多租户支持
- 支持多组织架构
- 数据隔离
- 权限分级

### 2. 工作流集成
- 集成Activiti工作流引擎
- 支持审批流程
- 流程监控

### 3. 文档管理
- 支持多种文件格式
- 在线预览
- 版本控制

### 4. 数据分析
- 集成Elasticsearch
- 支持复杂查询
- 数据可视化

### 5. 系统集成
- 支持外部系统对接
- RESTful API
- 统一认证

## 部署架构

### 微服务部署
- 每个模块独立部署
- 支持容器化部署
- 支持集群部署

### 数据库部署
- 支持读写分离
- 支持主从复制
- 支持分库分表

### 缓存部署
- Redis集群
- 分布式缓存
- 缓存预热

## 总结

华宝云平台是一个功能完整、架构先进的企业级管理平台，涵盖了企业运营的各个方面，包括审计、合同、法务、财务、合规、监控等核心业务模块。平台采用微服务架构，具有良好的扩展性和维护性，能够满足大型企业的复杂业务需求。
